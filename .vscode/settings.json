{"files.exclude": {"*.iml": true, "**/.idea": true}, "editor.suggestSelection": "first", "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue", "java.jdt.ls.vmargs": "--add-modules=ALL-SYSTEM --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED -server -ea -Xmx4G -Xms1024m", "git.path": "/usr/bin", "git.confirmSync": false, "redhat.telemetry.enabled": false, "editor.fontFamily": "'JetBrains Mono','Fira Code','Intel One Mono','Source Code Pro',Monaco", "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": "active", "git.enableSmartCommit": true, "explorer.confirmDelete": false, "no_proxy": "192.168.*,10.*,172.16.*,127.0.0.1,localhost,*.local,timestamp.apple.com,*.fxiaoke.com,*.firstshare.cn,*.foneshare.cn,*.ceshi112.com,172.17.*,172.18.*,172.19.*,172.28.*,172.29.*,172.31.*", "javascript.updateImportsOnFileMove.enabled": "always", "rest-client.excludeHostsForProxy": ["localhost", "*********/8", "10.0.0.0/8", "***********/16", "fspage.com", "firstshare.cn", "foneshare.cn", "fxiaoke.com"], "http.proxyStrictSSL": false, "editor.suggestFontSize": 12, "editor.fontLigatures": true, "gitlens.blame.ignoreWhitespace": true, "scm.diffDecorationsIgnoreTrimWhitespace": "true", "editor.tabSize": 2, "java.codeGeneration.toString.codeStyle": "STRING_BUILDER_CHAINED", "editor.linkedEditing": true, "[java]": {"editor.defaultFormatter": "redhat.java"}, "editor.wordWrapColumn": 160, "vscodeP3C.runWorkspaceOnActive": false, "editor.inlineSuggest.enabled": true, "github.copilot.enable": {"*": true, "yaml": true, "plaintext": true, "markdown": true}, "files.associations": {"*.jetx": "html"}, "rsp-ui.enableStartServerOnActivation": [{"id": "redhat.vscode-community-server-connector", "name": "Community Server Connector", "startOnActivation": true}], "better-comments.highlightPlainText": true, "editor.minimap.enabled": false, "sonarlint.ls.vmargs": "-Xmx4g -Djava.net.preferIPv4Stack=true -server", "java.debug.settings.vmArgs": "-Xmx4G -ea -Djava.net.preferIPv4Stack=true -server", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnType": true, "terminal.integrated.fontFamily": "JetBrains Mono", "boot-java.rewrite.reconcile": true, "boot-java.change-detection.on": true, "sonarlint.ls.javaHome": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home", "rsp-ui.rsp.java.home": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk/java/11", "java.import.gradle.home": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk/gradle/latest", "java.import.gradle.java.home": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home", "java.import.gradle.user.home": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home", "workbench.preferredDarkColorTheme": "Visual Studio Dark", "editor.wordWrap": "wordWrapColumn", "pgFormatter.wrapLimit": 160, "CodeGPT.query.language": "Chinese", "CodeGPT.maxTokens": 4000, "[python]": {"editor.formatOnType": true}, "sonarlint.connectedMode.connections.sonarqube": [{"serverUrl": "https://oss.firstshare.cn/sonarqube", "connectionId": "https-oss-firstshare-cn-sonarqube"}], "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabCompletion": "on", "AREPL.pythonPath": "/opt/homebrew/bin/python3", "python.defaultInterpreterPath": "/opt/homebrew/bin/python3", "sonarlint.rules": {"java:S108": {"level": "off"}, "java:S1602": {"level": "off"}, "java:S1168": {"level": "off"}}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.unicodeHighlight.allowedLocales": {"zh-hans": true, "zh-hant": true}, "java.format.settings.url": "https://git.firstshare.cn/JavaCommon/code-style/-/raw/main/sharecrm-java-style.xml", "java.format.settings.profile": "fxiaoke-paas-style", "debug.console.fontSize": 10, "github.copilot-labs.advanced": {}, "yaml.customTags": ["!And", "!And sequence", "!If", "!If sequence", "!Not", "!Not sequence", "!Equals", "!Equals sequence", "!Or", "!Or sequence", "!FindInMap", "!FindInMap sequence", "!Base64", "!Join", "!Join sequence", "!Cidr", "!Ref", "!Sub", "!Sub sequence", "!GetAtt", "!GetAZs", "!ImportValue", "!ImportValue sequence", "!Select", "!Select sequence", "!Split", "!Split sequence"], "aws.telemetry": false, "boot-java.common.properties-metadata": "", "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "diffEditor.ignoreTrimWhitespace": false, "explorer.confirmDragAndDrop": false, "CodeGPT.model": "gpt-o1", "java.code.generators.onlyPrimitiveForToString": true, "go.toolsManagement.autoUpdate": true, "gitlens.views.repositories.includeWorkingTree": true, "editor.fontVariations": false, "vs-kubernetes": {"vscode-kubernetes.helm-path.mac": "/Users/<USER>/.vs-kubernetes/tools/helm/darwin-arm64/helm", "vscode-kubernetes.kubectl-path.mac": "/Users/<USER>/.vs-kubernetes/tools/kubectl/kubectl", "vscode-kubernetes.minikube-path.mac": "/Users/<USER>/.vs-kubernetes/tools/minikube/darwin-arm64/minikube"}, "workbench.editor.empty.hint": "hidden", "terminal.integrated.tabStopWidth": 2, "prettier.printWidth": 160, "yaml.format.printWidth": 160, "xml.format.maxLineWidth": 160, "cursor.cpp.disabledLanguages": ["scminput", "csv"], "java.compile.nullAnalysis.mode": "automatic", "java.compile.nullAnalysis.nonnull": ["org.checkerframework.checker.nullness.qual.NonNull", "javax.annotation.Nonnull", "org.eclipse.jdt.annotation.NonNull", "org.springframework.lang.NonNull"], "java.compile.nullAnalysis.nullable": ["org.checkerframework.checker.nullness.qual.Nullable", "javax.annotation.Nullable", "org.eclipse.jdt.annotation.Nullable", "org.springframework.lang.Nullable"], "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "editor.rulers": [{"column": 80, "color": "#00FF0010"}, {"column": 100, "color": "#BDB76B15"}, {"column": 120, "color": "#FA807219"}], "editor.unicodeHighlight.includeComments": true, "emmet.variables": {"lang": "zh"}, "workbench.colorCustomizations": {"[Default Dark Modern]": {"tab.activeBorderTop": "#00FF00", "tab.unfocusedActiveBorderTop": "#00FF0088", "textCodeBlock.background": "#00000055"}, "editor.wordHighlightStrongBorder": "#FF6347", "editor.wordHighlightBorder": "#FFD700", "editor.selectionHighlightBorder": "#A9A9A9", "debugConsole.warningForeground": "#f06a6a", "debugConsole.errorForeground": "#eb5a5a", "debugConsole.infoForeground": "#96b0f9", "debugConsole.sourceForeground": "#0c0"}, "workbench.editor.revealIfOpen": true, "workbench.tree.indent": 20, "cSpell.diagnosticLevel": "Hint", "trailing-spaces.includeEmptyLines": false, "terminal.integrated.tabs.hideCondition": "never", "terminal.integrated.enablePersistentSessions": false, "java.debug.settings.hotCodeReplace": "auto", "java.sources.organizeImports.staticStarThreshold": 10, "java.configuration.runtimes": [{"name": "JavaSE-1.8", "path": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home"}], "terminal.integrated.profiles.osx": {"zsh": {"path": "zsh", "env": {"JAVA_HOME": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home", "ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk"}}, "JavaSE-1.8 LTS": {"overrideName": true, "env": {"ZDOTDIR": "/Users/<USER>/Library/Application Support/Cursor/User/globalStorage/pleiades.java-extension-pack-jdk", "JAVA_HOME": "/Users/<USER>/Library/Java/JavaVirtualMachines/azul-1.8.0_422/Contents/Home"}, "path": "zsh"}}, "terminal.integrated.defaultProfile.osx": "JavaSE-21 LTS", "maven.terminal.customEnv": [{"environmentVariable": "JAVA_HOME", "value": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home"}], "plantuml.java": "/Library/Java/JavaVirtualMachines/jdk1.8.0_361.jdk/Contents/Home/bin/java", "maven.executable.path": "/Users/<USER>/Coder/Applications/maven/apache-maven-3.6.3/bin/mvn", "trailing-spaces.backgroundColor": "rgba(255,0,0,0.1)", "cursor.chat.defaultNoContext": true, "java.signatureHelp.description.enabled": true, "java.edit.smartSemicolonDetection.enabled": true, "java.implementationsCodeLens.enabled": true, "java.symbols.includeSourceMethodDeclarations": true, "java.typeHierarchy.lazyLoad": true, "diffEditor.renderSideBySide": true, "sonarlint.pathToNodeExecutable": "/Users/<USER>/.nvm/versions/node/v20.5.1/bin/node", "[yaml]": {"editor.insertSpaces": true, "editor.tabSize": 2, "prettier.printWidth": 160, "prettier.tabWidth": 2, "editor.autoIndent": "advanced", "diffEditor.ignoreTrimWhitespace": false, "editor.defaultFormatter": "kennylong.kubernetes-yaml-formatter", "editor.formatOnSave": true, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}}, "accessibility.signals.taskCompleted": {"sound": "off"}, "accessibility.signals.notebookCellCompleted": {"sound": "off"}, "fittencode.languagePreference.displayPreference": "zh-cn", "fittencode.languagePreference.commentPreference": "zh-cn", "java.dependency.packagePresentation": "hierarchical", "java.import.exclusions": ["**.joda.**", "**/node_modules/**", "**/.metadata/**", "**/archetype-resources/**", "**/META-INF/maven/**"], "java.maven.downloadSources": true, "java.maxConcurrentBuilds": 2, "java.completion.chain.enabled": true, "java.completion.maxResults": 100, "java.codeGeneration.generateComments": true, "java.codeGeneration.toString.skipNullValues": true, "java.eclipse.downloadSources": true, "java.configuration.detectJdksAtStart": true, "java.jdt.ls.androidSupport.enabled": "off", "java.cleanup.actions": ["renameFileToType", "organizeImports", "addOverride"], "java.completion.filteredTypes": [".*shaded.*", "java.awt.*", "com.sun.*", "sun.*", "jdk.*", "org.graalvm.*", "io.micrometer.shaded.*"], "cSpell.userWords": ["Authing", "<PERSON><PERSON>", "clickhouse", "dryrun", "facishare", "<PERSON><PERSON><PERSON>", "firstshare", "foneshare", "fspage", "fstest", "fxiaoke", "janino", "jdbc", "jedis", "newbeefly", "mapstruct", "mybatis", "<PERSON><PERSON>", "<PERSON><PERSON>", "rocketmq", "sharecrm", "spotbugs", "testcontainers"], "java.completion.importOrder": ["com", "jakarta", "java", "javax", "lombok", "org", "#", ""], "cursor.aipreview.enabled": true, "cursor.chat.showSuggestedFiles": true, "cursor.chat.smoothStreaming": true, "ai-commit.EMOJI_ENABLED": false, "ai-commit.OPENAI_MODEL": "GPT-4o", "workbench.colorTheme": "Quiet Light", "java.saveActions.organizeImports": true, "java.project.importOnFirstTimeStartup": "interactive", "javaTests.template.junitDefaultVersion": "5", "java.project.resourceFilters": ["node_modules", "\\.git", "\\.vscode", "\\.idea", "\\.settings", "logs"], "java.completion.guessMethodArguments": "insertBestGuessedArguments", "java.codeGeneration.hashCodeEquals.useInstanceof": true, "java.referencesCodeLens.enabled": true, "java.configuration.updateBuildConfiguration": "automatic", "debug.console.wordWrap": false, "gitlens.ai.model": "openai:gpt-4o", "gitlens.ai.generateCommitMessage.customInstructions": "# Git Commit Message Generation Expert Prompt  You are a Git commit message generation expert. Generate a conventional commit message based on the following changes.  ## Input Format Please provide: 1. List of modified files (git status output) 2. Code changes (git diff output) 3. Context/purpose of these changes (optional)  ## Output Requirements Generated commit message must: 1. Follow conventional commit format: <type>(<scope>): <description> 2. Use one of these types:    - feat: new feature    - fix: bug fix    - docs: documentation changes    - style: code style/formatting    - refactor: code refactoring    - perf: performance improvements    - test: adding/updating tests    - build: build system or dependencies    - ci: CI configuration changes    - chore: other changes  3. Scope guidelines:    - Use affected module/component name    - Keep it short and meaningful    - Omit if changes affect multiple areas  4. Description must:    - Use imperative present tense    - Not exceed 50 characters    - Start with lowercase    - No period at end    - Be clear and descriptive  5. Body (if needed):    - Explain WHY not HOW    - Wrap at 72 characters    - Use bullet points for multiple points    - Separate from subject with blank line  6. Breaking changes (if any):    - Start with BREAKING CHANGE:    - Detail what breaks and migration steps  ## Writing Guidelines 1. Be specific but concise 2. Focus on business value over technical details 3. Use active voice 4. Consider commit message as part of documentation 5. Make it useful for future debugging 6. Think about readability in git log  Based on these requirements, please generate a high-quality commit message that clearly communicates the changes and their purpose. do not quote the generated content in three slash.", "debug.console.fontFamily": "'<PERSON><PERSON><PERSON>'", "sonarlint.focusOnNewCode": true, "cursor.general.gitGraphIndexing": "enabled", "workbench.editor.enablePreview": false, "cursor.cmdk.useThemedDiffBackground": true, "editor.semanticTokenColorCustomizations": {}, "java.jdt.ls.javac.enabled": "off", "editor.language.brackets": [], "testing.alwaysRevealTestOnStateChange": true, "testing.openTesting": "openExplorerOnTestStart", "maven.terminal.useJavaHome": true, "java.configuration.maven.globalSettings": "/Users/<USER>/Coder/Applications/maven/apache-maven-3.6.3/rep/settings.xml", "java.configuration.maven.userSettings": "/Users/<USER>/Coder/Applications/maven/apache-maven-3.6.3/rep/settings.xml", "maven.view": "hierarchical", "testing.automaticallyOpenTestResults": "openExplorerOnTestStart"}