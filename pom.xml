<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>com.facishare</groupId>
    <artifactId>fs-ai-detector</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>fs-ai-detector-provider</module>
        <module>fs-ai-detector-api</module>
    </modules>


    <properties>
        <httpclient.version>4.5.2</httpclient.version>
        <appserver-common-tools.version>1.1-SNAPSHOT</appserver-common-tools.version>
        <poi.version>3.14</poi.version>
        <poi-ooxml.version>3.14</poi-ooxml.version>
        <protostuff-api.version>1.4.0</protostuff-api.version>
        <protostuff-core.version>1.4.0</protostuff-core.version>
        <protostuff-runtime.version>1.4.0</protostuff-runtime.version>
        <fs-open-app-center-api.version>1.0.0-SNAPSHOT</fs-open-app-center-api.version>
        <fs-open-common-result.version>0.0.5</fs-open-common-result.version>
        <commons-fileupload.version>1.3.2</commons-fileupload.version>
        <ibss-service-eps-api.version>1.0.7-SNAPSHOT</ibss-service-eps-api.version>
        <fs-common-mds-event.version>1.0.4-SNAPSHOT</fs-common-mds-event.version>
        <qixin.version>0.0.2-SNAPSHOT</qixin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.63.Final</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Apache Commons Collections -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        
        <!-- SLF4J -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.32</version>
        </dependency>
        
        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>5.3.20</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>