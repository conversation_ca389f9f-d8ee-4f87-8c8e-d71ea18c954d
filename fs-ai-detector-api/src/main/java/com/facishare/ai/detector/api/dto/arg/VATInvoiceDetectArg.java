package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:49
 */
@Data
@ToString
public class VATInvoiceDetectArg implements Serializable {

    /**
     *
     * @param imagePath npath
     * @param accuracy 非必填 normal（默认配置）对应普通精度模型，识别速度较快，在四要素的准确率上和 high 模型保持一致
     *                 ，high对应高精度识别模型，相应的时延会增加，因为超时导致失败的情况也会增加（错误码282000）
     * @param type 非必填 进行识别的增值税发票类型，默认为 normal，可缺省
     *               - normal：可识别增值税普票、专票、电子发票
     *               - roll：可识别增值税卷票
     */
    private String tenantAccount;

    private String imagePath;

    private String accuracy;

    private String type;
}
