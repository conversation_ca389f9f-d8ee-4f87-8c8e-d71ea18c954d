package com.facishare.ai.detector.api.dto.api;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ObjectDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface SaveDetectRecord {

    @Data
    @ToString
    class Arg implements Serializable {

        private String modelId;

        private String path;

        private String processPath;

        private List<ObjectDto> objects;

        private JSONObject extraData;
    }


    @Data
    @ToString
    class Result implements Serializable {

        private int code;

        private String message;

        private boolean success;

        private ResultData data;
    }

    @Data
    @ToString
    class ResultData implements Serializable {

        private String id;
    }
}
