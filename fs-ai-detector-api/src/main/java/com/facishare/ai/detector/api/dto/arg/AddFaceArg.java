package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 19-11-8  下午2:19
 */
@Data
@ToString
public class AddFaceArg implements Serializable {

    private Integer tenantId;

    private String tenantAccount;

    private String imagePath;

    private String faceToken;

    private String userId;

    private String groupId;

    /**
     * LIVE:表示生活照 ,IDCARD：表示身份证芯片照,WATERMARK：表示带水印证件照：,CERT：表示证件照片 默认生活照
     */
    private String scene ="LIVE";

}
