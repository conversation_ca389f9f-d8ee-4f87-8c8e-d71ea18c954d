package com.facishare.ai.detector.api.dto.api;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2024/7/12 15:34
 */
public interface BatchUpdateMongoPO {

    @Data
    @ToString
    class Arg implements Serializable{
        private List<UpdateEntity> updateEntities;

        private String classPath;

        private Integer tenantId;
    }


    @Data
    @ToString
     class UpdateEntity implements Serializable{

        private Map<String,Object> conditionMap;

        private Map<String,Object> updateMap;
    }


    @Data
    @ToString
    class Result implements Serializable{

        private int successCount;

        private List<UpdateEntity> failEntities;
    }
}
