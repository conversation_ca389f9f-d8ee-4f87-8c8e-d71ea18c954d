package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.AddFaceArg;
import com.facishare.ai.detector.api.dto.arg.FaceComparisionArg;
import com.facishare.ai.detector.api.dto.arg.FaceDetectArg;
import com.facishare.ai.detector.api.dto.result.AddFaceResult;
import com.facishare.ai.detector.api.dto.result.FaceComparisionResult;
import com.facishare.ai.detector.api.dto.result.FaceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;

/**
 * <AUTHOR>
 * @date 19-12-20  下午5:24
 */
public interface FaceDetectService {

    AddFaceResult addFace(AddFaceArg arg) throws AiProviderException;

    FaceComparisionResult faceComparision(FaceComparisionArg arg) throws AiProviderException;

    FaceDetectResult faceDetect(FaceDetectArg arg) throws  AiProviderException;
}
