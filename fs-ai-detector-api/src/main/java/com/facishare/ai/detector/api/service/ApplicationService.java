package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.AddApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetApplicationArg;
import com.facishare.ai.detector.api.dto.result.AddApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetApplicationResult;

public interface ApplicationService {

    AddApplicationResult addApplication(AddApplicationArg arg);

    GetApplicationResult getApplication(GetApplicationArg arg);
}
