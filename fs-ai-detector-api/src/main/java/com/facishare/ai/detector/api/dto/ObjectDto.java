package com.facishare.ai.detector.api.dto;

import java.util.List;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class ObjectDto extends DtoBase {

    private String appId;

    private String objectType;

    private String objectName;

    private String objectId;

    private PositionDto position;

    private String unit;

    private String score;

    private String color;

    private Integer components;

    private Boolean isFront;

    private Boolean isRotated;

    private List<ObjectDto> componentEntities;

    private String type;

    private String key;

    private String scene;

    private Integer shelf;

    private Integer layer;

    private String skuSn;

    private Integer subSkuCount;

    private JSONObject extraData;
}
