package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;

/**
 * <AUTHOR>
 * @date 19-9-25  下午7:41
 */
public interface ObjectMapService {

    BatchAddObjectMapResult batchAddObjectMap(BatchAddObjectMapArg arg);

    DeleteModelMappingResult deleteModelMapping(DeleteModelMappingArg arg);

    UpdateObjectMapWithMapResult updateObjectMapWithMap(UpdateObjectMapWithMapArg arg);

    QueryObjectMapByKeysResult queryObjectMapByKeys(QueryObjectMapByKeysArg arg);

    /**
     * 通过ModelId和tenantId查询ObjectMapDto
     * @param arg 查询参数,包含modelId和tenantId
     * @return 查询结果
     */
    QueryObjectMapByModelIdResult queryObjectMapByModelIdAndTenantId(QueryObjectMapByModelIdArg arg);

    /**
     * 通过ID列表批量查询ObjectMap
     * @param arg 包含要查询的ID列表的参数对象
     * @return 查询结果，包含匹配的ObjectMap列表
     */
    QueryObjectMapByIdsResult queryObjectMapByIds(QueryObjectMapByIdsArg arg);


    /**
     * 批量更新ObjectMap
     * @param arg 包含ObjectMapDto列表和租户ID的参数对象
     * @return 批量更新结果
     */
    BatchUpdateObjectMapResult batchUpdateObjectMap(BatchUpdateObjectMapArg arg);

}
