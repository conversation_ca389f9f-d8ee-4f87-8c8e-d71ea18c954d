package com.facishare.ai.detector.api.dto.api;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface InnerDetect {

    @Data
    @ToString
    class Arg {

        private String appId;

        private String modelId;

        private String path;
    }

    @Data
    @ToString
    class Result {

        private int code;

        private String message;

        private boolean success;

        private ResultData data;
    }

    @Data
    @ToString
    class ResultData {

        private String path;

        private List<ObjectDTO> objectList;
    }

    @Data
    @ToString
    class ObjectDTO implements Serializable {

        private String apiName;

        private String dataId;

        private PositionDTO position;

        private String score;

        private String color;

        private String unit;

        private Integer components;

        private Boolean isFront;

        private Boolean isRotated;

        private List<ObjectDTO> componentEntities;

        private String type;
    }

    @Data
    @ToString
    class PositionDTO {

        private double x;

        private double y;

        private double w;

        private double h;
    }
}