package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-8  上午11:47
 */
@Data
@ToString
public class FaceComparisionResult implements Serializable {

    private String errorCode = "0";

    private String msg = ConstantUtil.SUCCESS;

    private Double score ;

    private List<String> faceIds;
}
