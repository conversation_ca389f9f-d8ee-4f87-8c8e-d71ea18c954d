package com.facishare.ai.detector.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/9 上午10:24
 */
@Data
@ToString
public class FaceAttributeDto extends DtoBase {

    /**
     * face id in detect platform
     */
    private String faceToken;

    /**
     * where the rectangle which covers the whole face locates in
     */
    private PositionDto position;

    /**
     * the extent to which is a face image
     */
    private Double confidence;

    /**
     * the angle of the face in yaw,pitch,plat
     */
    private Angle angle;

    /**
     * the goal for the face in beauty
     */
    private Integer beauty;

    private Integer age;
    /**
     * so it is
     */
    private String expression;


    private String faceShape;

    private String gender;

    /**
     * the type of glasses
     */
    private String glasses;

    /**
     * the degree of closure of left eye. the more close to 1 , the more open it is.
     */
    private Double leftEyeStatus;

    /**
     * the degree of closure of right eye . the more close to 1 , the more open it is.
     */
    private Double rightEyeStatus;


    private String emotion;

    /**
     * cartoon or real face
     */
    private String faceType;

    /**
     * if the mask is on face
     */
    private Integer mask;

    /**
     * occlusion, illumination
     */
    private Quality quality;

    /**
     * create by synthesis . 1 is true
     */
    private Boolean spoofing;


    @Data
    @ToString
    public static
    class Angle implements Serializable {
        private Double yaw;
        private Double pitch;
        private Double roll;
    }

    @Data
    @ToString
    public static
    class Quality implements Serializable {
        private Double leftEye;

        private Double rightEye;

        private Double nose;

        private Double mouth;

        private Double leftCheek;

        private Double rightCheek;

        private Double chin;

        private Double blur;

        private Double illumination;

        private Integer completeness;

    }

   public  enum Expression {
        LAUGH("laugh"),
        SMILE("smile"),
        NONE("none");
        private String value;

        public String value() {
            return this.value;
        }

        Expression(String value) {
            this.value = value;
        }
    }

    public enum Shape {
        SQUARE("square"),
        TRIANGLE("triangle"),
        OVAL("oval"),
        HEART("heart"),
        ROUND("round"),
        NONE("none");
        private String value;

        public String value() {
            return this.value;
        }

        Shape(String value) {
            this.value = value;
        }
    }

    public enum Gender {
        MALE("male"),
        FEMALE("female"),
        CUSTOM("custom"),
        NONE("none");
        private String value;

        public String value() {
            return this.value;
        }

        Gender(String value) {
            this.value = value;
        }
    }

    public enum Glasses {
        SUN("sun"),
        COMMON("common"),
        NONE("none");
        private String value;

        public String value() {
            return this.value;
        }

        Glasses(String value) {
            this.value = value;
        }
    }

    public enum Emotion {
        ANGRY("angry"),
        DISGUST("disgust"),
        FEAR("fear"),
        HAPPY("happy"),
        SAD("sad"),
        SURPRISE("surprise"),
        NEUTRAL("neutral"),
        POUTY("pouty"),
        GRIMACE("grimace"),
        NONE("none");
        private String value;

        public String value() {
            return this.value;
        }

        Emotion(String value) {
            this.value = value;
        }
    }
}
