package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.ObjectDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class DetectResult implements Serializable {

    private String path;

    private String originalPath;

    private String processedPath;

    private List<ObjectDto> objects;

    private Boolean isCache;
}
