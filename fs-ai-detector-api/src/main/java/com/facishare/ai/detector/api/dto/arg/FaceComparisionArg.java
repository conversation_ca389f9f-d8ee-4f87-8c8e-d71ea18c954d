package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 19-11-8  上午11:46
 */
@Data
@ToString
public class FaceComparisionArg implements Serializable {

    private Integer tenantId;

    private String tenantAccount;

    private String userId;

    private String groupId;

    private String baseImagePath;

    private String detectedImagePath;

    /*
    所识别的图片类型，如证件照，生活照，水印照等
     */
    private String scene="LIVE";


}
