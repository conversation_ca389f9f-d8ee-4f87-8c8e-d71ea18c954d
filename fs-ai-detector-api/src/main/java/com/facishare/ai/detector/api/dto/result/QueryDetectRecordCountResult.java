package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/27 下午6:33
 */
@Data
@ToString
public class QueryDetectRecordCountResult implements Serializable {

    private int errorCode;

    private String errorMsg = ConstantUtil.SUCCESS;

    private long count;
}
