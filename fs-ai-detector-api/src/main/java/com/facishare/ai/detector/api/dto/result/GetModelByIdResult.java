package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.ModelDto;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15 上午11:49
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GetModelByIdResult implements Serializable {

    private ModelDto modelDto;

    private List<ObjectMapDto> objectMapList;

    private List<AIDetectRuleDto> ruleList;
}
