package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.vo.DetectCounterDto;
import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/3/24 下午5:19
 */
@Data
@ToString
public class QueryCountByTimeResult implements Serializable {

    private int errorCode;

    private String errorMsg= ConstantUtil.SUCCESS;

    private DetectCounterDto record;
}
