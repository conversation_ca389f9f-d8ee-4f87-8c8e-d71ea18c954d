package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.DeleteAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleListArg;
import com.facishare.ai.detector.api.dto.arg.QueryRulesByIdsArg;
import com.facishare.ai.detector.api.dto.arg.QueryRulesByIdsArg;
import com.facishare.ai.detector.api.dto.arg.SaveAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.UpdateAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.result.DeleteAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleListResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.QueryRulesByIdsResult;
import com.facishare.ai.detector.api.dto.result.SaveAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.UpdateAIDetectRuleResult;

/**
 * AI检测规则服务接口
 * <AUTHOR>
 */
public interface AIDetectRuleService {
    
    /**
     * 新建AI检测规则
     * @param arg 规则信息
     * @return 保存结果
     */
    SaveAIDetectRuleResult saveRule(SaveAIDetectRuleArg arg);
    
    /**
     * 更新AI检测规则
     * @param arg 更新信息
     * @return 更新结果
     */
    UpdateAIDetectRuleResult updateRule(UpdateAIDetectRuleArg arg);
    
    /**
     * 作废/删除AI检测规则
     * @param arg 作废参数
     * @return 作废结果
     */
    DeleteAIDetectRuleResult deleteRule(DeleteAIDetectRuleArg arg);
    
    /**
     * 根据ID查询规则详情
     * @param arg 查询参数
     * @return 规则详情
     */
    GetAIDetectRuleResult getRuleById(GetAIDetectRuleArg arg);
    
    /**
     * 查询规则列表
     * @param arg 查询参数
     * @return 规则列表
     */
    GetAIDetectRuleListResult getRuleList(GetAIDetectRuleListArg arg);

    /**
     * 批量查询规则详情
     * @param arg 包含规则ID列表的查询参数
     * @return 规则详情列表
     */
    QueryRulesByIdsResult queryRulesByIds(QueryRulesByIdsArg arg);

}
