package com.facishare.ai.detector.api.exception;

public class AiProviderException extends RuntimeException {

    private final String code;

    public AiProviderException(String code, String message) {
        super(message);
        this.code = code;
    }

    public AiProviderException(String message, Throwable cause, String code) {
        super(message, cause);
        this.code = code;
    }

    public AiProviderException(Throwable cause, String code) {
        super(cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
