package com.facishare.ai.detector.api.dto.result;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ObjectDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/31 下午5:55
 */
@Data
@ToString
public class QueryDetectRecordResult implements Serializable{


    private Record record;

    @Data
    @ToString
    public static class Record{

        private String id;

        private Integer tenantId;

        private String modelId;

        private String originalPath;

        private String processedPath;

        private String path;

        private List<ObjectDto> objects;

        private Map<String,Object> extraData;

    }

}
