package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.ModelDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15 下午2:28
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GetModelListResult implements Serializable {

    private List<ModelDto> models;
}
