package com.facishare.ai.detector.api.dto.result;

import java.io.Serializable;
import java.util.List;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 通过ModelId和tenantId查询ObjectMap的结果类
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class QueryObjectMapByModelIdResult implements Serializable {

    private List<ObjectMapDto> data;

} 