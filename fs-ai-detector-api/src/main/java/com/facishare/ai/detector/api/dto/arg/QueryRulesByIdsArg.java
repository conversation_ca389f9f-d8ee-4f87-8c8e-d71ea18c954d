package com.facishare.ai.detector.api.dto.arg;

import java.io.Serializable;
import java.util.List;

/**
 * 批量查询规则的参数类
 * <AUTHOR>
 */
public class QueryRulesByIdsArg implements Serializable {

    /**
     * 企业ID
     */
    private Integer tenantId;

    /**
     * 规则ID列表
     */
    private List<String> ruleIds;

    // Getters and Setters
    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public List<String> getRuleIds() {
        return ruleIds;
    }

    public void setRuleIds(List<String> ruleIds) {
        this.ruleIds = ruleIds;
    }
} 