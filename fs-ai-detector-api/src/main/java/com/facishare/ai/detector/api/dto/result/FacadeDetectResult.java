package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午4:37
 */
@Data
@ToString
public class FacadeDetectResult implements Serializable {

    private int errorCode;
    private String errorMsg= ConstantUtil.SUCCESS;

    private List<FacadeInfo> facades;

    @Data
    @ToString
    static class FacadeInfo implements Serializable{
        private Double score;
        private String name;
        private String brief;
    }
}
