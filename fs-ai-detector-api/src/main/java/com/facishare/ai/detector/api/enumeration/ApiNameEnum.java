package com.facishare.ai.detector.api.enumeration;

/**
 * <AUTHOR>
 * @date 19-12-19  下午5:23
 */
public interface ApiNameEnum {
    class Common{
        public static final String OWNER="owner";
        public static final String API_NAME="api_name";
        public static final String ID="_id";
    }

    class PersonnelObj{
        public static final String API_NAME="PersonnelObj";
        public static final String SELFIE="selfie__c";
    }

    class PublicEmployeeObj{
        public static final String API_NAME="PublicEmployeeObj";
        public static final String SELFIE="ai_selfie";
    }
}
