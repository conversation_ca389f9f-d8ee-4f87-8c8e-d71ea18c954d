package com.facishare.ai.detector.api.dto.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:28
 */
@Data
@ToString
public class IdCardDetectResult implements Serializable {

    private int errorCode;

    private String errorMsg= ConstantUtil.SUCCESS;

    @JSONField(name = "direction")
    private int direction;

    @JSONField(name = "image_status")
    private String imageStatus;

    @J<PERSON>NField(name = "risk_type")
    private String riskType;

    @JSONField(name = "edit_tool")
    private String editTool;

    @JSO<PERSON>ield(name = "photo")
    private String photo;

    @JSONField(name = "idcard_number_type")
    private String validation;

    private String address;

    private String idNum;

    private String birth;

    private String name;

    private String sex;

    private String nationality;
}
