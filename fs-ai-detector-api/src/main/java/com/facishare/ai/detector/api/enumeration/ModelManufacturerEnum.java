package com.facishare.ai.detector.api.enumeration;



/**
 * 模型厂商枚举
 * 用于标识不同的AI模型提供商
 */
public enum ModelManufacturerEnum {
    
    BAIDU("百度", "baidu", "fmcg.model.manufacturer.baidu"),
    TU_JIANG("图匠", "tu_jiang", "fmcg.model.manufacturer.tu_jiang"),
    SENSE_TIME("商汤", "sense_time", "fmcg.model.manufacturer.sense_time"),
    HUAWEI("华为", "huawei", "fmcg.model.manufacturer.huawei"),
    RIO("RIO", "rio", "fmcg.model.manufacturer.rio"),
    YQSL("元气森林", "yqsl", "fmcg.model.manufacturer.yqsl"),
    MENG_NIU("蒙牛中台", "meng_niu", "fmcg.model.manufacturer.meng_niu"),
    FS("纷享", "fs", "fmcg.model.manufacturer.fs");

    /**
     * 厂商名称
     */
    private final String label;
    
    /**
     * 厂商标识值
     */
    private final String value;
    
    /**
     * 国际化key
     */
    private final String i18nKey;

    /**
     * 构造函数
     *
     * @param label 厂商名称
     * @param value 厂商标识值
     * @param i18nKey 国际化key
     */
    ModelManufacturerEnum(String label, String value, String i18nKey) {
        this.label = label;
        this.value = value;
        this.i18nKey = i18nKey;
    }

    /**
     * 根据value获取对应的枚举值
     *
     * @param value 厂商标识值
     * @return 对应的枚举值，如果没找到返回null
     */
    public static ModelManufacturerEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (ModelManufacturerEnum manufacturer : values()) {
            if (manufacturer.value().equals(value)) {
                return manufacturer;
            }
        }
        return null;
    }
    public String value() {
        return this.value;
    }
}
