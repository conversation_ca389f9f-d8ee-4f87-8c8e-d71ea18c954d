package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-9-26  上午10:01
 */
@Data
@ToString
public class BatchAddObjectMapResult implements Serializable {

    private String status;

    private String msg;

    private List<ObjectMapDto> objectMapDtoList;
}
