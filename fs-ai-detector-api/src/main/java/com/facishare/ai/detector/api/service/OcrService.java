package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.IdCardDetectArg;
import com.facishare.ai.detector.api.dto.arg.VATInvoiceDetectArg;
import com.facishare.ai.detector.api.dto.result.IdCardDetectResult;
import com.facishare.ai.detector.api.dto.result.VATInvoiceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/3/3 下午2:31
 */
public interface OcrService {

    IdCardDetectResult idCardDetect(IdCardDetectArg arg) throws AiProviderException, IOException;

    VATInvoiceDetectResult vatInvoiceDetect(VATInvoiceDetectArg arg) throws AiProviderException, IOException;


}
