package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.api.GetModel;
import com.facishare.ai.detector.api.dto.api.SaveDetectRecord;

public interface SdkService {

    GetModel.Result getModel(Integer tenantId, Integer currentEmployeeId, GetModel.Arg arg);

    SaveDetectRecord.Result saveDetectRecord(Integer tenantId, Integer currentEmployeeId, SaveDetectRecord.Arg arg);
}
