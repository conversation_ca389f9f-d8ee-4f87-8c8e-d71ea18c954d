package com.facishare.ai.detector.api.dto.arg;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import java.io.Serializable;
import java.util.List;

/**
 * 全量更新ObjectMap的参数类
 */
public class BatchUpdateObjectMapFullArg implements Serializable {
    
    /**
     * 要更新的完整ObjectMap信息
     */
    private List<ObjectMapDto> objectMapDtoList;
    
    /**
     * 租户ID
     */
    private Integer tenantId;

} 