package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.dto.result.DetectByBase64Result;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;

/**
 * <AUTHOR>
 */
public interface DetectorService {

    /**
     * 图像物体检测
     *
     * @param arg
     * @return
     * @throws AiProviderException
     */
    DetectResult detect(DetectArg arg) throws AiProviderException;

    BatchDetectResult batchDetect(BatchDetectArg arg) throws AiProviderException;

    DetectByBase64Result detectByBase64(DetectByBase64Arg arg) throws AiProviderException;
}
