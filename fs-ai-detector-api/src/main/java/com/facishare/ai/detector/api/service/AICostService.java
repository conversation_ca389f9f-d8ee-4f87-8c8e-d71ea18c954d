package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.CreateAccountArg;
import com.facishare.ai.detector.api.dto.arg.CreateAccountDetailArg;
import com.facishare.ai.detector.api.dto.arg.CreatePriceArg;
import com.facishare.ai.detector.api.dto.arg.QueryBalanceOfObjectDetectArg;
import com.facishare.ai.detector.api.dto.result.CreateAccountDetailResult;
import com.facishare.ai.detector.api.dto.result.CreateAccountResult;
import com.facishare.ai.detector.api.dto.result.CreatePriceResult;
import com.facishare.ai.detector.api.dto.result.QueryBalanceOfObjectDetectResult;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午3:59
 */
public interface AICostService {

    CreateAccountDetailResult createAccountDetail(CreateAccountDetailArg arg);

    CreateAccountResult createAccount(CreateAccountArg arg);

    CreatePriceResult createPrice(CreatePriceArg arg);

    QueryBalanceOfObjectDetectResult queryBalanceOfObjectDetect(QueryBalanceOfObjectDetectArg arg);
}
