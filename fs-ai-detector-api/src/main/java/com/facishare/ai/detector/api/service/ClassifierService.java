package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.BatchClassifyArg;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.dto.result.BatchClassifyResult;
import com.facishare.ai.detector.api.dto.result.ClassifyResult;
import com.facishare.ai.detector.api.exception.AiProviderException;

/**
 * <AUTHOR>
 * @date 19-8-1  下午3:19
 */
public interface ClassifierService {

    /**
     * 场景识别
     *
     * @param arg
     * @return
     * @throws AiProviderException
     */
    ClassifyResult classify(ClassifyArg arg) throws AiProviderException;

    BatchClassifyResult batchClassify(BatchClassifyArg arg) throws AiProviderException;
}
