package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetAIDetectRuleListResult implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Boolean success;
    private String message;
    private List<AIDetectRuleDto> ruleList;
}