package com.facishare.ai.detector.api.dto.arg;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * 批量更新ObjectMap的参数类
 */
@Data
@ToString
public class BatchUpdateObjectMapArg implements Serializable {
    
    /**
     * 要更新的ObjectMap列表
     */
    private List<ObjectMapDto> objectMapList;
    
    /**
     * 租户ID
     */
    private Integer tenantId;
} 