package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.DeleteDetectRecordByTenantIdArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordCountArg;
import com.facishare.ai.detector.api.dto.result.DeleteDetectedRecordByTenantIdResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordCountResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordResult;

/**
 * <AUTHOR>
 * @date 2021/8/31 下午5:52
 */
public interface DetectRecordService {

    QueryDetectRecordResult queryDetectRecord(QueryDetectRecordArg arg);

    QueryDetectRecordCountResult queryDetectRecordCount(QueryDetectRecordCountArg arg);

    DeleteDetectedRecordByTenantIdResult deleteDetectedRecordByTenantId(DeleteDetectRecordByTenantIdArg arg);

}
