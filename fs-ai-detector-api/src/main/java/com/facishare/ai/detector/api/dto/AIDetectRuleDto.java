package com.facishare.ai.detector.api.dto;

import java.util.Map;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AIDetectRuleDto extends DtoBase {

    private String modelId;

    private String name;

    private String apiName;

    private String ruleDescribe;

    private String promptTemplate;

    private String masterDescribeApiName;

    /**
     * 业务能力
     * isOpenProductRowNumber 商品sku/排面数识别
     * isOpenGroupNumber 商品陈列组数
     * isOpenLayerNumber 货架层数识别
     * openSkuUnit 商品单位识别
     * isOpenPrices 价格识别
     * isOpenSceneDetect 商品陈列场景
     * isOpenDisplayForm 商品陈列形式
     */
    private Map<String, Integer> detectCapabilityMap;

    /**
     * aiPath ai图片
     * productName 产品名称
     * aiRowNumber 商品排面数  需要calculateType属性
     * aiGroupNumber 商品组数  需要calculateType属性
     * aiLayerNumber 层数
     * aiSceneField 场景字段
     * aiUnitField 单位存储字段
     * aiPrices posm的价格字段
     *   陈列形式rio
     * displayTotalLayerNumber 陈列形式的层数
     * displayCutBoxNumber 陈列形式的割箱数
     * displayMaxVisibleNumber 陈列形式的最大可视数
     * displaySceneType 陈列形式的场景类型
     * displayTotalRowNumber 陈列形式的排面总数
     */
    private Map<String, FieldDto> fieldMap;

    private boolean isDefault;

}
