package com.facishare.ai.detector.api.dto.api;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface GetModel {

    @Data
    @ToString
    class Arg implements Serializable {

        private String modelId;

        private boolean includeObjectMap;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private int code;

        private String message;

        private boolean success;

        private ResultData data;
    }

    @Data
    @ToString
    class ResultData implements Serializable {

        private ModelDTO model;
    }

    @Data
    @ToString
    class ModelDTO implements Serializable {

        private String id;

        private String name;

        private Integer tenantId;

        private String platform;

        private String key;

        private Double confidence;

        private String type;

        private String identity;

        private String tokenKey;

        private JSONObject params;

        private String uniqueId;

        private String scene;

        private int status;

        private String modelManufacturer;

        private List<ObjectMapDTO> objectMapList;
    }


    @Data
    @ToString
    class ObjectMapDTO implements Serializable {

        private String key;

        private String apiName;

        private String objectId;

        private String unit;

        private String appId;

        private String color;

        private Double threshold;

        private JSONObject extraData;
    }
}
