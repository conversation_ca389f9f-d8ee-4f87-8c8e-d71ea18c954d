package com.facishare.ai.detector.api.enumeration;

/**
 * <AUTHOR>
 * @date 19-9-24  下午7:21
 */
public enum AIAbilityEnum {
    RECAPTURE_CLASSIFY("RECAPTURE_CLASSIFY"),
    JML_NOODLES_DETECT("JML_NOODLES_DETECT"),
    GF_HAWTHORN_DETECT("GF_HAWTHORN_DETECT"),
    CUSTOM_DRINK_DETECT("CUSTOM_DRINK_DETECT"),
    HA_TISSUE_DETECT("HA_TISSUE_DETECT");

    AIAbilityEnum(String value) {
        this.value = value;
    }

    private String value;

    public String value() {
        return this.value;
    }
}
