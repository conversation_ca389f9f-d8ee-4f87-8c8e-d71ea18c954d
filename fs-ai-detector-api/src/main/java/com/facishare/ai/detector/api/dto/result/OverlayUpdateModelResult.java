package com.facishare.ai.detector.api.dto.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

import com.facishare.ai.detector.api.dto.ModelDto;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OverlayUpdateModelResult implements Serializable {
    private String code;
    private String message;
    private ModelDto model;
}