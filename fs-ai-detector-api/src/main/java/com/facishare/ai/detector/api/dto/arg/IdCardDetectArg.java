package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:48
 */
@Data
@ToString
public class IdCardDetectArg implements Serializable {
    /**
     *
     * @param imagePath 图片的npath
     * @param cardSide front/back 必填 正面或背面
     * @param detectDirection 默认为true 是否为检测旋转方向 90度为一个单位
     * @param detectRisk  默认为 false 是否为翻拍和复印件
     * @param detectPhoto 默认为false 是否检测头像
     * @param detectRectify 默认为false 是否检测完整性
     */
    private String tenantAccount;
    private String imagePath;
    private String cardSide;
    private String detectDirection;
    private String detectRisk;
    private String detectPhoto;
    private String detectRectify;
}
