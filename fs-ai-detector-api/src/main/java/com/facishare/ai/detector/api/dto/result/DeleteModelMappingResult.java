package com.facishare.ai.detector.api.dto.result;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/3/4 14:26
 */

@Data
@ToString
@NoArgsConstructor
public class DeleteModelMappingResult implements Serializable {

    private String message;

    private int count;

    public DeleteModelMappingResult(String message){
        this.message = message;
    }

    public DeleteModelMappingResult(int count){
        this.count = count;
    }
}
