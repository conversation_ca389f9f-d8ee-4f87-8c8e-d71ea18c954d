package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 19-11-8  下午2:19
 */
@Data
@ToString
public class AddFaceResult implements Serializable {

    private String errorCode = "0";

    private String msg = ConstantUtil.SUCCESS;

    private String faceId;

    private double[] location;
}
