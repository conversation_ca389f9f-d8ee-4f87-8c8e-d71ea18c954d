package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 批量查询规则的结果类
 * <AUTHOR>
 */
@Data
public class QueryRulesByIdsResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private Boolean success;


    /**
     * 错误信息
     */
    private String message;

    /**
     * 规则详情列表
     */
    private List<AIDetectRuleDto> rules;

    // Getters and Setters
   
} 