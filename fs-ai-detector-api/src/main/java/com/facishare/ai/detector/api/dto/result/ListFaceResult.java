package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-8  下午2:45
 */
@Data
@ToString
public class ListFaceResult implements Serializable {

    private String errorCode = "0";

    private String msg = ConstantUtil.SUCCESS;

    private List<String> faceIds;
}
