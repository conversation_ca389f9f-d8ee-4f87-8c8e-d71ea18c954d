package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.FaceAttributeDto;
import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/9 上午10:03
 */
@Data
@ToString
public class FaceDetectResult implements Serializable {

    private String errorCode = "0";

    private String msg = ConstantUtil.SUCCESS;

    private Integer faceCnt;

    private List<FaceAttributeDto> faces;
}
