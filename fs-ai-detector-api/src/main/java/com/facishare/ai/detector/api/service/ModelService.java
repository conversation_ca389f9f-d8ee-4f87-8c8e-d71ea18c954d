package com.facishare.ai.detector.api.service;

import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;

/**
 * <AUTHOR>
 * @date 19-9-25  下午7:41
 */
public interface ModelService {

    BatchAddModelResult batchAddModel(BatchAddModelArg arg);

    GetModelsByTenantIdResult getModelsByTenantId(GetModelsByTenantIdArg arg);

    SaveModelResult saveModel(SaveModelArg arg);

    OverlayUpdateModelResult overlayUpdate(OverlayUpdateModelArg arg);

    UpdateModelResult updateModel(UpdateModelArg arg);

    GetModelByIdResult getModelById(GetModelByIdArg arg);

    GetModelListResult getModelList(GetModelListArg arg);

    CopyModelAndMappingResult copyModelAndMapping(CopyModelAndMappingArg arg);

    ModelSwitchResult modelSwitch(ModelSwitchArg arg);
}
