package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class DetectByBase64Arg implements Serializable {

    private String tenantId;

    private String userId;

    private String tenantAccount;

    private String appId;

    private String modelId;

    private String path;

    private String base64;
}
