package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 通过ID列表查询ObjectMap的结果类
 */
@Data
@ToString
public class QueryObjectMapByIdsResult implements Serializable {
    
    /**
     * 查询到的ObjectMap列表
     */
    private List<ObjectMapDto> objectMapList;
    
} 