package com.facishare.ai.detector.api.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;



@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FieldDto extends DtoBase {

    /**
     *  类型 目前仅有 mapping
     */
    private String type;

    /**
     * 字段apiName
     */
    private String fieldKey;

    /**
     * 0 求和
     * 1 最大值
     */
    private Integer  calculateType;

    /**
     * 对象apiName
     */
    private String objectApiName;

    /**
     * 字段类型
     * image
     * select_one
     * object_reference
     * number
     * text
     */
    private String fieldType;

    /**
     * ai存储字段
     */
    private String aiStoreFieldApiName;

    /**
     * 手动存储字段
     */
    private String manuallyStoreFieldApiName;

    private Boolean enable ;

    private ValidLayerJudgmentDto validLayerJudgment;
}
