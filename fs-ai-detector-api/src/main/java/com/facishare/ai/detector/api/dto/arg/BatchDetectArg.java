package com.facishare.ai.detector.api.dto.arg;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-10-14  上午10:24
 */
@Data
@ToString
public class BatchDetectArg implements Serializable {

    private String tenantId;

    private String tenantAccount;

    private String appId;

    private String userId;

    private String modelId;

    private List<String> paths;

    private Boolean createProcessedImage = true;
}
