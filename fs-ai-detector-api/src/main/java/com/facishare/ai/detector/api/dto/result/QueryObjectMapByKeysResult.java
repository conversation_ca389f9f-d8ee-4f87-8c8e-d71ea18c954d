package com.facishare.ai.detector.api.dto.result;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class QueryObjectMapByKeysResult implements Serializable {

    private List<ObjectMapDto> objectMaps;
}
