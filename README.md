# fs-ai-detector

Artificial intelligence image detector base on YOLO or other platform.

## API 接口说明

### 内部接口 (Inner API)

#### 模型相关接口

1. **获取模型列表**
   - 路径: `/API/inner/v1/ai/model/getModelList`
   - 方法: POST
   - 参数: GetModelListArg
   - 返回: GetModelListResult

2. **根据ID获取模型**
   - 路径: `/API/inner/v1/ai/model/getModelById`
   - 方法: POST
   - 参数: GetModelByIdArg
   - 返回: GetModelByIdResult

#### 应用相关接口

1. **添加应用**
   - 路径: `/API/inner/v1/ai/model/addApplication`
   - 方法: POST
   - 参数: AddApplicationArg
   - 返回: AddApplicationResult
   - 说明: 添加AI应用信息

2. **获取应用**
   - 路径: `/API/inner/v1/ai/model/getApplication`
   - 方法: POST
   - 参数: GetApplicationArg
   - 返回: GetApplicationResult
   - 说明: 根据租户ID和身份密钥获取应用信息