package com.facishare.ai.detector.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.ObjectDto;
import com.facishare.ai.detector.api.dto.api.SaveDetectRecord;
import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.ai.detector.api.service.FaceDetectService;
import com.facishare.ai.detector.api.service.SdkService;
import com.facishare.ai.detector.provider.adapter.service.abstraction.OcrDetector;
import com.facishare.ai.detector.provider.adapter.service.classfy.BaiduClassifierImpl;
import com.facishare.ai.detector.provider.adapter.service.classfy.BaiduCustomizedClassifierImpl;
import com.facishare.ai.detector.provider.adapter.service.classfy.BaiduLocalClassifierImpl;
import com.facishare.ai.detector.provider.adapter.service.classfy.HuaWeiClassifierImpl;
import com.facishare.ai.detector.provider.adapter.service.detect.*;
import com.facishare.ai.detector.provider.dao.OldModelDaoImpl;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.AutoMapper;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.fs.fmcg.sdk.ai.adapter.contract.CommonDetect;
import com.fs.fmcg.sdk.ai.business.abstration.RowFaceBusiness;
import com.fs.fmcg.sdk.ai.common.SpringContextHolder;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.google.common.base.Strings;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.sql.SQLOutput;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-1  下午5:19
 */
public class BaiduTest extends YoloTest {

    @Resource
    private TokenFactory tokenFactory;

    @Resource
    private BaiduClassifierImpl baiduClassifier;

    @Resource
    private BaiduLocalClassifierImpl baiduLocalClassifier;

    @Resource
    private HuaWeiClassifierImpl huaWeiClassifier;

    @Autowired(required = false)
    private AlibabaDetectorImpl alibabaDetector;

    @Resource
    private BaiduDetectorImpl baiDuAdapter;


    @Resource
    private OldModelDaoImpl oldModelDao;

    @Resource
    private BaiduCustomizedClassifierImpl baiduCustomizedClassifier;

    @Resource
    private FaceDetectService faceDetectService;

    @Resource
    private OcrDetector ocrDetector;

    @Resource
    private PinLanDetectorImpl pinLanDetector;
    @Resource
    private HuaWeiDetectorImpl huaWeiDetector;

    @Resource
    private DetectorService detectorService;

    @Resource
    private SdkService sdkService;

    @Resource
    private ModelDao modelDao;

    @Resource
    private HuaWeiSkuPocDetectorImpl huaweiSkuPocDetectorImpl;

    @Resource
    private HuaWeiModelArtsDetectorImpl huaWeiModelArtsDetectorImpl;

    @Resource
    private YQDetectorImpl yqDetector;

    @Resource
    private BaiduOfficialRetailDetectorImpl baiduOfficialRetailDetector;

    private static final Color[] COLORS = new Color[]{new Color(249,42,130),new Color(67,217,253),new Color(249,100,116),new Color(32,218,154),new Color(252,224,17),new Color(255,116,204),new Color(109,169,255),new Color(31,131,255),new Color(246,172,255),new Color(185,207,68),new Color(80,143,179),new Color(201,31,37),new Color(255,169,46),new Color(224,88,41),new Color(63,63,152),new Color(242,182,194),new Color(134,193,45),new Color(155,57,236),new Color(177,119,55),new Color(147,133,236),new Color(24,151,159),new Color(210,85,101),new Color(36,112,160),new Color(183,92,37),new Color(61,212,196)};

        @Test
        public void test1() throws IOException, AiProviderException {
            JSONObject object = new JSONObject();
            File file = new File("/Users/<USER>/Downloads/zxgt.jpeg");
            byte[] b = new byte[(int) file.length()];
            object.put("img", Base64.encodeBase64String(b));
            new FileInputStream(file).read(b);
            ModelPo po = modelDao.query("60af5e18cb69647054074789",81767);

            DetectArg arg = new DetectArg();

            arg.setTenantId("81767");
            arg.setTenantAccount("81767");
            arg.setPath("N_202105_20_e9016eda598b41c3a4d73d28dfe767961");
            arg.setModelId("60af5e18cb69647054074789");
            arg.setUserId("1011");
            List<BoxDto> list = huaWeiModelArtsDetectorImpl.detect(arg, po, b);
            list  = AutoMapper.toBoxDtoList(rebuildBox(po.getKey(),AutoMapper.toCommonDetectBoxDTOList(list)));
            System.out.println("----------------------------------------------------------------------");
            System.out.println(JSON.toJSONString(list));
            drawBox(b,list,"55");
        }

    private List<CommonDetect.BoxDTO> rebuildBox(String key, List<CommonDetect.BoxDTO> boxes)  {
        String util = RowFaceBusiness.HLY_ROW_FACE;
        if(!Strings.isNullOrEmpty(util)){
            RowFaceBusiness rowFaceBusiness = SpringContextHolder.getBean(RowFaceBusiness.HLY_ROW_FACE);
            if(rowFaceBusiness!=null)
                return rowFaceBusiness.countRowFace(boxes);
        }
        return boxes;
    }
    @Test
    public void test2() throws IOException, AiProviderException {
        JSONObject object = new JSONObject();
        File file = new File("/home/<USER>/Downloads/timg.jpeg");
        byte[] b = new byte[(int) file.length()];
        object.put("img", Base64.encodeBase64String(b));
        new FileInputStream(file).read(b);
        ModelPo po = oldModelDao.get(608158,"14e92e1b-5467-448e-8417-e550769c5b91");
        ClassifyArg arg = new ClassifyArg();

        arg.setTenantId("608158");
        arg.setPath("test");
        arg.setModelId("60001594cb696470543fa567");
        List<ClassDto> list = baiduCustomizedClassifier.classify(arg, po, b);
        System.out.println("----------------------------------------------------------------------");
        System.out.println(JSON.toJSONString(list));
    }

    public void drawBox(byte[] imageStream, List<BoxDto> boxes, String name) throws IOException {
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageStream));
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        Map<String, Color> colorMap = new HashMap<>();

        int index = 0;
        for (BoxDto box : boxes) {
            if (!colorMap.containsKey(box.getName())) {
                colorMap.put(box.getName(), COLORS[index % COLORS.length]);
                index = index + 1;
            }
            graphics.setColor(colorMap.get(box.getName()));
            graphics.setStroke(new BasicStroke(10.0f));

            int top = box.getBox()[0].intValue();
            int left = box.getBox()[1].intValue();
            int bottom = box.getBox()[2].intValue();
            int right = box.getBox()[3].intValue();

            graphics.drawRoundRect(left, top, right - left, bottom - top, 50, 50);
            double size = (right - left)/8.0;
            Font font = new Font("微软雅黑", Font.BOLD, (int) size);
            graphics.setFont(font);
            graphics.drawString(box.getName(), left + 20, top - 10);
        }

        StoneFileUploadRequest uploadRequest = new StoneFileUploadRequest();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", new File(String.format("/home/<USER>/Pictures/rst_%s.jpg", name)));
    }

    @Test
    public void FaceTest() throws AiProviderException {
        FaceComparisionArg arg = new FaceComparisionArg();
        arg.setBaseImagePath("N_202104_30_57121014a14b4dbc824d93c28cc640f9");
        arg.setDetectedImagePath("TN_0f1a567a1bd442d88a36a5c77fbb2e76_tmb");
        arg.setTenantId(84931);
        arg.setUserId("1000");
        arg.setTenantAccount("84931");
        System.out.println(faceDetectService.faceComparision(arg));
    }

    @Test
    public void FaceDetectTest() throws AiProviderException {
        FaceDetectArg arg = new FaceDetectArg();
        arg.setImagePath("N_202010_09_3f0460dc9d7b4ea1867e94516732c34f1");
        arg.setTenantId(78582);
        arg.setUserId("1059");
        arg.setTenantAccount("78582");
        System.out.println(JSON.toJSONString(faceDetectService.faceDetect(arg)));
    }

    @Test
    public void FaceAddTest() throws AiProviderException {
        AddFaceArg arg = new AddFaceArg();
        arg.setImagePath("TN_ae9f69eff3e547fd8a8fdb7b75778794");
        arg.setTenantId(78582);
        arg.setUserId("1011");
        arg.setTenantAccount("78582");
        System.out.println(faceDetectService.addFace(arg));
    }


    @Test
    public void testOcr() throws IOException, AiProviderException {
        JSONObject object = new JSONObject();
        File file = new File("/home/<USER>/Pictures/Selection_005.png");
        byte[] b = new byte[(int) file.length()];
        new FileInputStream(file).read(b);
        System.out.println(        ocrDetector.idCardDetect(b,"front",null,null,null,null)
        );
    }

    @Test
    public void testOcrFp() throws IOException, AiProviderException {
        JSONObject object = new JSONObject();
        File file = new File("/home/<USER>/Pictures/fp.jpeg");
        byte[] b = new byte[(int) file.length()];
        new FileInputStream(file).read(b);
        System.out.println(JSON.toJSONString(ocrDetector.VATInvoiceDetect(b,null,null)));;
        System.out.println();
    }
    /*
        @Test
        public void getTocken() throws AiProviderException {
            BaiduDetectorImpl baiduDetector = new BaiduDetectorImpl();
            GetAccessTokenArg accessTokenArg = new GetAccessTokenArg();
            accessTokenArg.setClientId("9klF9WGtUn0BztnRaFpAEl3s");
            accessTokenArg.setClientSecret("wpqK7jdzIXni26TapdInGqR9svR9DHKG");
            GetAccessTokenResult result = baiduDetector.getAccessToken(accessTokenArg);
            System.out.println("22222222222222222222222222222222222222222222222");
            System.out.println(JSON.toJSONString(result.getAccessToken()));
        }

        @Test
        public void test2() throws IOException, AiProviderException {
            long t = System.currentTimeMillis();
            File file = new File("/home/<USER>/Downloads/N_201909_20_6391afc678934a1884d84c20ed79a3a71.jpg");
            byte[] b = new byte[(int) file.length()];
            new FileInputStream(file).read(b);
            System.out.println(huaWeiClassifier.classify(null, null, b));
            System.out.println((System.currentTimeMillis() - t) + "ms");
        }

        @Test
        public void test3() throws AiProviderException {
            ModelPo modelPo = new ModelPo();
            DetectArg arg = new DetectArg();
            arg.setAppId("default");
         //   arg.setModelId("");
            arg.setUserId("default");

            System.out.println(JSON.toJSONString(alibabaDetector.detect(arg, modelPo, null)));
            System.out.println(JSON.toJSONString(        alibabaDetector.query("http://*************:12102/pub/v1/job_manager/query?job_id=63d018c16f9a4fa8a22a1cebba4cd6a9&user_id=default","default")
            ));
        }

        @Test
        public void test4() throws AiProviderException {

            System.out.println(JSON.toJSONString(alibabaDetector.query("http://*************:12102/pub/v1/job_manager/query?job_id=903b9e3b8abe417c95ea3396e849ab01&user_id=default", "default")
            ));
        }








        @Test
        public  void deal(){
            ModelPo recapture1 = new ModelPo();
            recapture1.setKey("image_recapture_detection");
            recapture1.setName("baidu_customized_recapture");
            recapture1.setPlatform("baidu_customized");
            recapture1.setConfidence(0.9);
            recapture1.setTenantId(590081);
            recapture1.setType(AICategoryEnum.IMAGE_CLASSIFICATION.value());

            ModelPo recapture2 = new ModelPo();
            recapture2.setKey("recaptureV2");
            recapture2.setModelCode("36102");
            recapture2.setName("baidu_recapture");
            recapture2.setPlatform("baidu");
            recapture2.setConfidence(0.9);
            recapture2.setTenantId(590081);
            recapture2.setType(AICategoryEnum.IMAGE_CLASSIFICATION.value());


            ModelPo recapture3 = new ModelPo();
            recapture3.setKey("jml_v1");
            recapture3.setModelCode("37496");
            recapture3.setName("jml_noodles");
            recapture3.setPlatform("baidu");
            recapture3.setConfidence(0.8);
            recapture3.setTenantId(590081);
            recapture3.setType(AICategoryEnum.OBJECT_RECOGNITION.value());

            ModelPo recapture4 = new ModelPo();
            recapture4.setKey("drink");
            recapture4.setName("custom_drink");
            recapture4.setPlatform("baidu_retail");
            recapture4.setConfidence(0.7);
            recapture4.setTenantId(590081);
            recapture4.setType(AICategoryEnum.OBJECT_RECOGNITION.value());

            ModelPo recapture5 = new ModelPo();
            recapture5.setKey("hengan_v2");
            recapture5.setModelCode("33869");
            recapture5.setName("heanan_tissue");
            recapture5.setPlatform("baidu");
            recapture5.setConfidence(0.8);
            recapture5.setTenantId(590081);
            recapture5.setType(AICategoryEnum.OBJECT_RECOGNITION.value());


            ModelPo recapture6 = new ModelPo();
            recapture6.setKey("");
            recapture6.setModelCode("FenXiangXiaoKe");
            recapture6.setName("guanfang_hawthorn");
            recapture6.setPlatform("alibaba");
            recapture6.setConfidence(0.8);
            recapture6.setTenantId(590081);
            recapture6.setType(AICategoryEnum.OBJECT_RECOGNITION.value());

            ModelPo recapture7 = new ModelPo();
            recapture7.setKey("recapture");
            recapture7.setName("huawei_recapture");
            recapture7.setPlatform("huawei");
            recapture7.setConfidence(0.7);
            recapture7.setTenantId(590081);
            recapture7.setType(AICategoryEnum.IMAGE_CLASSIFICATION.value());

            modelDao.save(recapture1);
            modelDao.save(recapture2);
            modelDao.save(recapture3);
            modelDao.save(recapture4);
            modelDao.save(recapture5);
            modelDao.save(recapture6);
            modelDao.save(recapture7);
        }*/
   /* @Test
    public void test() throws AiProviderException {
        System.out.println("------------------------------"+tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_EASYDL.value()));
    }
*/
   @Test
   public void detect() throws AiProviderException {

       DetectArg arg =  new DetectArg();
       arg.setTenantAccount("78582");
       arg.setUserId("1011");
       arg.setModelId("5dabce96e75d9594e1dc05f6");
       arg.setPath("N_202105_28_ab913a6cc6d34ea298f2c9e3c11e2746");
       arg.setTenantId("78582");
       arg.setAppId("CRM");
       System.out.println(detectorService.detect(arg));;
   }

   @Test
   public void testSdkSaveRecord(){
       SaveDetectRecord.Arg arg = new SaveDetectRecord.Arg();
       arg.setModelId("123");
       arg.setPath("2134");
       arg.setObjects(new ArrayList<>());
       ObjectDto dto = new ObjectDto();
       dto.setObjectId("5d3bbf1e7cfed965a54daf72");
       dto.setObjectType("ProductObj");
       arg.getObjects().add(dto);
       sdkService.saveDetectRecord(78582,1000,arg);
   }

    @Test
    public void testGray() throws AiProviderException, InterruptedException {

       /* while(true){
            Map<Integer,List<Integer>> map  = GrayUtil.getMap("AI_FACE_RECAPTURE");
            System.out.println(JSON.toJSONString(map));
            Thread.sleep(2000);
        }*/
    }

   @Test
    public void testHuawei() throws AiProviderException {
       System.out.println(       "-------------------start:"+tokenFactory.getToken("BAIDU_KM")
       +"   --------------------------------------------------------");
   }

   @Test
    public void printColor(){
       List<String> colorList = new ArrayList<>();
       String tmplate = "#%2x%2x%2x";
       for(Color color : COLORS){
           colorList.add(String.format(tmplate,color.getRed(),color.getGreen(),color.getGreen()));
       }
       System.out.println(JSON.toJSONString(colorList));
   }

}
