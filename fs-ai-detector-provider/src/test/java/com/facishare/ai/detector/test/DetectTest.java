package com.facishare.ai.detector.test;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.DetectRecordService;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.ai.detector.api.service.OcrService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.facade.DetectorController;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DetectTest extends YoloTest {

    @Resource
    private DetectorService detectorService;

    @Resource
    private DetectorController detectorController;

    @Resource
    private OcrService ocrService;

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private DetectRecordService detectRecordService;

    @Test
    public void batchDetectTest() throws AiProviderException {
        BatchDetectArg batchDetectArg = new BatchDetectArg();
        batchDetectArg.setTenantId("78582");
        batchDetectArg.setTenantAccount("78582");
        batchDetectArg.setAppId("FMCG.EFFICIENCY");
        batchDetectArg.setUserId("1011");
        batchDetectArg.setModelId("60001594cb696470543fa567");
        batchDetectArg.setCreateProcessedImage(false);
        batchDetectArg.setPaths(Lists.newArrayList(
                "**********.jpg"));
        BatchDetectResult result = detectorService.batchDetect(batchDetectArg);
        System.out.println(JSON.toJSONString(result));
        assert result != null;
    }

    @Test
    public void loc()
    {
        String spec = "口味:老坛酸菜";
        if (!Strings.isNullOrEmpty(spec)) {
            List<String> specCodeItemList = new ArrayList<>();
            for (String specItem : spec.split(";")) {
                if (specItem.contains(":")) {
                    specCodeItemList.add(specItem.split(":")[1]);
                }
            }
            String specCode = String.join("-", specCodeItemList);
            System.out.println(specCode);
        }
    }

    @Test
    public void testController() throws AiProviderException {
        ClassifyArg arg = new ClassifyArg();
        arg.setModelId("14e92e1b-5467-448e-8417-e550769c5b91");
        arg.setPath("N_201910_30_9611762ca5204627884528972b40d6361");
        arg.setUserId("1000");
        arg.setTenantAccount("fsceshi007");
        arg.setTenantId("71578");
        System.out.println(       "-----------------------------"+ detectorController.classify(arg));
    }


    @Test
    public void testOcrIdCard() throws AiProviderException, IOException {
        IdCardDetectArg arg = new IdCardDetectArg();
        arg.setTenantAccount("78582");
        arg.setCardSide("front");
        arg.setImagePath("TN_c7573997203043369ed3acfb7fffa8e9_tmb");
        System.out.println(JSON.toJSONString(ocrService.idCardDetect(arg)));
    }


    @Test
    public void testOcrVatInvoice() throws AiProviderException, IOException {
        VATInvoiceDetectArg arg = new VATInvoiceDetectArg();
        arg.setTenantAccount("78582");
        arg.setImagePath("N_202003_13_d1f2a3c00c2d4995a854a09fdc7707211");
        System.out.println(JSON.toJSONString(ocrService.vatInvoiceDetect(arg)));
    }

    @Test
    public void testDetect() throws AiProviderException, IOException {
        DetectArg arg = new DetectArg();
        arg.setTenantAccount("78582");
        arg.setPath("N_202006_02_8ce13e1703f54edf86f50c70694ab5111");
        arg.setTenantId("78582");
        arg.setAppId("CRM");
        arg.setUserId("1000");
        arg.setModelId("5ed5facfe4990776b7d3a91c");
        System.out.println(JSON.toJSONString(detectorController.detect(arg)));
    }

    @Test
    public void share() throws AiProviderException {
        System.out.println( fileAdapter.createShareFile("78582",1000,"N_202007_08_77e700cdb8504a64bcd6c6e3bd340b4d1"));
    }

    @Test
    public void fileTest() throws AiProviderException {
        fileAdapter.downloadWithoutSockets(78582,"78582","TN_4f5a727c3d7843ed8b55c9d6420aa7b2_tmb");
    }

    @Test
    public void recordTest(){
        QueryDetectRecordArg arg = new QueryDetectRecordArg();
        arg.setModelId("639c2f1fd3245cdbd12dc028");
        arg.setTenantId(84931);
        arg.setPath("TN_e28cd2d689344057a0434bda4369ee8b_tmb");
        System.out.println(JSON.toJSONString(detectRecordService.queryDetectRecord(arg)));
    }

}
