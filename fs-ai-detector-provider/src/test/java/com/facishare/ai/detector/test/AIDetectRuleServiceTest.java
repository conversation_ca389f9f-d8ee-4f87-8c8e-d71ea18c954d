package com.facishare.ai.detector.test;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.FieldDto;
import com.facishare.ai.detector.api.dto.arg.DeleteAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleListArg;
import com.facishare.ai.detector.api.dto.arg.QueryRulesByIdsArg;
import com.facishare.ai.detector.api.dto.arg.SaveAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.UpdateAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.result.DeleteAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleListResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.QueryRulesByIdsResult;
import com.facishare.ai.detector.api.dto.result.SaveAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.UpdateAIDetectRuleResult;
import com.facishare.ai.detector.provider.dao.abstraction.AIDetectRuleDAO;
import com.facishare.ai.detector.provider.service.AIDetectRuleServiceImpl;

import javax.annotation.Resource;

import com.facishare.ai.detector.provider.util.ConvertUtil;

import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.bson.types.ObjectId;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

public class AIDetectRuleServiceTest extends YoloTest {

    @Resource
    private AIDetectRuleServiceImpl aiDetectRuleService;

    @Resource
    private AIDetectRuleDAO aiDetectRuleDao;

    @Test
    public void testSaveRule_Success() {
        // 准备测试数据
        Integer tenantId = 84931;
        String ruleId = new ObjectId().toString();

        SaveAIDetectRuleArg arg = new SaveAIDetectRuleArg();
        AIDetectRuleDto ruleDto = new AIDetectRuleDto();
        ruleDto.setId(ruleId);
        ruleDto.setRuleDescribe("123");
        ruleDto.setModelId("67b84517d056f06fbe2b1bcf");
        Map<String, Integer> detectCapabilityMap = new HashMap<>();
        /**
         * 业务能力
         * isOpenProductRowNumber 商品sku/排面数识别
         * isOpenGroupNumber 商品陈列组数
         * isOpenLayerNumber 货架层数识别
         * openSkuUnit 商品单位识别
         * isOpenPrices 价格识别
         * isOpenSceneDetect 商品陈列场景
         * isOpenDisplayForm 商品陈列形式
         * isPOSMDetect 物料识别
         */
        detectCapabilityMap.put("isOpenProductRowNumber", 1);
        detectCapabilityMap.put("isOpenGroupNumber", 1);
        detectCapabilityMap.put("isOpenLayerNumber", 1);
        detectCapabilityMap.put("openSkuUnit", 1);
        detectCapabilityMap.put("isOpenPrices", 1);
        detectCapabilityMap.put("isOpenSceneDetect", 1);
        detectCapabilityMap.put("isOpenDisplayForm", 1);
        detectCapabilityMap.put("isPOSMDetect", 1);
        ruleDto.setDetectCapabilityMap(detectCapabilityMap);
        /**
         * aiPath ai图片
         * productName 产品名称
         * aiRowNumber 商品排面数 需要calculateType属性
         * aiGroupNumber 商品组数 需要calculateType属性
         * aiLayerNumber 层数
         * aiSceneField 场景字段
         * aiUnitField 单位存储字段
         * aiPrices posm的价格字段
         **/
        Map<String, FieldDto> fieldMap = new HashMap<>();
        fieldMap.put("productName",
                new FieldDto("mapping", "productName", 1, "name", "object_reference", "productName", "name",false,null));
        fieldMap.put("aiRowNumber",
                new FieldDto("mapping", "aiRowNumber", 1, "name", "number", "aiRowNumber", "aiRowNumber__c",false,null));
        fieldMap.put("aiGroupNumber",
                new FieldDto("mapping", "aiGroupNumber", 1, "name", "number", "aiGroupNumber", "aiGroupNumber__c",false,null));
        fieldMap.put("aiPath",
                new FieldDto("mapping", "aiPath", 1, "name", "image", "aiPath", "aiPath",false,null));
        ruleDto.setFieldMap(fieldMap);

        arg.setRule(ruleDto);
        arg.setTenantId(tenantId);

        // 执行测试
        SaveAIDetectRuleResult result = aiDetectRuleService.saveRule(arg);

        // 验证结果
        assertEquals(Boolean.TRUE, result.getSuccess());
        System.out.println("result:"+JSON.toJSONString(result));
    }

    @Test
    public void testUpdateRule_Success() {
        // 准备测试数据
        Integer tenantId = 84931;
        Integer userId = 1000;
        String ruleId = "67bc2997c1e320241f920d65";

        UpdateAIDetectRuleArg arg = new UpdateAIDetectRuleArg();
        AIDetectRuleDto ruleDto = ConvertUtil.convertToDto(aiDetectRuleDao.getRuleById(tenantId, ruleId));
        ruleDto.getDetectCapabilityMap().put("123",1);
        ruleDto.setId(ruleId);
        arg.setRule(ruleDto);
        arg.setTenantId(tenantId);
        arg.setUserId(userId);

        // 执行测试
        UpdateAIDetectRuleResult result = aiDetectRuleService.updateRule(arg);

        // 验证结果
        assertEquals(Boolean.TRUE, result.getSuccess());
        System.out.println("result:"+JSON.toJSONString(result));
    }

    @Test
    public void testDeleteRule_Success() {
        // 准备测试数据
        Integer tenantId = 84931;
        Integer userId = 1001;
        String ruleId = "67bc2997c1e320241f920d65";

        DeleteAIDetectRuleArg arg = new DeleteAIDetectRuleArg();
        arg.setTenantId(tenantId);
        arg.setUserId(userId);
        arg.setRuleId(ruleId);

        // 执行测试
        DeleteAIDetectRuleResult result = aiDetectRuleService.deleteRule(arg);

        // 验证结果
        assertEquals(Boolean.TRUE, result.getSuccess());
    }

    @Test
    public void testGetRuleById_Success() {
        // 准备测试数据
        Integer tenantId = 84931;
        Integer userId = 1001;
        String ruleId = "67bc2997c1e320241f920d65";


        // 执行测试
        GetAIDetectRuleArg arg = new GetAIDetectRuleArg();
        arg.setTenantId(tenantId);
        arg.setRuleId(ruleId);
        GetAIDetectRuleResult result = aiDetectRuleService.getRuleById(arg);

        // 验证结果
        assertEquals(Boolean.TRUE, result.getSuccess());

        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testGetRuleList_Success() {
        // 准备测试数据
        Integer tenantId = 84931;
        String modelId = "67b84517d056f06fbe2b1bcf";



        // 执行测试
        GetAIDetectRuleListArg arg = new GetAIDetectRuleListArg();
        arg.setTenantId(tenantId);
        arg.setModelId(modelId);
        GetAIDetectRuleListResult result = aiDetectRuleService.getRuleList(arg);

        // 验证结果
        assertEquals(Boolean.TRUE, result.getSuccess());
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testSaveRule_Exception() {
        // 准备测试数据
        SaveAIDetectRuleArg arg = new SaveAIDetectRuleArg();
        // 不设置必要参数,触发异常

        // 执行测试
        SaveAIDetectRuleResult result = aiDetectRuleService.saveRule(arg);

        // 验证结果
        assertEquals(Boolean.FALSE, result.getSuccess());
    }

    @Test
    public void testQueryRulesByIds() {
        // 准备测试数据
        Integer tenantId = 84931;

        List<String> ruleIds = Lists.newArrayList("67bc2997c1e320241f920d65");

        // 构造请求参数
        QueryRulesByIdsArg arg = new QueryRulesByIdsArg();
        arg.setTenantId(tenantId);
        arg.setRuleIds(ruleIds);

        // 执行测试
        QueryRulesByIdsResult result = aiDetectRuleService.queryRulesByIds(arg);
        System.out.println(JSON.toJSONString(result));
    }
   


}