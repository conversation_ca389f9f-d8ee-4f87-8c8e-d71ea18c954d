package com.facishare.ai.detector.test;

import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.dao.GeneralDAO;
import com.facishare.ai.detector.provider.dao.abstraction.*;
import com.facishare.ai.detector.provider.dao.po.AccountPO;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.dao.po.PricePO;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/10 下午3:32
 */
public class DaoTest extends YoloTest {

    @Resource
    private DetectCounterDao detectCounterDao;

    @Resource
    private PriceDAO priceDAO;

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private ModelDao modelDao;

    @Resource
    private ObjectMapDao objectMapDao;

    @Resource
    private DetectRecordDao detectRecordDao;

    @Resource
    private GeneralDAO generalDAO;

    @Test
    public void testCounter() {

        //detectCounterDao.success(78582,"idcard");
        detectCounterDao.fail(78582, ConstantUtil.ID_CARD_DETECT);
    }

    @Test
    public void testSum() {
        System.out.println(detectCounterDao.sum(78582, ConstantUtil.ID_CARD_DETECT, 1583798400000L, System.currentTimeMillis() + 3600 * 24000));
    }

    @Test
    public void addPrice() {
        PricePO po = new PricePO();

        po.setTenantId(78582);
        po.setModelId("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
        po.setUnitPrice(0.01);
        //priceDAO.insert(po);
    }

    @Test
    public void addAccount() {
        AccountPO po = new AccountPO();
        po.setTenantId(78582);
        po.setBalance(0D);
        //accountDAO.insert(po);
    }

    @Test
    public void addModel() {
        ModelPo po = new ModelPo();
        po.setConfidence(0.8);
        po.setKey("sense_time");
        po.setPlatform("sense_time");
        po.setTokenKey("sense_time_pre");
        po.setTenantId(84931);
        po.setName("商汤模型");
        po.setType("OBJECT_RECOGNITION");
        System.out.println(        modelDao.save(po));
    }

    @Test
    public void addObjectMap() {
        ObjectMapPo po = new ObjectMapPo();
        po.setTenantId(78582);
        po.setModelId("5ee08c4be49907041af6faa5");
        po.setKey("上品桶红烧牛肉面箱_今麦郎_整箱");
        po.setAppId("CRM");
        po.setApiName("ProductObj");
        po.setObjectId("5d3bbf1e7cfed965a54daf70");
        po.setName("一桶半[红烧牛肉]");
        po.setUnit("2");
        po.setColor("ec377f");


        ObjectMapPo po2 = new ObjectMapPo();
        po2.setTenantId(78582);
        po2.setModelId("5ee08c4be49907041af6faa5");
        po2.setKey("上品桶香辣牛肉面箱_今麦郎_整箱");
        po2.setAppId("CRM");
        po2.setApiName("ProductObj");
        po2.setObjectId("5d3bbf1e7cfed965a54daf71");
        po2.setName("一桶半[香辣牛肉]");
        po2.setUnit("2");
        po2.setColor("3dd4c4");

        //objectMapDao.save(po);
        //objectMapDao.save(po2);
    }

    @Test
    public void testDeleteRecord() {
        detectRecordDao.deleteAll(83921, "62fb40bfcb69647054928be1", Lists.newArrayList("N_202304_03_aa60b2022ff745738e9b2b53904601cb", "N_202304_03_517b7370bf7840dab353081c102fe821"));
    }

    @Test
    public void testUpdate() {
        Map<String,Object> contionMap = new HashMap<>();
        Map<String,Object> updateMap = new HashMap<>();
        contionMap.put("K","130100001480");
        updateMap.put("T",0.1);

        System.out.println(generalDAO.update(84931,"com.facishare.ai.detector.provider.dao.po.ObjectMapPo",contionMap,updateMap));
    }
}
