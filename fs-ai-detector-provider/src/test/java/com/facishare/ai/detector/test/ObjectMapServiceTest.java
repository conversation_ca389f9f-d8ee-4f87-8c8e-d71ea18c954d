package com.facishare.ai.detector.test;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import com.facishare.ai.detector.api.dto.arg.BatchAddObjectMapArg;
import com.facishare.ai.detector.api.dto.arg.BatchUpdateObjectMapArg;
import com.facishare.ai.detector.api.dto.arg.QueryObjectMapByIdsArg;
import com.facishare.ai.detector.api.dto.arg.QueryObjectMapByModelIdArg;
import com.facishare.ai.detector.api.dto.result.BatchAddObjectMapResult;
import com.facishare.ai.detector.api.dto.result.BatchUpdateObjectMapResult;
import com.facishare.ai.detector.api.dto.result.QueryObjectMapByModelIdResult;
import com.facishare.ai.detector.api.service.ObjectMapService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * ObjectMapService 测试类
 */
public class ObjectMapServiceTest extends YoloTest {

    @Resource
    private ObjectMapService objectMapService;

    /**
     * 测试批量更新对象映射
     */
    @Test
    public void testBatchUpdateObjectMap() {
        // 准备测试数据
        QueryObjectMapByIdsArg queryObjectMapByIdsArg = new QueryObjectMapByIdsArg();
        queryObjectMapByIdsArg.setTenantId(84931);
        queryObjectMapByIdsArg.setIds(Lists.newArrayList("675acf884baf3d4a5a2abfd4"));
        // 不设置必要参数，应该抛出异常
        List<ObjectMapDto> objectMapList =objectMapService.queryObjectMapByIds(queryObjectMapByIdsArg).getObjectMapList();


        BatchUpdateObjectMapArg arg = new BatchUpdateObjectMapArg();

        arg.setTenantId(84931);
        arg.setObjectMapList(objectMapList);

        objectMapList.forEach(v -> v.setName("demo"));
        // 执行测试
        BatchUpdateObjectMapResult result = objectMapService.batchUpdateObjectMap(arg);

        // 验证结果
        Assert.assertNotNull("更新结果不应为空", result);
        Assert.assertNotNull("更新后的列表不应为空", result.getObjectMapDtoList());
        Assert.assertEquals("更新数量应该匹配", objectMapList.size(), result.getObjectMapDtoList().size());
    }

    /**
     * 测试根据模型ID和租户ID查询对象映射
     */
    @Test
    public void testQueryObjectMapByModelIdAndTenantId() {
        // 准备测试数据
        QueryObjectMapByModelIdArg arg = new QueryObjectMapByModelIdArg();
        arg.setTenantId(84931);
        arg.setModelId("675acd674baf3d4a5a2abfc8");

        // 执行测试
        QueryObjectMapByModelIdResult result = objectMapService.queryObjectMapByModelIdAndTenantId(arg);

        System.out.println("result:" + JSON.toJSONString(result));
        // 验证结果
        Assert.assertNotNull("查询结果不应为空", result);
        Assert.assertNotNull("查询结果列表不应为空", result.getData());

    }


    @Test
    public void testQueryObjectMapByIds() {
        QueryObjectMapByIdsArg arg = new QueryObjectMapByIdsArg();
        arg.setTenantId(84931);
        arg.setIds(Lists.newArrayList("675acf884baf3d4a5a2abfd4"));
        // 不设置必要参数，应该抛出异常
        System.out.println("result:"+JSON.toJSONString(objectMapService.queryObjectMapByIds(arg)));
    }


    @Test
    public void addObjectMap() {
        BatchAddObjectMapArg arg = new BatchAddObjectMapArg();
        arg.setTenantId(84931);
        arg.setMaps(new ArrayList<>());
        ObjectMapDto dto = new ObjectMapDto();
        dto.setTenantId(84931);
        dto.setModelId("675acd674baf3d4a5a2abfc8");
        dto.setKey("demo1");
        dto.setObjectId("645b4569a3d3ee0001907011");
        dto.setApiName("ProductObj");
        arg.getMaps().add(dto);

        // 不设置必要参数，应该抛出异常
        System.out.println("result:"+JSON.toJSONString(objectMapService.batchAddObjectMap(arg)));
    }
} 