package com.facishare.ai.detector.test;

import static org.junit.Assert.*;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.ApplicationDto;
import com.facishare.ai.detector.api.dto.ModelDto;
import com.facishare.ai.detector.api.dto.arg.AddApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetModelByIdArg;
import com.facishare.ai.detector.api.dto.arg.GetModelListArg;
import com.facishare.ai.detector.api.dto.arg.ModelSwitchArg;
import com.facishare.ai.detector.api.dto.arg.OverlayUpdateModelArg;
import com.facishare.ai.detector.api.dto.result.AddApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetModelByIdResult;
import com.facishare.ai.detector.api.dto.result.GetModelListResult;
import com.facishare.ai.detector.api.dto.result.ModelSwitchResult;
import com.facishare.ai.detector.api.dto.result.OverlayUpdateModelResult;
import com.facishare.ai.detector.api.enumeration.ModelSceneEnum;
import com.facishare.ai.detector.api.service.ApplicationService;
import com.facishare.ai.detector.api.service.ModelService;
import com.fxiaoke.api.IdGenerator;

public class ModelTest extends YoloTest {

    @Resource
    private ModelService modelService;

    @Resource
    private ApplicationService applicationService;


    @Test
    public void testAddApplication() {
        AddApplicationArg arg = new AddApplicationArg();
        ApplicationDto applicationDto = new ApplicationDto();
        applicationDto.setId(IdGenerator.get());
        applicationDto.setTenantId(84931);
        applicationDto.setIdentityKey("zijixie");
        applicationDto.setPlatForm("RIO");
        applicationDto.setUseRedis(true);
        Map<String, Object> params = new HashMap<>();
        params.put("token", "1111");
        applicationDto.setParams(params);
        applicationDto.setCreateTime(System.currentTimeMillis());
        applicationDto.setLastModifyTime(System.currentTimeMillis());
        // 设置applicationDto的属性

        arg.setApplication(applicationDto);
        arg.setTenantId(84931);

        AddApplicationResult result = applicationService.addApplication(arg);

        System.out.println("result:" + JSON.toJSONString(result));
    }

    @Test
    public void testGetApplication() {
        GetApplicationArg arg = new GetApplicationArg();
        arg.setIdentityKey("HUAWEI_FSHUAWEICLOUD_EAST_STANDARD");
        arg.setTenantId(78612);

        GetApplicationResult result = applicationService.getApplication(arg);


        System.out.println("result:" + JSON.toJSONString(result));
    }

    @Test
    public void testGetModelById() {
        GetModelByIdArg arg = new GetModelByIdArg();
        String modelId = "6788d5b3b6ab2619671a9a70";
        arg.setModelId(modelId);
        arg.setTenantId(84931);
        arg.setNeedRule(true);
        arg.setNeedObjectMap(true);

        GetModelByIdResult result = modelService.getModelById(arg);

        System.out.println("result:" + JSON.toJSONString(result));

        // 断言结果不为空
        assertNotNull(result);
        // 断言模型DTO不为空
        assertNotNull(result.getModelDto());
        // 断言模型ID与输入一致
        assertEquals(modelId, result.getModelDto().getId());
        // 断言租户ID与输入一致
        assertEquals(Integer.valueOf(84931), result.getModelDto().getTenantId());
    }


    @Test
    public void testModelSwitch() {
        ModelSwitchArg arg = new ModelSwitchArg();
        String modelId = "6788d5b3b6ab2619671a9a70";
        arg.setId(modelId);
        arg.setTenantId(84931);
        arg.setSwitchStatus(0); // 假设1表示开启模型

        ModelSwitchResult result = modelService.modelSwitch(arg);

        System.out.println("result:" + JSON.toJSONString(result));

        // 断言结果不为空
        assertNotNull(result);

    }

    private ModelDto getModelById(Integer tenantId, String modelId) {
        GetModelByIdArg arg = new GetModelByIdArg();
        arg.setModelId(modelId);
        arg.setTenantId(tenantId);
        arg.setNeedRule(true);
        arg.setNeedObjectMap(true);

        GetModelByIdResult result = modelService.getModelById(arg);
        return result.getModelDto();
    }

    @Test
    public void testOverlayUpdate() {
        OverlayUpdateModelArg arg = new OverlayUpdateModelArg();
        Integer tenantId = 78612;
        String modelId = "6788d5b3b6ab2619671a9a70";

        /*ModelDto modelDto = getModelById(tenantId, modelId);
        modelDto.setId(modelId);
        modelDto.setTenantId(tenantId);
        modelDto.setName("Updated Model Name");
        modelDto.setStatus(0);*/
        ModelDto modelDto = JSON.parseObject("{\"createTime\":1739433070229,\"creator\":-10000,\"id\":\"6088d264cb69647054f21f53\",\"key\":\"niupi\",\"lastModifier\":1000,\"lastModifyTime\":1740018778527,\"modelManufacturer\":\"huawei\",\"name\":\"华为SKU模型\",\"params\":{\"url\":\"https://fdd1e570f187404fa894e30ce82651e1.apig.cn-east-3.huaweicloudapis.com/v1/infers/5f462257-8029-4df7-8f34-20df4cffad2d/v1/image/shelf-sku-recognition\"},\"platform\":\"huawei_sku_poc\",\"scene\":\"display\",\"status\":1,\"tenantId\":78612,\"tokenKey\":\"HUAWEI_FSHUAWEICLOUD_EAST_STANDARD\",\"type\":\"OBJECT_RECOGNITION\"}",ModelDto.class);
        arg.setModel(modelDto);
        arg.setTenantId(tenantId);

        OverlayUpdateModelResult result = modelService.overlayUpdate(arg);

        System.out.println("result:" + JSON.toJSONString(result));

        // 断言结果不为空
        assertNotNull(result);
        // 断言模型DTO不为空

    }

    @Test
    public void testGetModelList() {
        Integer tenantId = 78612;

        GetModelListArg arg = new GetModelListArg();
        arg.setTenantId(tenantId);
        arg.setScene(ModelSceneEnum.DISPLAY.getValue());

        GetModelListResult result = modelService.getModelList(arg);

        System.out.println("result:" + JSON.toJSONString(result));

        // 断言结果不为空
        assertNotNull(result);
        // 断言模型列表不为空
        assertNotNull(result.getModels());
        // 断言模型列表中至少有一个模型
        assertFalse(result.getModels().isEmpty());
        // 断言第一个模型的租户ID正确
    }

}
