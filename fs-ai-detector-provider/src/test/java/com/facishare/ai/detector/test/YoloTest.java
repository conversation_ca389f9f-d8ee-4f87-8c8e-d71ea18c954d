package com.facishare.ai.detector.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
        "classpath:application-context.xml"
})
public class YoloTest {
    static {
        System.setProperty("process.profile", "fstest");
        System.setProperty("isLocal","yes");
    }

    @Test
    public void Test() {

    }
}
