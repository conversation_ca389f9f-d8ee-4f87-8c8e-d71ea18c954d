<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	    http://code.alibabatech.com/schema/dubbo
	    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <context:component-scan base-package="com.facishare.ai.detector.provider.dao,com.facishare.ai.detector.provider.adapter.model"/>
    <context:annotation-config/>

    <import resource="classpath:spring/dao.xml"/>

    <bean id="baiDuAdapter" class="com.facishare.ai.detector.provider.adapter.service.detect.BaiduDetectorImpl"/>
    <bean id="baiduLocalClassifier" class="com.facishare.ai.detector.provider.adapter.service.classfy.BaiduLocalClassifierImpl"/>
    <bean id="baiduClassifier" class="com.facishare.ai.detector.provider.adapter.service.classfy.BaiduClassifierImpl"/>
   <!-- <bean id="alibabaDetector" class="com.facishare.ai.detector.provider.adapter.service.detect.AlibabaDetectorImpl"/>-->
    <bean id="huaweiClassifier" class="com.facishare.ai.detector.provider.adapter.service.classfy.HuaWeiClassifierImpl"/>
</beans>
