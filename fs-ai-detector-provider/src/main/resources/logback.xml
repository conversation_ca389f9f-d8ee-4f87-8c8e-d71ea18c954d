<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <property name="APP_Name" value="biz"/>
    <contextName>${APP_Name}</contextName>

    <appender name="InfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/info.log</File>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/info-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>19</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} traceId=%X{traceId} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--错误输出-->
    <appender name="ErrorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <File>${catalina.home}/logs/error.log</File>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/error-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>19</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} traceId=%X{traceId} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="ErrorLog" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ErrorFile"/>
    </appender>


    <!--错误输出-->
    <appender name="AccessLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/api_access.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/api_access-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>10</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="AccessLog" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="AccessLogFile"/>
    </appender>

    <!-- Trace Log  -->
    <appender name="TraceLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/trace.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/trace.%d{yyyy-MM-dd}.log</FileNamePattern>
            <maxHistory>1</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!-- Dubbo -->
    <appender name="Dubbo" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${catalina.home}/logs/dubbo.log</File>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/dubbo.%d{yyyy-MM-dd}.log</FileNamePattern>
            <maxHistory>1</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
    </appender>

    <!--ROOT-->
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <!-- 可让每天产生一个日志文件，最多 7 个，自动回滚 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalina.home}/logs/${APP_Name}-%d{yyyy-MM-dd}.log.zip</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- 异步输出 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="RollingFile"/>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%t] %-5level %logger{36}:%L - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.github.trace" level="DEBUG" additivity="false">
        <appender-ref ref="TraceLog"/>
    </logger>

    <logger name="com.alibaba.dubbo" level="DEBUG" additivity="false">
        <appender-ref ref="Dubbo"/>
    </logger>

    <logger name="com.facishare" level="INFO" additivity="false">
        <appender-ref ref="ErrorLog"/>
        <appender-ref ref="InfoLog"/>
    </logger>

    <logger name="com.fxiaoke.metrics" level="DEBUG"/>

    <root level="DEBUG">
        <appender-ref ref="ASYNC"/>
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
