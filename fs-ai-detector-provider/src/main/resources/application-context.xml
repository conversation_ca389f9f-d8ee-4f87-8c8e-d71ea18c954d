<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	    http://code.alibabatech.com/schema/dubbo
	    http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <mvc:annotation-driven>
        <mvc:argument-resolvers>
            <bean class="com.facishare.ai.detector.provider.resolver.FMCGRequestResolver"/>
        </mvc:argument-resolvers>
    </mvc:annotation-driven>

    <context:component-scan base-package="com.facishare.ai"/>
    <context:annotation-config/>
    <import resource="classpath:fmcg-sdk-ai.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/dao.xml"/>
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml"/>
    <!--<import resource="classpath:spring/service-aop.xml"/>-->

    <bean id="yoloDetector" class="com.facishare.ai.detector.provider.adapter.service.detect.YoloDetectorImpl"/>
    <bean id="baiduDetector" class="com.facishare.ai.detector.provider.adapter.service.detect.BaiduDetectorImpl"/>
    <bean id="pinLanDetector" class="com.facishare.ai.detector.provider.adapter.service.detect.PinLanDetectorImpl"/>
    <bean id="baiduLocalClassifier"
          class="com.facishare.ai.detector.provider.adapter.service.classfy.BaiduLocalClassifierImpl"/>
    <bean id="baiduClassifier" class="com.facishare.ai.detector.provider.adapter.service.classfy.BaiduClassifierImpl"/>
    <bean id="alibabaDetector" class="com.facishare.ai.detector.provider.adapter.service.detect.AlibabaDetectorImpl"/>
    <bean id="huaweiClassifier"
          class="com.facishare.ai.detector.provider.adapter.service.classfy.HuaWeiClassifierImpl"/>
    <bean id="baiduOfficialRetailDetector"
          class="com.facishare.ai.detector.provider.adapter.service.detect.BaiduOfficialRetailDetectorImpl"/>
    <bean id="baiduCustomizedClassifier"
          class="com.facishare.ai.detector.provider.adapter.service.classfy.BaiduCustomizedClassifierImpl"/>
    <bean id="langJingClassifier"
          class="com.facishare.ai.detector.provider.adapter.service.classfy.LangJingClassifierImpl"/>


    <bean id="detectorService" class="com.facishare.ai.detector.provider.service.DetectorServiceImpl"/>
    <bean id="classifierService" class="com.facishare.ai.detector.provider.service.ClassifierServiceImpl"/>
    <bean id="modelService" class="com.facishare.ai.detector.provider.service.ModelServiceImpl"/>
    <bean id="objectMapService" class="com.facishare.ai.detector.provider.service.ObjectMapServiceImpl"/>
    <bean id="sdkService" class="com.facishare.ai.detector.provider.service.SdkServiceImpl"/>

    <bean id="baiduFaceDetector" class="com.facishare.ai.detector.provider.adapter.service.face.BaiduFaceDetectorImpl"/>
    <bean id="ocrService" class="com.facishare.ai.detector.provider.service.OcrServiceImpl"/>
    <bean id="detectCounterService" class="com.facishare.ai.detector.provider.service.DetectCounterServiceImpl"/>

    <bean id="baiduOcrDetector" class="com.facishare.ai.detector.provider.adapter.service.ocr.BaiduOcrDetectorImpl"/>

    <bean id="organizationAdapter"
          class="com.facishare.ai.detector.provider.adapter.organization.OrganizationAdapterImpl"/>


    <bean id="redisCmd" class="com.github.jedis.support.JedisFactoryBean" p:configName="checkins-v2-redis"/>

    <bean id="qXEIEAConverter" class="com.facishare.qixin.converter.QXEIEAConverterImpl"/>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <bean id="springContextHolder" class="com.facishare.ai.detector.provider.adapter.SpringContextHolder"
          lazy-init="false"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="fs-ai-detector-config"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>
    <import resource="classpath:fmcg-framework-http.xml"/>

    <bean id="client" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-fmcg-framework-config"/>

    <dubbo:registry address="${zookeeper.address}"/>
    <dubbo:application name="fs-ai-detector" logger="slf4j"/>
    <dubbo:protocol port="12075"/>
    <dubbo:reference id="employeeService" interface="com.facishare.organization.adapter.api.service.EmployeeService"
                     timeout="10000"/>
    <dubbo:reference id="departmentService" interface="com.facishare.organization.adapter.api.service.DepartmentService"
                     timeout="10000"/>

    <dubbo:reference id="sharedFileService" interface="com.facishare.fsc.api.service.SharedFileService"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.DetectorService" ref="detectorService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.ClassifierService" ref="classifierService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.FaceDetectService" ref="faceDetectService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.OcrService" ref="ocrService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.AICostService" ref="aiCostService"
                   protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.ModelService" ref="modelService" protocol="dubbo" version="1.0.1"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.ApplicationService" ref="applicationService" protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.ObjectMapService" ref="objectMapService" protocol="dubbo"/>
    <dubbo:service interface="com.facishare.ai.detector.api.service.AIDetectRuleService" ref="aiDetectRuleService" protocol="dubbo"/>


    <dubbo:consumer filter="tracerpc"/>
    <dubbo:provider filter="tracerpc"/>
</beans>
