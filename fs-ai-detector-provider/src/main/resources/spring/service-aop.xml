<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd


        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">
    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>

    <aop:config>
        <aop:aspect ref="serviceProfiler">
            <aop:around method="profile"
                        pointcut="execution(* com.facishare.ai.detector.provider.service.*ServiceImpl.*(..))"/>
        </aop:aspect>
    </aop:config>
</beans>
