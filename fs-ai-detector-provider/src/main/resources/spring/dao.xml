<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <bean id="dbContext" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="fs-fmcg-mongo"/>
    <bean id="modelDao" class="com.facishare.ai.detector.provider.dao.ModelDaoImpl"/>
    <bean id="objectMapDao" class="com.facishare.ai.detector.provider.dao.ObjectMapDaoImpl"/>
    <bean id="detectRecordDao" class="com.facishare.ai.detector.provider.dao.DetectRecordDaoImpl"/>
    <bean id="classifyRecordDao" class="com.facishare.ai.detector.provider.dao.ClassifyRecordDaoImpl"/>
    <bean id="faceInfoDao" class="com.facishare.ai.detector.provider.dao.FaceInfoDaoImpl"/>
    <bean id="detectCounterDao" class="com.facishare.ai.detector.provider.dao.DetectCounterDaoImpl"/>
</beans>
