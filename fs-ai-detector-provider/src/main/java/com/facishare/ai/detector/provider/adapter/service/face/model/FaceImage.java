package com.facishare.ai.detector.provider.adapter.service.face.model;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 19-12-20  下午2:36
 */
@Data
@ToString
public class FaceImage {

    /**
     * BASE64,URL,FACE_TOKEN  必填
     */
    private String type;

    /**
     * 对应type  必填
     */
    private String image;

    /**
     * LIVE:生活照,IDCARD,WATERMARK:水印证件,CERT:证件照 默认LIVE
     */
    private String faceType;

    /**
     * NONE,LOW,NORMAL,HIGH.质量检测需求 默认NONE
     */
    private String qualityControl;

    /**
     * NONE,LOW,NORMAL,HIGH.活体检测需求 默认NONE
     */
    private String livenessControl;
}
