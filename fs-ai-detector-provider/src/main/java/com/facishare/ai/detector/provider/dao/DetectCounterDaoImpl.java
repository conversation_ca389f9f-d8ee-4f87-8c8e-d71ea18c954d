package com.facishare.ai.detector.provider.dao;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.po.DetectCounterPo;
import lombok.Data;
import org.mongodb.morphia.aggregation.AggregationPipeline;
import org.mongodb.morphia.aggregation.Group;
import org.mongodb.morphia.annotations.Property;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.Iterator;

import static org.mongodb.morphia.aggregation.Group.grouping;

/**
 * <AUTHOR>
 * @date 2020/3/10 下午2:49
 */
public class DetectCounterDaoImpl extends DaoBase<DetectCounterPo> implements DetectCounterDao {
    @Override
    public void success(int tenantId,String service) {
       long zero = System.currentTimeMillis()/(3600000*24)*(3600000*24);
        success(tenantId,service,zero,(3600000*24)+zero);
    }

    @Override
    public void fail(int tenantId,String service) {
        long zero = System.currentTimeMillis()/(3600000*24)*(3600000*24);
        fail(tenantId,service,zero,(3600000*24)+zero);
    }

    @Override
    public void success(int tenantId, String service,long start, long end) {
        Query<DetectCounterPo> query = dbContext.createQuery(DetectCounterPo.class);
        query.field(DetectCounterPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectCounterPo.F_START).greaterThanOrEq(start);
        query.field(DetectCounterPo.F_END).lessThanOrEq(end);
        query.field(DetectCounterPo.F_SERVICE).equal(service);
        UpdateOperations<DetectCounterPo> updateOperations = dbContext.createUpdateOperations(DetectCounterPo.class);
        updateOperations.inc(DetectCounterPo.F_SUCCESS,1);
        updateOperations.set(DetectCounterPo.F_START,start);
        updateOperations.set(DetectCounterPo.F_END,end);
        updateOperations.set(DetectCounterPo.F_SERVICE,service);
        dbContext.update(query,updateOperations,true);
    }

    @Override
    public void fail(int tenantId, String service,long start, long end) {
        Query<DetectCounterPo> query = dbContext.createQuery(DetectCounterPo.class);
        query.field(DetectCounterPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectCounterPo.F_START).greaterThanOrEq(start);
        query.field(DetectCounterPo.F_END).lessThanOrEq(end);
        query.field(DetectCounterPo.F_SERVICE).equal(service);
        UpdateOperations<DetectCounterPo> updateOperations = dbContext.createUpdateOperations(DetectCounterPo.class);
        updateOperations.inc(DetectCounterPo.F_FAIL,1);
        updateOperations.set(DetectCounterPo.F_START,start);
        updateOperations.set(DetectCounterPo.F_END,end);
        updateOperations.set(DetectCounterPo.F_SERVICE,service);
        dbContext.update(query,updateOperations,true);
    }

    @Override
    public DetectCounterPo sum(int tenantId, String service,long start, long end) {
        Query<DetectCounterPo> query = dbContext.createQuery(DetectCounterPo.class);
        query.field(DetectCounterPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectCounterPo.F_START).greaterThanOrEq(start);
        query.field(DetectCounterPo.F_END).lessThanOrEq(end);
        query.field(DetectCounterPo.F_SERVICE).equal(service);
        AggregationPipeline aggregationPipeline = dbContext.createAggregation(DetectCounterPo.class);
        aggregationPipeline.match(query).group(Group.id(),grouping(DetectCounterPo.F_SUCCESS,Group.sum(DetectCounterPo.F_SUCCESS)),grouping(DetectCounterPo.F_FAIL,Group.sum(DetectCounterPo.F_FAIL)));
        Iterator<Item> iter= aggregationPipeline.aggregate(Item.class);
        DetectCounterPo po = new DetectCounterPo();
        if(iter.hasNext()){
            Item item = iter.next();
            po.setSuccess(item.getSuccess());
            po.setFail(item.getFail());
        }else {
            po.setSuccess(0);
            po.setFail(0);
        }
        po.setTenantId(tenantId);
        po.setStart(start);
        po.setEnd(end);
        po.setService(service);
        return po;
    }

    @Data
    static class Item{
        @Property(DetectCounterPo.F_SUCCESS)
        int success;
        @Property(DetectCounterPo.F_FAIL)
        int fail;
    }
}
