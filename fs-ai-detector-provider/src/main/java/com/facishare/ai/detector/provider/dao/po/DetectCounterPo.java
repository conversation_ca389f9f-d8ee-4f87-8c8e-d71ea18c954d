package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 * @date 2020/3/4 下午2:58
 */
@Data
@ToString
@Entity(value = "ai_detect_count",noClassnameStored = true)
public class DetectCounterPo extends MongoPOBase {

    public static final String F_SUCCESS="S";
    public static final String F_FAIL="F";
    public static final String F_START="ST";
    public static final String F_END="ED";
    public static final String F_SERVICE="SV";


    @Property(F_SUCCESS)
    private Integer success;

    @Property(F_FAIL)
    private Integer fail;

    @Property(F_SERVICE)
    private String service;

    @Property(F_START)
    private Long start;

    @Property(F_END)
    private Long end;

}
