package com.facishare.ai.detector.provider.dao.po;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Entity(value = "ai_object_map", noClassnameStored = true)
public class ObjectMapPo extends MongoPOBase {

    public static final String F_MODEL_ID = "MI";
    public static final String F_KEY = "K";
    public static final String F_API_NAME = "AN";
    public static final String F_OBJECT_ID = "OI";
    public static final String F_NAME = "N";
    public static final String F_APP_ID = "AI";
    public static final String F_COLOR = "C";
    public static final String F_UNIT = "UN";
    public static final String F_THRESHOLD = "T";
    public static final String F_EXTRA_DATA = "ED";



    @Property(F_MODEL_ID)
    private String modelId;

    @Property(F_KEY)
    private String key;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_OBJECT_ID)
    private String objectId;

    @Property(F_UNIT)
    private String unit;

    @Property(F_NAME)
    private String name;

    @Property(F_APP_ID)
    private String appId;

    @Property(F_COLOR)
    private String color;

    @Property(F_THRESHOLD)
    private Double threshold;

    @Property(F_EXTRA_DATA)
    private JSONObject extraData;
}
