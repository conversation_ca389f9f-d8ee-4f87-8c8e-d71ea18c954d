package com.facishare.ai.detector.provider.adapter.service.abstraction;

import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.FacadeDetectAO;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.IdCardDetectAO;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.VATInvoiceDetectAO;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:17
 */
public interface OcrDetector {


    /**
     * @param image           二进制图片
     * @param cardSide        front/back 必填 正面或背面
     * @param detectDirection 默认为true 是否为检测旋转方向 90度为一个单位
     * @param detectRisk      默认为 false 是否为翻拍和复印件
     * @param detectPhoto     默认为false 是否检测头像
     * @param detectRectify   默认为false 是否检测完整性
     * @return
     */
    IdCardDetectAO idCardDetect(byte[] image, String cardSide, String detectDirection, String detectRisk, String detectPhoto, String detectRectify) throws AiProviderException, IOException;

    /**
     * @param image    二进制图片
     * @param accuracy normal（默认配置）对应普通精度模型，识别速度较快，在四要素的准确率上和 high 模型保持一致
     *                 ，high对应高精度识别模型，相应的时延会增加，因为超时导致失败的情况也会增加（错误码282000）
     * @param type     进行识别的增值税发票类型，默认为 normal，可缺省
     *                 - normal：可识别增值税普票、专票、电子发票
     *                 - roll：可识别增值税卷票
     * @return
     */
    VATInvoiceDetectAO VATInvoiceDetect(byte[] image, String accuracy, String type) throws AiProviderException, IOException;

    FacadeDetectAO facadeDetect(byte[] image) throws AiProviderException, IOException;
}
