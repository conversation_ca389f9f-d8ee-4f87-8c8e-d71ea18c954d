package com.facishare.ai.detector.provider.adapter.service.ocr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:32
 */
@Data
@ToString
public class VATInvoiceDetectAO {


    private int errorCode;
    private String errorMsg= ConstantUtil.SUCCESS;

    @JSONField(name = "InvoiceType")
    private String invoiceType;// 发票种类

    @JSONField(name = "InvoiceTypeOrg")
    private String invoiceTypeOrg;// 发票名称

    @JSONField(name = "InvoiceCode")
    private String invoiceCode;// 发票代码

    @JSONField(name = "InvoiceNum")
    private String invoiceNum;// 发票号码

    @JSONField(name = "CheckCode")
    private String checkCode;//校验码

    @JSONField(name = "InvoiceDate")
    private String invoiceDate;// 开票日期

    @JSONField(name = "PurchaserName")
    private String purchaserName;// 购方名称

    @JSONField(name = "PurchaserRegisterNum")
    private String purchaserRegisterNum;// 购方纳税人识别号

    @JSONField(name = "PurchaserAddress")
    private String purchaserAddress;// 购方地址及电话

    @JSONField(name = "PurchaserBank")
    private String purchaserBank;// 购方开户行及账号

    @JSONField(name = "Password")
    private String password;// 密码区

    private List<Item> products;

    @JSONField(name = "SellerName")
    private String sellerName;// 销售方名称

    @JSONField(name = "SellerRegisterNum")
    private String sellerRegisterNum;// 销售方纳税人识别号

    @JSONField(name = "SellerAddress")
    private String sellerAddress;// 销售方地址及电话

    @JSONField(name = "SellerBank")
    private String sellerBank;// 销售方开户行及账号

    @JSONField(name = "TotalAmount")
    private double totalAmount;//合计金额

    @JSONField(name = "TotalTax")
    private double totalTax;//合计税额

    @JSONField(name = "AmountInWords")
    private String amountInWords;// 价税合计(大写)

    @JSONField(name = "AmountInFiguers")
    private double amountInFiguers;//价税合计(小写)

    @JSONField(name = "Payee")
    private String payee;// 收款人

    @JSONField(name = "Checker")
    private String checker;// 复核

    @JSONField(name = "NoteDrawer")
    private String noteDrawer;// 开票人

    @JSONField(name = "Remarks")
    private String remarks;// 备注

    @Data
    @ToString
    public static class Item{
        @JSONField(name = "CommodityName")
        private String commodityName;// 货物名称

        @JSONField(name = "CommodityType")
        private String commodityType;// 规格型号

        @JSONField(name = "CommodityUnit")
        private String commodityUnit;// 单位

        @JSONField(name = "CommodityNum")
        private String commodityNum;// 数量

        @JSONField(name = "CommodityPrice")
        private String commodityPrice;// 单价

        @JSONField(name = "CommodityAmount")
        private String commodityAmount;// 金额

        @JSONField(name = "CommodityTaxRate")
        private String commodityTaxRate;// 税率

        @JSONField(name = "CommodityTax")
        private String commodityTax;// 税额
    }
}
