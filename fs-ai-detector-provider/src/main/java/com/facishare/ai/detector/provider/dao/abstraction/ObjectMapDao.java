package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ObjectMapDao {

    List<ObjectMapPo> query(Integer tenantId, String modelId);

    String save(ObjectMapPo po);

    void batchAdd(List<ObjectMapPo> pos);

    Set<String> checkContainsIds(Integer tenantId, List<String> ids);

    List<ObjectMapPo> queryNullUniqueIdPo(int limit);

    List<ObjectMapPo> queryByUniqueId(Integer tenantId, String modelId, List<String> uniqueIds);

    Integer deleteByUniqueId(Integer tenantId, String modelId, List<String> uniqueIds);

    Integer deleteByKeys(Integer tenantId, String modelId, List<String> keys);

    List<ObjectMapPo> queryByKeys(Integer tenantId, String modelId, List<String> keys);

    Integer updateWithMap(Integer tenantId, String id, Map<String, Object> map);



    /**
     * 批量更新ObjectMap
     * @param pos 要更新的ObjectMap列表
     * @param tenantId 租户ID
     * @return 更新后的ObjectMap列表
     */
    List<ObjectMapPo> batchUpdate(List<ObjectMapPo> pos, Integer tenantId);

    /**
     * 根据企业ID和数据ID列表查询对象映射
     * @param tenantId 企业ID
     * @param ids 数据ID列表
     * @return 对象映射列表
     */
    List<ObjectMapPo> queryByTenantIdAndIds(Integer tenantId,List<String> ids);
}
