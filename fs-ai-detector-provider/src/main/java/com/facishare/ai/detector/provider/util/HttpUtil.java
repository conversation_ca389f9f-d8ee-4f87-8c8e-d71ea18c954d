package com.facishare.ai.detector.provider.util;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.net.InetAddresses;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 19-8-1  下午4:45
 */
public class HttpUtil {

    private static Logger log = LoggerFactory.getLogger(HttpUtil.class);

    private static final OkHttpClient CLIENT;

    private static final MediaType JSON_MEDIA = MediaType.parse("application/json;charset=utf-8");

    private static final MediaType JPG_MEDIA = MediaType.parse("image/jpg");

    private static final MediaType FORM_MEDIA = MediaType.parse("application/x-www-form-urlencoded");

    private static final String CONFIG_NAME = "fs-fmcg-adapter-apis";
    private static final String ENTERPRISE_ID_HEADER = "x-fs-ei";
    private static final String EMPLOYEE_ID_HEADER = "x-fs-userInfo";
    private static final List<Proxy> myProxy = Lists.newArrayList(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("egress-proxy.egress", 9999)));
    private static final List<Proxy> noProxy = Lists.newArrayList(Proxy.NO_PROXY);
    private static final List<String> noProxyHosts = Lists.newArrayList(".foneshare", ".intranet.fxiaoke.com", ".fxiaokeyun", ".firstshare");

    static {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(96);
        dispatcher.setMaxRequestsPerHost(192);
        ConnectionPool defaultPool = new ConnectionPool(20, 10L, TimeUnit.MINUTES);

        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.setDispatcher$okhttp(dispatcher);
        builder.readTimeout(10000, TimeUnit.MILLISECONDS);
        builder.connectTimeout(10000, TimeUnit.MILLISECONDS);
        builder.writeTimeout(10000, TimeUnit.MILLISECONDS);
        builder.setConnectionPool$okhttp(defaultPool);
        builder.setProxySelector$okhttp(new ProxySelector() {
            @Override
            public List<Proxy> select(URI uri) {
                if (!Strings.isNullOrEmpty(System.getProperty("isLocal")))
                    return noProxy;
                if (useProxy(uri.getHost())) {
                    log.debug("access with proxy, address: {}", uri.toString());
                    return myProxy;
                }
                log.debug("access without proxy, address: {}", uri.toString());
                return noProxy;
            }

            @Override
            public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
                throw new RuntimeException("connect failed! proxy: " + sa.toString() + ", url: " + uri.toASCIIString(), ioe);
            }
        });
        builder.setRetryOnConnectionFailure$okhttp(false);
        builder.addInterceptor(new RetryInterceptor(1));
        CLIENT = builder
                .build();

        ConfigFactory.getConfig(CONFIG_NAME, (config -> {
            String str = config.get("proxyList");
            String env = System.getProperty("process.profile", System.getenv("process.profile"));
            if (env.equals("foneshare-gray")) {
                return;
            }
            if (Strings.isNullOrEmpty(str)) {
                return;
            }
            List<String> proxyList = JSON.parseArray(str, String.class);
            if (CollectionUtils.isEmpty(proxyList)) {
                return;
            }
            noProxyHosts.clear();
            noProxyHosts.addAll(proxyList);
        }));
    }

    public static <T> Object post(String url, Map<String, String> headers, T data) throws AiProviderException {
        Request.Builder builder = new Request.Builder().url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        builder.addHeader("Content-Type", "application/json");
        RequestBody requestBody = RequestBody.create(JSON_MEDIA, JSON.toJSONString(data));
        Request request = builder.post(requestBody).build();
        try {
            Response response = CLIENT.newCall(request).execute();
            if (response.code() != 200) {
                log.info("http fail response:code{},msg:{},headers:{}", response.code(), response.message(), request.headers());
            }
            String body = response.body().string();
            return JSON.parse(body);
        } catch (IOException e) {
            String dataS = data.toString();
            log.error("post data fail.url:{},data:{},header:{},e:{}", url, dataS.substring(0, Math.min(dataS.length(), 100)), headers, e);
            throw new AiProviderException("100500", e.getMessage());
        }
    }


    public static Object get(String url, Map<String, String> headers) throws AiProviderException {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        headers.forEach(builder::addHeader);
        try {
            Response response = CLIENT.newCall(builder.build()).execute();
            String body = response.body().string();
            return JSON.parse(body);
        } catch (IOException e) {
            log.error("get data fail.url:{},e:{}", url, e);
            throw new AiProviderException("100500", e.getMessage());
        }
    }

    public static Object form_post(String url, Map<String, String> headers, String data) throws AiProviderException {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        builder.addHeader("Content-type", "application/x-www-form-urlencoded");
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        RequestBody body = RequestBody.create(FORM_MEDIA, data);
        builder.post(body);
        try {
            Response response = CLIENT.newCall(builder.build()).execute();
            String json = response.body().string();
            return JSON.parse(json);
        } catch (Exception e) {
            log.error("request url:{}.data:{}.e:{}", url, data.substring(0, Math.min(data.length(), 100)), e);
            throw new AiProviderException("100500", e.getMessage());
        }
    }

    public static <T, R> R multiFromFilePost(String url, Map<String, Object> headers, String key, String fileName, byte[] bytes, Class<R> clazz) throws AiProviderException {
        Request.Builder builder = new Request.Builder();
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((k, v) -> {
                builder.addHeader(k, v.toString());
            });
        }

        RequestBody fileBody = RequestBody.create(MediaType.parse("application/octet-stream"), bytes);
        RequestBody requestBody = (new okhttp3.MultipartBody.Builder()).setType(MultipartBody.FORM).addFormDataPart(key, fileName, fileBody).build();
        Request request = builder.url(url).post(requestBody).build();

        try {
            Response response = CLIENT.newCall(request).execute();
            if (response.code() != 200) {
                log.info("http fail response:code{},msg:{}", response.code(), response.message());
                throw new AiProviderException(String.valueOf(response.code()), response.message());
            } else {
                return JSON.parseObject(response.body().string(), clazz);
            }
        } catch (IOException | AiProviderException var11) {
            log.error("request url:{}.", url, var11);
            throw new AiProviderException("100500", var11.getMessage());
        }
    }

    public static <T> Headers head(String url, Map<String, String> headers, T data) {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        builder.addHeader("Content-Type", "application/json");
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        RequestBody requestBody = RequestBody.create(JSON_MEDIA, JSON.toJSONString(data));
        Request request = builder.post(requestBody).build();
        try {
            Response response = CLIENT.newCall(request).execute();
            return response.headers();
        } catch (IOException e) {
            log.error("request url:{} fail.", url);
        }
        return null;
    }

    public static <T, R> R post(String url, Map<String, Object> headers, T arg, Class<R> clazz) throws IOException {
        Request.Builder builder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
        }
        Request request = builder.url(url).post(RequestBody.create(JSON_MEDIA, arg == null ? "{}" : JSON.toJSONString(arg))).build();
        Response response = CLIENT.newCall(request).execute();
        String json = response.body().string();
        return JSON.parseObject(json, clazz);
    }

    public static <R> R get(String url, Map<String, Object> headers, Class<R> clazz) throws IOException {
        Request.Builder builder = new Request.Builder();
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach((k, v) -> builder.addHeader(k, v.toString()));
        }
        Request request = builder.url(url).get().build();
        Response response = CLIENT.newCall(request).execute();
        return JSON.parseObject(response.body().string(), clazz);
    }

    public static Map<String, Object> initMetadataHeader(Integer tenantId, Integer userId) {
        Map<String, Object> header = new HashMap<>(2);
        header.put(ENTERPRISE_ID_HEADER, tenantId.toString());
        header.put(EMPLOYEE_ID_HEADER, userId.toString());
        if (!Strings.isNullOrEmpty(System.getProperty("app.name"))) {
            header.put("x-fs-peer-name", System.getProperty("app.name"));
            header.put("x-peer-name", System.getProperty("app.name"));
        }
        return header;
    }

    public static Map<String, Object> initMetadataHeader(Integer tenantId) {
        Map<String, Object> header = new HashMap<>(2);
        header.put(ENTERPRISE_ID_HEADER, tenantId.toString());
        header.put(EMPLOYEE_ID_HEADER, "-10000");
        if (!Strings.isNullOrEmpty(System.getProperty("app.name"))) {
            header.put("x-fs-peer-name", System.getProperty("app.name"));
            header.put("x-peer-name", System.getProperty("app.name"));
        }
        return header;
    }

    public static String initUrl(String key) {
        return ConfigFactory.getConfig(CONFIG_NAME).get(key);
    }

    public static String initUrl(String key, Object... parameters) {
        String url = ConfigFactory.getConfig(CONFIG_NAME).get(key);
        return url != null ? String.format(url, parameters) : null;
    }


    private static boolean useProxy(String host) {
        try {
            // k8s集群内部的访问地址不使用代理。k8s内部域名地址基本上最多只有两级：app, app.namespace
            if (StringUtils.countMatches(host, '.') < 2) {
                return false;
            }
            // 私有ip不使用代理
            if (InetAddresses.isInetAddress(host)) {
                InetAddress inetAddress = InetAddresses.forString(host);
                if (inetAddress.isSiteLocalAddress() || inetAddress.isLoopbackAddress()) {
                    return false;
                }
            }
            for (String it : noProxyHosts) {
                if (host.contains(it)) {
                    return false;
                }
            }
        } catch (RuntimeException e) {
            log.warn("Can't decide whether to use proxy, return default value! ", e);
        }
        return true;
    }
}
