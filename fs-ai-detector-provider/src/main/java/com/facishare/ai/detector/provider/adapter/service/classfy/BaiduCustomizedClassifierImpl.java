package com.facishare.ai.detector.provider.adapter.service.classfy;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-8-6  下午3:07
 */
@SuppressWarnings("Duplicates")
public class BaiduCustomizedClassifierImpl implements Classifier {

    private Logger log = LoggerFactory.getLogger(BaiduCustomizedClassifierImpl.class);

    @Autowired
    private TokenFactory tokenFactory;

    @Override
    public List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        JSONObject data = new JSONObject();
        data.put("image", Base64.encodeBase64String(imageCache));
        //返回的分类的数量 默认6个 非必填
        data.put("top_num", 6);
        String tokenKey = Strings.isNullOrEmpty(model.getTokenKey())? AppEnum.BAIDU_FXIAOKERD_EASYDL.value():model.getTokenKey();
        String accessToken = tokenFactory.getToken(tokenKey);
        String url = String.format(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.BAIDU_OFFICIAL_RETAIL),model.getKey(),accessToken);
        JSONObject result = (JSONObject) HttpUtil.post(url, null, data);

        log.info("classification finished. {}", result);

        List<ClassDto> classes = new ArrayList<>();

        if (result != null && result.containsKey("error_code")) {
            data.put("image","");
            log.error("call baidu api err .arg:{},msg:{}", arg,result.getString("error_msg"));
            throw new AiProviderException("AI","baidu api fail."+ result.getString("error_msg"));
        }
        if (result != null && result.getJSONArray("results") != null) {
            result.getJSONArray("results").forEach(v -> {
                ClassDto dto = new ClassDto();
                JSONObject o = (JSONObject) v;
                dto.setClassName(o.getString("name"));
                dto.setConfidence(o.getDouble("score"));
                classes.add(dto);
            });
        } else {
            log.warn("call baidu api but return null.arg:{}", data);
            throw new AiProviderException("AI","call baidu api but return null.");
        }
        return classes;
    }
}
