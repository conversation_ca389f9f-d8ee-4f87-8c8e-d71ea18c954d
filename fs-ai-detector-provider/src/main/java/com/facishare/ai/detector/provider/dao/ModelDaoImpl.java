package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */

public class ModelDaoImpl extends DaoBase<ModelPo> implements ModelDao {


    @Override
    public String save(ModelPo po) {
        checkApiNameExist(po.getTenantId(), po.getApiName(), po.getUniqueId());
        checkNameExist(po.getTenantId(), po.getName(), po.getUniqueId());
        return super.save(po);
    }

    private void checkNameExist(Integer tenantId,  String name, String id) {
        Query<ModelPo> query = dbContext.createQuery(ModelPo.class);
        query.field(ModelPo.F_TENANT_ID).equal(tenantId);
        query.field(ModelPo.F_NAME).equal(name);
        query.field(ModelPo.F_IS_DELETED).equal(false);
        if(!Strings.isNullOrEmpty(id)){
            query.field(MongoPOBase.F_UNIQUE_ID).notEqual(id);
        }
        if (query.get() != null) {
            throw new AiProviderException("7125", "存在重复的模型名称。");
        }
    }

    private void checkApiNameExist(Integer tenantId,  String apiName, String id) {
        if (Strings.isNullOrEmpty(apiName)) {
            return;
        }
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(AIDetectRulePO.F_TENANT_ID).equal(tenantId);
        query.field(AIDetectRulePO.F_API_NAME).equal(apiName);
        query.field(AIDetectRulePO.F_IS_DELETED).equal(false);
        if(!Strings.isNullOrEmpty(id)){
            query.field(MongoPOBase.F_UNIQUE_ID).notEqual(id);
        }
        if (query.get() != null) {
            throw new AiProviderException("7124", "存在重复的模型。");
        }
    }

    @Override
    public List<ModelPo> query(Integer tenantId) {
        Query<ModelPo> query = dbContext.createQuery(ModelPo.class);
        query.field(ModelPo.F_TENANT_ID).equal(tenantId);
        return query.asList();
    }

    @Override
    public List<ModelPo> query(Integer tenantId, String scene) {
        Query<ModelPo> query = dbContext.createQuery(ModelPo.class);
        query.field(ModelPo.F_TENANT_ID).equal(tenantId);
        query.field(ModelPo.F_SCENE).equal(scene);
        return query.asList();
    }

    @Override
    public void update(Integer tenantId, String id, Map<String, Object> updateFields) {
        Query<ModelPo> query = buildUniqueIdQuery(id, ModelPo.class);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        UpdateOperations<ModelPo> updateOperations = dbContext.createUpdateOperations(ModelPo.class);
        if (updateFields != null && !updateFields.isEmpty()) {
            updateFields.forEach(updateOperations::set);
        }
        dbContext.update(query, updateOperations);
    }

    @Override
    public List<ModelPo> queryEmptyStatusPo(int limit) {
        Query<ModelPo> query = dbContext.createQuery(ModelPo.class);
        query.field(ModelPo.F_STATUS).doesNotExist();
        query.limit(limit);
        return query.asList();
    }

    @Override
    public ModelPo queryModelByName(Integer tenantId, String name, String id) {
        Query<ModelPo> query = dbContext.createQuery(ModelPo.class);
        query.field(ModelPo.F_TENANT_ID).equal(tenantId);
        query.field(ModelPo.F_NAME).equal(name);
        if (!Strings.isNullOrEmpty(id)) {
            query.field(ModelPo.F_UNIQUE_ID).notEqual(id);
            query.field("_id").notEqual(new ObjectId(id));
        }
        List<ModelPo> list = query.asList();
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

}
