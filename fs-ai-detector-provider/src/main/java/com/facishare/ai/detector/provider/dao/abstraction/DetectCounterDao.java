package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.DetectCounterPo;

/**
 * <AUTHOR>
 * @date 2020/3/10 下午2:46
 */
public interface DetectCounterDao {

    void success(int tenantId,String service);

    void fail(int tenantId,String service);

    void success(int tenantId,String service,long start,long end);

    void fail(int tenantId,String service ,long start, long end);

    DetectCounterPo sum(int tenantId,String service,long start,long end);
}
