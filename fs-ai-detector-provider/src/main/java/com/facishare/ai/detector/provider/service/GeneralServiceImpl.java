package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.api.BatchUpdateMongoPO;
import com.facishare.ai.detector.api.service.GeneralService;
import com.facishare.ai.detector.provider.dao.GeneralDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: linmj
 * Date: 2024/7/12 15:43
 */
@Slf4j
@Service
public class GeneralServiceImpl implements GeneralService {

    @Resource
    private GeneralDAO generalDAO;

    @Override
    public BatchUpdateMongoPO.Result batchUpdateMongoPO(BatchUpdateMongoPO.Arg arg) {
        List<BatchUpdateMongoPO.UpdateEntity> failList = new ArrayList<>();
        int count = 0;
        for(BatchUpdateMongoPO.UpdateEntity entity : arg.getUpdateEntities()){
            try{
                count += generalDAO.update(arg.getTenantId(), arg.getClassPath(), entity.getConditionMap(), entity.getUpdateMap());
            }catch (Exception e){
                log.info("update err. data:{}. ", entity, e);
                failList.add(entity);
            }
        }
        BatchUpdateMongoPO.Result result = new BatchUpdateMongoPO.Result();
        result.setFailEntities(failList);
        result.setSuccessCount(count);
        return result;
    }

}
