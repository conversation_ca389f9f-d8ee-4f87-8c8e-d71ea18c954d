package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.ApplicationDAO;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.po.ApplicationPO;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ApplicationDAOImpl extends DaoBase<ApplicationPO> implements ApplicationDAO {


    @Override
    public List<ApplicationPO> query(int tenantId) {
        Query<ApplicationPO> query = dbContext.createQuery(ApplicationPO.class);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        return query.asList();
    }

    @Override
    public ApplicationPO get(int tenantId, String identityKey) {
        Query<ApplicationPO> query = dbContext.createQuery(ApplicationPO.class);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        query.field(ApplicationPO.F_IDENTITY_KEY).equal(identityKey);
        return query.get();
    }

    @Override
    public String saveUnique(ApplicationPO applicationPO) {
        ApplicationPO oldPo = get(applicationPO.getTenantId(), applicationPO.getIdentityKey());
        if (oldPo == null) {
            setSystemInfo(applicationPO);
            fillUniqueId(applicationPO);
            return dbContext.save(applicationPO).getId().toString();
        }
        return oldPo.getUniqueId();
    }
}
