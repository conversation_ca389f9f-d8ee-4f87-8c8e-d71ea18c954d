package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.github.autoconf.ConfigFactory;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class YoloDetectorImpl implements Detector {

    @Resource
    private FileAdapter fileAdapter;

    private static final Logger logger = LoggerFactory.getLogger(YoloDetectorImpl.class);

    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageStream) throws AiProviderException {
        String url = fileAdapter.createShareFile(arg.getTenantAccount(), Integer.parseInt(arg.getUserId()), arg.getPath());
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .build();

        try {
            FormBody formBody = new FormBody.Builder()
                    .add("url", String.format(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.LOCAL_OBJECT_RECOGNITION),model.getKey()))
                    .add("group", model.getKey())
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .post(formBody)
                    .build();

            Response response = client.newCall(request).execute();
            String responseString = response.body().string();

            logger.info("response string : {}", responseString);

            return JSON.parseArray(responseString, BoxDto.class);
        } catch (IOException ex) {
            throw new AiProviderException("ADAPTER-500", ex.getMessage());
        }
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }
}
