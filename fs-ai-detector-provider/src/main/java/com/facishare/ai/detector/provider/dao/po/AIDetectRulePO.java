package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.api.dto.FieldDto;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;

import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.Map;

@Data
@ToString
@Entity(value = "fmcg_ai_detect_rule", noClassnameStored = true)
public class AIDetectRulePO extends MongoPOBase {

    public static final String F_MODEL_ID = "modelId";
    public static final String F_NAME = "name";
    public static final String F_RULE_DESCRIBE = "ruleDescribe";
    public static final String F_MASTER_DESCRIBE_API_NAME = "masterDescribeApiName";
    public static final String F_DETECT_CAPABILITY_MAP = "detectCapabilityMap";
    public static final String F_FIELD_MAP = "fieldMap";
    public static final String F_DESCRIPTION = "description";
    public static final String F_IS_DEFAULT = "isDefault";
    public static final String F_API_NAME = "apiName";
    public static final String F_PROMPT_TEMPLATE = "promptTemplate";

    @Property(F_MODEL_ID)
    private String modelId;

    @Property(F_NAME)
    private String name;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_PROMPT_TEMPLATE)
    private String promptTemplate;

    @Property(F_RULE_DESCRIBE)
    private String ruleDescribe;

    @Property(F_MASTER_DESCRIBE_API_NAME)
    private String masterDescribeApiName;

    /**
     * 业务能力
     * isOpenProductRowNumber 商品sku/排面数识别
     * isOpenGroupNumber 商品陈列组数
     * isOpenLayerNumber 货架层数识别
     * openSkuUnit 商品单位识别
     * isOpenPrices POSM识别
     * isOpenSceneDetect 商品陈列场景
     * isOpenDisplayForm 商品陈列形式
     */
    @Property(F_DETECT_CAPABILITY_MAP)
    private Map<String, Integer> detectCapabilityMap;

    /**
     * aiPath ai图片
     * productName 产品名称
     * aiRowNumber 商品排面数  需要calculateType属性
     * aiGroupNumber 商品组数  需要calculateType属性
     * aiLayerNumber 层数
     * aiSceneField 场景字段
     * aiUnitField 单位存储字段
     * aiPrices posm的价格字段
     * 陈列形式rio
     * displayTotalLayerNumber 陈列形式的层数
     * displayCutBoxNumber 陈列形式的割箱数
     * displayMaxVisibleNumber 陈列形式的最大可视数
     * displaySceneType 陈列形式的场景类型
     * displayTotalRowNumber 陈列形式的排面总数
     */
    @Embedded(F_FIELD_MAP)
    private Map<String, FieldDto> fieldMap;

    @Property(F_DESCRIPTION)
    private String description;

    @Property(F_IS_DEFAULT)
    private boolean isDefault;

}
