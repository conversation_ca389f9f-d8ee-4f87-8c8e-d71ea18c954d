package com.facishare.ai.detector.provider.adapter.service.file;

import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.util.CommonUtil;
import com.facishare.fsc.api.model.CreateFileShareId;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.*;
import com.facishare.stone.sdk.response.StoneFileRangeDownloadResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.facishare.stone.sdk.response.StoneSaveImageFromTempFilesResponse;
import com.fxiaoke.common.release.GrayRelease;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 19-11-8  下午4:03
 */
@Component(value = "fileAdapter")
@SuppressWarnings("Duplicates")
public class FileAdapter {

    private static String DOMAIN;

    private static final String DOT_JPG = ".jpg";

    private static final String JPG = "jpg";

    private static final Logger logger = LoggerFactory.getLogger(FileAdapter.class);

    static {
        DOMAIN = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.FXIAOKE_DOMAIN);
        DOMAIN = DOMAIN == null ? "www.fxiaoke.com" : DOMAIN;
    }

    @Resource
    private StoneProxyApi stoneProxyApi;

    @Resource
    private SharedFileService sharedFileService;

    @Resource
    private NFileStorageService nFileStorageService;

    public String createShareFile(String ea, Integer employeeId, String path) throws AiProviderException {
        CreateFileShareId.Arg createFileShareIdArg = new CreateFileShareId.Arg();
        createFileShareIdArg.employeeId = employeeId;
        createFileShareIdArg.ea = ea;
        createFileShareIdArg.path = path;
        createFileShareIdArg.securityGroup = "";
        try {
            CreateFileShareId.Result result = sharedFileService.createFileShareId(createFileShareIdArg);
            return "https://" + DOMAIN + "/FSC/N/FileShare/ShowImage?fileId=" + result.fileId;
        } catch (Exception ex) {
            logger.error("createShareFile  error.", ex);
            throw new AiProviderException("500", ex.toString());
        }

    }

    public String saveTempFile(String ea, int employeeId, String path) {
        logger.info("start save image, tenant account : {}, path : {}", ea, path);

        if (!path.startsWith("TN_")) {

            logger.info("finish save image.");
            return path;
        }
        try {

            StoneSaveImageFromTempFilesRequest request = new StoneSaveImageFromTempFilesRequest();
            request.setTempFileNames(Lists.newArrayList(path));
            request.setEa(ea);
            request.setEmployeeId(employeeId);
            request.setBusiness("FS-AI");
            request.setExtensionName(CommonUtil.JPG);

            StoneSaveImageFromTempFilesResponse response = stoneProxyApi.saveImageFromTempFiles(request);

            logger.info("finish save image.");

            String nPath = response.getResponseList().isEmpty() ? path : response.getResponseList().get(0).getPath();
            return nPath.endsWith(DOT_JPG) || nPath.endsWith(DOT_JPG.toUpperCase()) ? nPath : nPath + DOT_JPG;
        } catch (FRestClientException ex) {

            logger.error("save image error.", ex);
            return path;
        }
    }

    public byte[] downloadByPath(String ea, String path) throws AiProviderException {
        logger.info("start download image, tenant account : {}, path : {}", ea, path);
        long start = System.currentTimeMillis();
        try {
            StoneFileDownloadRequest downloadRequest = new StoneFileDownloadRequest();
            downloadRequest.setFileType(CommonUtil.JPG);
            downloadRequest.setCancelRemoteThumb(false);
            downloadRequest.setPath(path);
            downloadRequest.setSecurityGroup("");
            downloadRequest.setEa(ea);
            downloadRequest.setEmployeeId(1000);
            downloadRequest.setBusiness("FS-AI");
            InputStream stream = stoneProxyApi.downloadStream(downloadRequest);

            ByteArrayOutputStream output = new ByteArrayOutputStream();
            try {
                byte[] buffer = new byte[102400];
                int temp;
                while (-1 != (temp = stream.read(buffer))) {
                    output.write(buffer, 0, temp);
                }
            } catch (IOException ex) {
                throw new AiProviderException(ex, "image convert cause io exception.");
            }
            long end = System.currentTimeMillis();
            if (end - start > 500) {
                logger.info("download image cost:{}.may be too long", end - start);
            }
            logger.info("finish download image.");

            return output.toByteArray();
        } catch (FRestClientException ex) {
            logger.error("file exception:{}", ex);
            throw new AiProviderException("500", ex.toString());
        }
    }


    public String uploadImage(String ea, Integer employeeId, byte[] imageStream) throws AiProviderException {
        try {

            logger.info("start upload image, tenant account : {}", ea);

            StoneFileUploadRequest uploadRequest = new StoneFileUploadRequest();
            InputStream inputStream = new ByteArrayInputStream(imageStream);
            uploadRequest.setImageProcessRequest(new StoneFileImageProcessRequest());
            uploadRequest.setNeedThumbnail(false);
            uploadRequest.setKeepFormat(true);
            uploadRequest.setFileSize(imageStream.length);
            uploadRequest.setSecurityGroup("");
            uploadRequest.setPermissions(Lists.newArrayList());
            uploadRequest.setGlobal(false);
            uploadRequest.setExtensionName(CommonUtil.JPG);
            uploadRequest.setNamedPath("");
            uploadRequest.setEa(ea);
            uploadRequest.setEmployeeId(employeeId);
            uploadRequest.setBusiness("FS-AI");
            StoneFileUploadResponse uploadResponse = stoneProxyApi.uploadByStream("n", uploadRequest, inputStream);

            logger.info("start upload image, tenant account : {}", ea);

            return uploadResponse.getPath().endsWith(DOT_JPG) || uploadResponse.getPath().endsWith(DOT_JPG.toUpperCase()) ? uploadResponse.getPath() : uploadResponse.getPath() + DOT_JPG;
        } catch (Exception ex) {
            logger.error("Draw picture box error.", ex);
            throw new AiProviderException("600514", ex.toString());
        }
    }

    public byte[] downloadByChunk(String ea, Integer employeeId, String path) throws AiProviderException {
        StoneFileRangeDownloadRequest downloadRequest = new StoneFileRangeDownloadRequest();
        long batchSize = 1000L;
        long total = 0L;
        try (ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream()) {

            downloadRequest.setEndPos(batchSize);
            downloadRequest.setStartPos(total);
            downloadRequest.setBusiness("FS-AI");
            downloadRequest.setEa(ea);
            downloadRequest.setPath(path);
            downloadRequest.setEmployeeId(employeeId);
            downloadRequest.setSecurityGroup("");
            StoneFileRangeDownloadResponse response;
            do {
                response = stoneProxyApi.downloadByRange(downloadRequest);
                byteOutputStream.write(response.getData());
                total += response.getData().length;
                downloadRequest.setStartPos(total);
                downloadRequest.setEndPos(response.getTotalSize());
            } while (response.getTotalSize() > total);
            return byteOutputStream.toByteArray();
        } catch (FRestClientException | IOException e) {
            logger.info("get file err.", e);
            throw new AiProviderException("100010101", "download file fail.");
        }
    }

    public byte[] downloadAllByte(String ea, Integer employeeId, String path) throws AiProviderException {

        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setDownloadSecurityGroup("");
        arg.setDownloadUser(String.valueOf(employeeId));
        arg.setEa(ea);
        arg.setFiletype("jpg");
        arg.setnPath(path);
        NDownloadFile.Result result = nFileStorageService.nDownloadFile(arg, ea);
        return result.getData();
    }

    public byte[] downloadWithoutSockets(Integer ei, String ea, String path) throws AiProviderException {
        if (path.startsWith("TN") && !GrayRelease.isAllow("fmcg", "FORCE_GET_ALL_BYTES", ei)) {
            return downloadByChunk(ea, 1000, path);
        } else {
            return downloadAllByte(ea, 1000, path);
        }
    }

}
