package com.facishare.ai.detector.provider.service;

import com.alibaba.fastjson2.JSON;
import com.facishare.ai.detector.api.dto.ApplicationDto;
import com.facishare.ai.detector.api.dto.arg.AddApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetApplicationArg;
import com.facishare.ai.detector.api.dto.result.AddApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetApplicationResult;
import com.facishare.ai.detector.api.service.ApplicationService;
import com.facishare.ai.detector.provider.dao.abstraction.ApplicationDAO;
import com.facishare.ai.detector.provider.dao.po.ApplicationPO;
import com.facishare.ai.detector.provider.util.FmcgBeanUtil;
import com.fs.fmcg.sdk.ai.plat.Application;
import com.fs.fmcg.sdk.ai.plat.IdentityKeyUtil;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service(value = "applicationService")
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationDAO applicationDao;

    @Resource
    private TokenFactory tokenFactory;

    @Override
    public AddApplicationResult addApplication(AddApplicationArg arg) {
        AddApplicationResult result = new AddApplicationResult();
        result.setApplication(arg.getApplication());
        String identityKey = Strings.isBlank(arg.getApplication().getIdentityKey()) ? IdentityKeyUtil.generateIdentityKey(arg.getApplication().getPlatForm(), arg.getApplication().getParams()) : arg.getApplication().getIdentityKey();
        ApplicationPO applicationPO = getApplicationFromDB(arg.getTenantId(), identityKey);
        arg.getApplication().setIdentityKey(identityKey);
        if (applicationPO == null) {
            applicationPO = new ApplicationPO();
            FmcgBeanUtil.copyProperties(arg.getApplication(), applicationPO);
            applicationDao.saveUnique(applicationPO);
        }
        FmcgBeanUtil.copyProperties(applicationPO, result.getApplication());
        return result;
    }

    @Override
    public GetApplicationResult getApplication(GetApplicationArg arg) {
        GetApplicationResult result = new GetApplicationResult();
        try {
            ApplicationPO applicationPO = getApplicationFromDB(arg.getTenantId(), arg.getIdentityKey());

            if (applicationPO != null) {
                ApplicationDto dto = new ApplicationDto();
                FmcgBeanUtil.copyProperties(applicationPO, dto);
                result.setData(dto);
            } else {
                result.setData(null);
            }
        } catch (Exception e) {
            log.error("查询应用失败, tenantId={}, identityKey={}",
                    arg.getTenantId(), arg.getIdentityKey(), e);
        }
        return result;
    }

    private ApplicationPO getApplicationFromDB(int tenantId, String identityKey) {
        if (TokenFactory.APPLICATIONS.containsKey(identityKey)) {
            Application application = TokenFactory.APPLICATIONS.get(identityKey);
            ApplicationPO applicationPO = new ApplicationPO();
            applicationPO.setIdentityKey(identityKey);
            applicationPO.setPlatForm(application.getPlatform());
            applicationPO.setParams(JSON.parseObject(JSON.toJSONString(application), Map.class));
            return applicationPO;
        }
        ApplicationPO po = applicationDao.get(tenantId, identityKey);
        if (po == null) {
            return applicationDao.get(-1, identityKey);
        }
        return po;
    }
}