package com.facishare.ai.detector.provider.dao.po;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
public class FieldEntity {

    public static final String F_TYPE = "type";
    public static final String F_FIELD_KEY = "fieldKey";
    public static final String F_CALCULATE_TYPE = "calculateType";
    public static final String F_OBJECT_API_NAME = "objectApiName";
    public static final String F_FIELD_TYPE = "fieldType";
    public static final String F_AI_STORE_FIELD_API_NAME = "aiStoreFieldApiName";
    public static final String F_MANUALLY_STORE_FIELD_API_NAME = "manuallyStoreFieldApiName";

    /**
     * 类型 目前仅有 mapping
     */
    @Property(F_TYPE)
    private String type;

    /**
     * 字段apiName
     */
    @Property(F_FIELD_KEY)
    private String fieldKey;

    /**
     * 0 求和
     * 1 最大值
     */
    @Property(F_CALCULATE_TYPE)
    private Integer calculateType;

    /**
     * 对象apiName
     */
    @Property(F_OBJECT_API_NAME)
    private String objectApiName;

    /**
     * 字段类型
     * image
     * select_one
     * object_reference
     * number
     * text
     */
    @Property(F_FIELD_TYPE)
    private String fieldType;

    /**
     * ai存储字段
     */
    @Property(F_AI_STORE_FIELD_API_NAME)
    private String aiStoreFieldApiName;

    /**
     * 手动存储字段
     */
    @Property(F_MANUALLY_STORE_FIELD_API_NAME)
    private String manuallyStoreFieldApiName;
} 