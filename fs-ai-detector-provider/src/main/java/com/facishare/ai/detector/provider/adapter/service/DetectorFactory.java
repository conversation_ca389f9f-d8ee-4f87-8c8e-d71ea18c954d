package com.facishare.ai.detector.provider.adapter.service;

import com.facishare.ai.detector.provider.adapter.SpringContextHolder;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/18 18:59
 */
public class DetectorFactory {

    private static final Map<String, String> BEAN_NAME_MAP = new HashMap<>();

    static {
        BEAN_NAME_MAP.put("fs", "yoloDetector");
        BEAN_NAME_MAP.put("baidu", "baiduDetector");
        BEAN_NAME_MAP.put("alibaba", "alibabaDetector");
        BEAN_NAME_MAP.put("baidu_retail", "baiduOfficialRetailDetector");
        BEAN_NAME_MAP.put("baidu_custom_retail", "baiduOfficialRetailDetector");
        BEAN_NAME_MAP.put("pinlan", "pinLanDetector");
        BEAN_NAME_MAP.put("huawei", "huaweiDetectorImpl");
        BEAN_NAME_MAP.put("huawei_sku_poc", "huaweiSkuPocDetectorImpl");
        BEAN_NAME_MAP.put("huawei_model_art_obj_detect", "huaWeiModelArtsDetectorImpl");
        BEAN_NAME_MAP.put("yqsl", "yqDetectorImpl");
    }

    public static Detector getDetector(String platform) {
        return SpringContextHolder.getBean(BEAN_NAME_MAP.get(platform));
    }
}
