// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: XDService.proto

package com.facishare.ai.detector.provider.adapter.grpc.yq;

public final class XDServiceProto {
  private XDServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_XDService_RequestParam_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_XDService_RequestParam_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_XDService_ResultTrack_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_XDService_ResultTrack_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\017XDService.proto\022\tXDService\",\n\014RequestP" +
      "aram\022\r\n\005imgId\030\001 \001(\t\022\r\n\005image\030\002 \001(\014\":\n\013Re" +
      "sultTrack\022\r\n\005imgId\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022\016" +
      "\n\006result\030\003 \001(\t2T\n\021XDRecogizeService\022?\n\nX" +
      "DRecogize\022\027.XDService.RequestParam\032\026.XDS" +
      "ervice.ResultTrack\"\000BF\n2com.facishare.ai" +
      ".detector.provider.adapter.grpc.yqB\016XDSe" +
      "rviceProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_XDService_RequestParam_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_XDService_RequestParam_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_XDService_RequestParam_descriptor,
        new String[] { "ImgId", "Image", });
    internal_static_XDService_ResultTrack_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_XDService_ResultTrack_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_XDService_ResultTrack_descriptor,
        new String[] { "ImgId", "Code", "Result", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
