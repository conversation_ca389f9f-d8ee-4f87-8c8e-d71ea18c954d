package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.model.BaiduDetectArg;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.codec.binary.Base64;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
public class BaiduDetectorImpl implements Detector {

    private static final Logger logger = LoggerFactory.getLogger(BaiduDetectorImpl.class);

    private OkHttpClient okHttpClient = new OkHttpClient();

    private MediaType mediaType = MediaType.parse("application/json;charset=utf-8");

    @Resource
    private TokenFactory tokenFactory;

    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageStream) throws AiProviderException {

        logger.info("api detect started.");
        List<BoxDto> boxes = Lists.newArrayList();
        try {
            BaiduDetectArg detectArg = convertToBaiduDetectArg(imageStream, model.getConfidence());

            RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(detectArg));
            String tokenKey = Strings.isNullOrEmpty(model.getTokenKey()) ? AppEnum.BAIDU_FXIAOKERD_EASYDL.value() : model.getTokenKey();
            String url = String.format(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.BAIDU_OBJECT_RECOGNITION), model.getKey(), tokenFactory.getToken(tokenKey));

            logger.info("baidu api url : {}", url);

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            String jsonStr = response.body().string();

            logger.info("baidu api response : {}", jsonStr);

            JSONObject object = JSON.parseObject(jsonStr);
            if (object.containsKey("error_code")) {

                logger.info("baidu api fail : {}", object.getString("error_msg"));
                throw new AiProviderException("AI", "baidu api fail." + object.getString("error_msg"));
            }
            JSONArray array = object.getJSONArray("results");
            array.forEach(item -> {
                JSONObject o = (JSONObject) item;

                JSONObject location = o.getJSONObject("location");
                Double[] size = new Double[4];
                size[0] = location.getDouble("top");
                size[1] = location.getDouble("left");
                size[2] = location.getDouble("top") + location.getDouble("height");
                size[3] = location.getDouble("left") + location.getDouble("width");

                BoxDto box = new BoxDto();
                box.setScore(o.getDouble("score").toString());
                box.setName(o.getString("name"));
                box.setBox(size);
                boxes.add(box);
            });
        } catch (IOException e) {
            logger.error("baidu api error.", e);
            throw new AiProviderException("AI", "call BaiDu Api fail.");
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        logger.info("api detect started.");
        List<BoxDto> boxes = Lists.newArrayList();
        try {
            BaiduDetectArg detectArg = convertToBaiduDetectArg(arg.getBase64(), model.getConfidence());

            RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(detectArg));
            String tokenKey = Strings.isNullOrEmpty(model.getTokenKey()) ? AppEnum.BAIDU_FXIAOKERD_EASYDL.value() : model.getTokenKey();
            String url = String.format(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.BAIDU_OBJECT_RECOGNITION), model.getKey(), tokenFactory.getToken(tokenKey));

            logger.info("baidu api url : {}", url);

            Request request = new Request.Builder()
                    .url(url)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            Response response = okHttpClient.newCall(request).execute();
            String jsonStr = response.body().string();

            logger.info("baidu api response : {}", jsonStr);

            JSONObject object = JSON.parseObject(jsonStr);
            if (object.containsKey("error_code")) {

                logger.info("baidu api fail : {}", object.getString("error_msg"));
                throw new AiProviderException("AI", "baidu api fail." + object.getString("error_msg"));
            }
            JSONArray array = object.getJSONArray("results");
            array.forEach(item -> {
                JSONObject o = (JSONObject) item;

                JSONObject location = o.getJSONObject("location");
                Double[] size = new Double[4];
                size[0] = location.getDouble("top");
                size[1] = location.getDouble("left");
                size[2] = location.getDouble("top") + location.getDouble("height");
                size[3] = location.getDouble("left") + location.getDouble("width");

                BoxDto box = new BoxDto();
                box.setScore(o.getDouble("score").toString());
                box.setName(o.getString("name"));
                box.setBox(size);
                boxes.add(box);
            });
        } catch (IOException e) {
            logger.error("baidu api error.", e);
            throw new AiProviderException("AI", "call BaiDu Api fail.");
        }
        return boxes;
    }

    private BaiduDetectArg convertToBaiduDetectArg(byte[] imageCache, double confidence) {
        BaiduDetectArg result = new BaiduDetectArg();
        result.setThreshold(confidence);
        result.setImage(Base64.encodeBase64String(imageCache));
        return result;
    }

    private BaiduDetectArg convertToBaiduDetectArg(String base64, double confidence) {
        BaiduDetectArg result = new BaiduDetectArg();
        result.setThreshold(confidence);
        result.setImage(base64);
        return result;
    }
}