package com.facishare.ai.detector.provider.service;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.BatchClassifyArg;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.dto.result.BatchClassifyResult;
import com.facishare.ai.detector.api.dto.result.ClassifyResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.ClassifierService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.adapter.service.ClassifierFactory;
import com.facishare.ai.detector.provider.dao.OldModelDaoImpl;
import com.facishare.ai.detector.provider.dao.abstraction.ClassifyRecordDao;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.po.ClassEntity;
import com.facishare.ai.detector.provider.dao.po.ClassifyRecordPo;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 19-8-1  下午3:42
 */
@SuppressWarnings("Duplicates")
public class ClassifierServiceImpl implements ClassifierService {

    private static final Logger logger = LoggerFactory.getLogger(ClassifierServiceImpl.class);

    @Resource
    private StoneProxyApi stoneProxyApi;

    @Resource
    private ModelDao modelDao;

    @Autowired
    private OldModelDaoImpl oldModelDao;

    @Resource
    private ClassifyRecordDao classifyRecordDao;

    @Resource
    private DetectCounterDao detectCounterDao;

    private static final String JPG = "jpg";
    private static final String DOT_JPG = ".jpg";


    @Override
    public ClassifyResult classify(ClassifyArg arg) throws AiProviderException {

        logger.info("classify start : {}", JSON.toJSONString(arg));
        /*if(arg.getTenantId().trim().equals("472252")){
            ClassifyRecordPo tmp = new ClassifyRecordPo();
            tmp.setMessage("langjing classify again");
            tmp.setSrcImgPath(arg.getPath());
            tmp.setStatus(ConstantUtil.WAITING);
            tmp.setTenantId(Integer.parseInt(arg.getTenantId()));
            classifyRecordDao.save(tmp);
        }*/
        ClassifyResult rst = new ClassifyResult();

        if (!arg.getPath().endsWith(DOT_JPG) && !arg.getPath().endsWith(DOT_JPG.toUpperCase())) {
            arg.setPath(arg.getPath() + DOT_JPG);
        }

        rst.setPath(arg.getPath());

        ModelPo modelPo = getModel(Integer.valueOf(arg.getTenantId()),arg.getModelId());
        if (modelPo==null) {
            logger.info("tenant :{}  can`t use this model!", arg.getTenantId());
            return rst;
        }
        ClassifyRecordPo record = classifyRecordDao.get(Integer.valueOf(arg.getTenantId()), arg.getModelId(), arg.getPath());
        if (record != null && record.getStatus().equals(ConstantUtil.SUCCESS)) {
            logger.info("classify success. get from record:{}.", record);
            rst.setClasses(toClassDto(record.getResult()));
        } else {
            byte[] imageCache = getImage(arg.getTenantAccount(), arg.getPath());

            if (record == null) {
                record = new ClassifyRecordPo();
                record.setModelPoId(arg.getModelId());
                record.setSrcImgPath(arg.getPath());
                record.setTenantId(Integer.valueOf(arg.getTenantId()));
                record.setStatus(ConstantUtil.FAILURE);
            }
            logger.info("classify info : {}", JSON.toJSONString(modelPo));
            Classifier classifier = ClassifierFactory.getClassifier(modelPo.getPlatform());
            try {
                List<ClassDto> classes = classifier.classify(arg, modelPo, imageCache);
                rst.setClasses(classes);
                record.setStatus(ConstantUtil.SUCCESS);
                record.setResult(toClassEntity(classes));
                detectCounterDao.success(Integer.parseInt(arg.getTenantId()), String.format(ConstantUtil.OBJECT_CLASSIFY,modelPo.getId()==null?modelPo.getIdentity():modelPo.getUniqueId()));
            } catch (AiProviderException ae) {
                record.setMessage(ae.getMessage());
                classifyRecordDao.save(record);
                detectCounterDao.fail(Integer.parseInt(arg.getTenantId()), String.format(ConstantUtil.OBJECT_CLASSIFY,modelPo.getId()==null?modelPo.getIdentity():modelPo.getUniqueId()));
                throw ae;
            }
            classifyRecordDao.save(record);
            logger.info("classify finished!");

            logger.info("classify success! :{}", JSON.toJSONString(rst));
        }

        return rst;
    }

    @Override
    public BatchClassifyResult batchClassify(BatchClassifyArg arg) throws AiProviderException {

        logger.info("start batch insert.arg:{}", arg);
        BatchClassifyResult result = new BatchClassifyResult();
        List<ClassifyResult> list = new ArrayList<>();
        ModelPo modelPo = getModel(Integer.valueOf(arg.getTenantId()),arg.getModelId());
        if (modelPo==null || !modelPo.getTenantId().toString().equals(arg.getTenantId())) {
            logger.info("tenant :{}  can`t use this model!", arg.getTenantId());
            return result;
        }
        arg.setPaths(arg.getPaths().stream().map(v -> v + ".jpg").collect(Collectors.toList()));
        List<ClassifyRecordPo> records = classifyRecordDao.query(Integer.valueOf(arg.getTenantId()), arg.getModelId(), arg.getPaths());

        result.setResults(list);
        if (!records.isEmpty()) {
            records.forEach(v -> {
                if (!v.getStatus().equals(ConstantUtil.SUCCESS)) {
                    return;
                }
                ClassifyResult tmp = new ClassifyResult();
                tmp.setPath(v.getSrcImgPath());
                tmp.setClasses(toClassDto(v.getResult()));
                list.add(tmp);
                arg.getPaths().remove(v.getSrcImgPath());
            });
            logger.info("some of them has been classified by historical record.");
        }

        if (!arg.getPaths().isEmpty()) {
            ClassifyArg classifyArg = new ClassifyArg();
            classifyArg.setTenantAccount(arg.getTenantAccount());
            classifyArg.setTenantId(arg.getTenantId());
            classifyArg.setModelId(arg.getModelId());
            classifyArg.setUserId(arg.getUserId());
            for (String path : arg.getPaths()) {
                classifyArg.setPath(path.replace(".jpg", ""));
                list.add(classify(classifyArg));
            }
            logger.info("calll classfy interface one by one successfully.");
        }
        logger.info("batchClassify success.");
        return result;
    }

    private byte[] getImage(String ea, String path) throws AiProviderException {
        try {
            StoneFileDownloadRequest downloadRequest = new StoneFileDownloadRequest();
            downloadRequest.setFileType(JPG);
            downloadRequest.setCancelRemoteThumb(false);
            downloadRequest.setPath(path);
            downloadRequest.setSecurityGroup("");
            downloadRequest.setEa(ea);
            downloadRequest.setEmployeeId(1000);
            downloadRequest.setBusiness("FS-AI");
            InputStream stream = stoneProxyApi.downloadStream(downloadRequest);

            ByteArrayOutputStream output = new ByteArrayOutputStream();
            try {
                byte[] buffer = new byte[4096];
                int temp;
                while (-1 != (temp = stream.read(buffer))) {
                    output.write(buffer, 0, temp);
                }
            } catch (IOException ex) {
                throw new AiProviderException(ex, "image convert cause io exception.");
            }
            return output.toByteArray();
        } catch (FRestClientException ex) {
            logger.error("getImage err!.ea:{},paths:{},err:{}", ea, path, ex);
            throw new AiProviderException(ex.getCode(), ex.getMessage());
        }
    }

    private List<ClassEntity> toClassEntity(List<ClassDto> dtos) {
        if (dtos == null || dtos.isEmpty())
            return new ArrayList<>();
        return dtos.stream().map(v -> {
            ClassEntity entity = new ClassEntity();
            BeanUtils.copyProperties(v, entity);
            return entity;
        }).collect(Collectors.toList());
    }

    private List<ClassDto> toClassDto(List<ClassEntity> entities) {
        if (entities == null || entities.isEmpty())
            return new ArrayList<>();
        return entities.stream().map(v -> {
            ClassDto dto = new ClassDto();
            BeanUtils.copyProperties(v, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    private ModelPo getModel(Integer tenantId,String modelId){
        ModelPo modelPo = oldModelDao.get(Integer.valueOf(tenantId), modelId);
        if (modelPo == null)
            modelPo = modelDao.query(modelId,tenantId);
        return  modelPo;
    }
}
