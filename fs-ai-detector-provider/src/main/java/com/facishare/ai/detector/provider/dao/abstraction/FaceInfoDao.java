package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.FaceInfoPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-11  下午4:45
 */
public interface FaceInfoDao {

    String save(FaceInfoPo po);

    void update(FaceInfoPo po);

    FaceInfoPo query(String groupId,String userId);

    FaceInfoPo query(String groupId,String userId,String path);

    void  delete(String groupId,String userId);

}
