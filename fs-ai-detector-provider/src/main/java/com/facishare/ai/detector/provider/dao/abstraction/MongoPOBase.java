package com.facishare.ai.detector.provider.dao.abstraction;

import com.google.common.base.Strings;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/7/20 10:59
 */
@Data
@ToString
public class MongoPOBase implements Serializable {

    public static final String F_UNIQUE_ID = "UI";

    public static final String F_TENANT_ID = "TI";

    public static final String F_CREATOR = "CAR";

    public static final String F_LAST_MODIFIER = "LMR";

    public static final String F_CREATE_TIME = "CT";

    public static final String F_LAST_MODIFY_TIME = "LMT";

    public static final String F_IS_DELETED = "IDLD";

    @Id
    protected ObjectId id;

    @Property(F_TENANT_ID)
    protected Integer tenantId;

    @Property(F_UNIQUE_ID)
    protected String uniqueId;

    public String getUniqueId() {
        if (Strings.isNullOrEmpty(uniqueId)) {
            if(id == null){
                return null;
            }
            uniqueId = id.toString();
        }
        return uniqueId;
    }

    public ObjectId getId() {
        if (uniqueId != null) {
            return new ObjectId(uniqueId);
        }
        return id;
    }

    public ObjectId getOriginalId() {
        return id;
    }

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_LAST_MODIFIER)
    private Integer lastModifier;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_LAST_MODIFY_TIME)
    private Long lastModifyTime;

    @Property(F_IS_DELETED)
    private Boolean isDeleted = false;
}
