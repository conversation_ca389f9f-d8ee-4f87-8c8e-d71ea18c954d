package com.facishare.ai.detector.provider.adapter.service.ocr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.ai.detector.api.util.ConstantUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午2:28
 */
@Data
@ToString
public class IdCardDetectAO {

    private int errorCode;

    private String errorMsg= ConstantUtil.SUCCESS;

    @J<PERSON>NField(name = "direction")
    private int direction;

    @JSONField(name = "image_status")
    private String imageStatus;

    @JSONField(name = "risk_type")
    private String riskType;

    @JSONField(name = "edit_tool")
    private String editTool;

    @J<PERSON><PERSON>ield(name = "photo")
    private String photo;

    @JSONField(name = "idcard_number_type")
    private String validation;

    private String address;

    private String idNum;

    private String birth;

    private String name;

    private String sex;

    private String nationality;
}
