package com.facishare.ai.detector.provider.facade;

import com.facishare.ai.detector.api.dto.arg.AddFaceArg;
import com.facishare.ai.detector.api.dto.arg.FaceComparisionArg;
import com.facishare.ai.detector.api.dto.arg.FaceDetectArg;
import com.facishare.ai.detector.api.dto.result.AddFaceResult;
import com.facishare.ai.detector.api.dto.result.FaceComparisionResult;
import com.facishare.ai.detector.api.dto.result.FaceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.service.FaceDetectServiceImpl;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 19-11-12  下午6:59
 */
@RestController
@RequestMapping(value = "face")
public class FaceDetectController {

    @Resource
    private FaceDetectServiceImpl faceDetectService;

    @RequestMapping(value = "faceComparision")
    public FaceComparisionResult faceComparision(@RequestBody FaceComparisionArg arg) throws AiProviderException {
        return faceDetectService.faceComparision(arg);
    }

    @RequestMapping(value = "addFace")
    public AddFaceResult addFace(@RequestBody AddFaceArg arg) throws AiProviderException {
        return faceDetectService.addFace(arg);
    }

    @RequestMapping(value = "faceDetect")
    public FaceDetectResult detectFace(@RequestBody FaceDetectArg arg) throws AiProviderException {
        return faceDetectService.faceDetect(arg);
    }

}
