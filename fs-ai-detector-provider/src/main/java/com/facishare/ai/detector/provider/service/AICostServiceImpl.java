package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.arg.CreateAccountArg;
import com.facishare.ai.detector.api.dto.arg.CreateAccountDetailArg;
import com.facishare.ai.detector.api.dto.arg.CreatePriceArg;
import com.facishare.ai.detector.api.dto.arg.QueryBalanceOfObjectDetectArg;
import com.facishare.ai.detector.api.dto.result.CreateAccountDetailResult;
import com.facishare.ai.detector.api.dto.result.CreateAccountResult;
import com.facishare.ai.detector.api.dto.result.CreatePriceResult;
import com.facishare.ai.detector.api.dto.result.QueryBalanceOfObjectDetectResult;
import com.facishare.ai.detector.api.service.AICostService;
import com.facishare.ai.detector.api.service.DetectCounterService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.dao.abstraction.AccountDAO;
import com.facishare.ai.detector.provider.dao.abstraction.AccountDetailDAO;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.abstraction.PriceDAO;
import com.facishare.ai.detector.provider.dao.po.AccountDetailPO;
import com.facishare.ai.detector.provider.dao.po.AccountPO;
import com.facishare.ai.detector.provider.dao.po.DetectCounterPo;
import com.facishare.ai.detector.provider.dao.po.PricePO;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午4:56
 */
@Service(value = "aiCostService")
public class AICostServiceImpl implements AICostService {

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private AccountDetailDAO accountDetailDAO;

    @Resource
    private PriceDAO priceDAO;

    @Resource
    private DetectCounterService detectCounterService;

    @Resource
    private DetectCounterDao detectCounterDao;


    @Override
    public CreateAccountDetailResult createAccountDetail(CreateAccountDetailArg arg) {

        AccountDetailPO po = new AccountDetailPO();
        po.setTenantId(arg.getTenantId());
        po.setAmount(arg.getAmount());
        po.setType(arg.getType());
        po.setRemark(arg.getRemark());
        po.setAccountId(arg.getAccountId());
        CreateAccountDetailResult result = new CreateAccountDetailResult();
        result.setDetailId(accountDetailDAO.insert(po));
        accountDAO.riseBalance(po.getAccountId(),po.getAmount());
        return result;
    }

    @Override
    public CreateAccountResult createAccount(CreateAccountArg arg) {
        AccountPO po = new AccountPO();
        po.setTenantId(arg.getTenantId());
        po.setBalance(0D);
        po.setType(arg.getType());
        CreateAccountResult result = new CreateAccountResult();
        result.setAccountId(accountDAO.insert(po));
        return result;
    }

    @Override
    public CreatePriceResult createPrice(CreatePriceArg arg) {
        PricePO pricePO = new PricePO();
        pricePO.setUnitPrice(arg.getUnitPrice());
        pricePO.setModelId(arg.getModelId());
        pricePO.setTenantId(arg.getTenantId());
        CreatePriceResult result = new CreatePriceResult();
        result.setPriceId(priceDAO.insert(pricePO));
        return result;
    }

    @Override
    public QueryBalanceOfObjectDetectResult queryBalanceOfObjectDetect(QueryBalanceOfObjectDetectArg arg) {
        AccountPO account = accountDAO.query(arg.getTenantId(),arg.getType());
        DetectCounterPo counterPo =  detectCounterDao.sum(arg.getTenantId(),String.format(ConstantUtil.OBJECT_DETECT,arg.getModelId()),0,System.currentTimeMillis()+ DateUtils.MILLIS_PER_DAY);
        PricePO pricePO = priceDAO.query(arg.getTenantId(),arg.getModelId());
        QueryBalanceOfObjectDetectResult result = new QueryBalanceOfObjectDetectResult();
        result.setCost(counterPo.getSuccess()*pricePO.getUnitPrice());
        result.setBalance(account.getBalance()-result.getCost());
        result.setDetectedPicCnt(counterPo.getSuccess());
        return result;
    }
}
