package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.ObjectDto;
import com.facishare.ai.detector.api.dto.api.GetModel;
import com.facishare.ai.detector.api.dto.api.SaveDetectRecord;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.SdkService;
import com.facishare.ai.detector.provider.cache.ProductCache;
import com.facishare.ai.detector.provider.dao.OldModelDaoImpl;
import com.facishare.ai.detector.provider.dao.OldObjectMapDaoImpl;
import com.facishare.ai.detector.provider.dao.abstraction.DetectRecordDao;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.*;
import com.fmcg.framework.http.PaasDataProxy;
import com.fxiaoke.common.MapUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
public class SdkServiceImpl implements SdkService {

    @Resource
    private ModelDao modelDao;

    @Resource
    private OldModelDaoImpl oldModelDao;

    @Resource
    private ObjectMapDao objectMapDao;

    @Resource
    private OldObjectMapDaoImpl oldObjectMapDao;

    @Resource
    private DetectRecordDao detectRecordDao;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private ProductCache productCache;

    @Override
    public GetModel.Result getModel(Integer tenantId, Integer currentEmployeeId, GetModel.Arg arg) {
        if (tenantId == null || tenantId == 0) {
            GetModel.Result result = new GetModel.Result();
            result.setCode(10403);
            result.setMessage("tenant id error.");
            result.setSuccess(false);
            return result;
        }
        if (Strings.isNullOrEmpty(arg.getModelId())) {
            GetModel.Result result = new GetModel.Result();
            result.setCode(10500);
            result.setMessage("arg error.");
            result.setSuccess(false);
            return result;
        }
        ModelPo model;
        try {
            model = insureModel(arg.getModelId(), tenantId.toString());
        } catch (AiProviderException e) {
            GetModel.Result result = new GetModel.Result();
            result.setCode(10404);
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            return result;
        }
        GetModel.Result result = new GetModel.Result();
        result.setCode(0);
        result.setMessage("");
        result.setSuccess(true);
        GetModel.ResultData data = new GetModel.ResultData();
        GetModel.ModelDTO modelDTO = new GetModel.ModelDTO();
        modelDTO.setId(model.getId() == null ? model.getIdentity() : model.getUniqueId());
        modelDTO.setName(model.getName());
        modelDTO.setTenantId(model.getTenantId());
        modelDTO.setPlatform(model.getPlatform());
        modelDTO.setKey(model.getKey());
        modelDTO.setConfidence(model.getConfidence());
        modelDTO.setType(model.getType());
        modelDTO.setIdentity(model.getIdentity());
        modelDTO.setTokenKey(model.getTokenKey());
        modelDTO.setParams(model.getParams());
        modelDTO.setObjectMapList(new ArrayList<>());
        modelDTO.setModelManufacturer(model.getModelManufacturer());
        modelDTO.setStatus(model.getStatus());
        modelDTO.setScene(modelDTO.getScene());
        List<ObjectMapPo> maps = new ArrayList<>(queryObjectMap(tenantId, arg.getModelId()).values());
        for (ObjectMapPo map : maps) {
            GetModel.ObjectMapDTO objectMap = new GetModel.ObjectMapDTO();
            objectMap.setKey(map.getKey());
            objectMap.setApiName(map.getApiName());
            objectMap.setObjectId(map.getObjectId());
            objectMap.setUnit(map.getUnit());
            objectMap.setAppId(map.getAppId());
            objectMap.setColor(map.getColor());
            objectMap.setThreshold(map.getThreshold());
            objectMap.setExtraData(map.getExtraData());
            modelDTO.getObjectMapList().add(objectMap);
        }
        data.setModel(modelDTO);
        result.setData(data);
        return result;
    }

    @Override
    public SaveDetectRecord.Result saveDetectRecord(Integer tenantId, Integer currentEmployeeId, SaveDetectRecord.Arg arg) {
        if (tenantId == null || tenantId == 0) {
            SaveDetectRecord.Result result = new SaveDetectRecord.Result();
            result.setCode(10403);
            result.setMessage("tenant id error.");
            result.setSuccess(false);
            return result;
        }
        if (Strings.isNullOrEmpty(arg.getModelId()) || Strings.isNullOrEmpty(arg.getPath())) {
            SaveDetectRecord.Result result = new SaveDetectRecord.Result();
            result.setCode(10500);
            result.setMessage("arg error.");
            result.setSuccess(false);
            return result;
        }
        //productId->name
        Map<String, String> objectIdMap = new HashMap<>();
        DetectRecordPo record = new DetectRecordPo();
        record.setTenantId(tenantId);
        record.setModelId(arg.getModelId());
        record.setOriginalPath(arg.getPath());
        record.setProcessedPath(Strings.isNullOrEmpty(arg.getProcessPath()) ? arg.getPath() : arg.getProcessPath());
        record.setPath(arg.getPath());
        record.setCreateTime(System.currentTimeMillis());
        record.setExtraData(arg.getExtraData());
        record.setObjects(arg.getObjects().stream().map(m -> {
            ObjectEntity entity = new ObjectEntity();
            entity.setObjectType(m.getObjectType());
            if (!Strings.isNullOrEmpty(m.getObjectName())) {
                entity.setObjectName(m.getObjectName());
            } else {
                try {
                    entity.setObjectName(getObjectName(tenantId, m.getObjectType(), m.getObjectId(), objectIdMap));
                } catch (AiProviderException e) {
                    log.info("save exception:", e);
                }
            }
            entity.setObjectId(m.getObjectId());
            entity.setScore(String.valueOf(m.getScore()));
            entity.setAppId(m.getAppId());
            entity.setColor(m.getColor());
            entity.setUnit(m.getUnit());
            entity.setComponents(m.getComponents());
            entity.setIsFront(m.getIsFront());
            entity.setIsRotated(m.getIsRotated());
            entity.setComponentEntities(translateObjectEntity(tenantId, objectIdMap, m.getComponentEntities()));
            entity.setType(m.getType());
            entity.setKey(m.getKey());
            entity.setScene(m.getScene());
            entity.setShelf(m.getShelf());
            entity.setLayer(m.getLayer());
            entity.setSkuSn(m.getSkuSn());
            entity.setSubSkuCount(m.getSubSkuCount());
            entity.setExtraData(m.getExtraData());
            PositionEntity positionEntity = new PositionEntity();
            if (m.getPosition() != null) {
                positionEntity.setX(m.getPosition().getX());
                positionEntity.setY(m.getPosition().getY());
                positionEntity.setW(m.getPosition().getW());
                positionEntity.setH(m.getPosition().getH());
                entity.setPosition(positionEntity);
            }
            return entity;
        }).collect(Collectors.toList()));

        SaveDetectRecord.Result result = new SaveDetectRecord.Result();
        result.setCode(0);
        result.setMessage("");
        result.setSuccess(true);
        SaveDetectRecord.ResultData data = new SaveDetectRecord.ResultData();
        data.setId(detectRecordDao.save(record));
        result.setData(data);
        return result;
    }

    List<ObjectEntity> translateObjectEntity(Integer tenantId, Map<String, String> productIdMap, List<ObjectDto> entities) {

        List<ObjectEntity> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(entities)) {
            entities.forEach(m -> {
                ObjectEntity entity = new ObjectEntity();
                entity.setObjectType(m.getObjectType());
                try {

                    entity.setObjectName(getObjectName(tenantId, m.getObjectType(), m.getObjectId(), productIdMap));
                } catch (AiProviderException e) {
                    log.info("save exception:", e);
                }
                entity.setObjectId(m.getObjectId());
                entity.setScore(String.valueOf(m.getScore()));
                entity.setAppId(m.getAppId());
                entity.setColor(m.getColor());
                entity.setUnit(m.getUnit());
                entity.setKey(m.getKey());
                entity.setComponents(m.getComponents());
                entity.setIsFront(m.getIsFront());
                entity.setIsRotated(m.getIsRotated());
                entity.setComponentEntities(translateObjectEntity(tenantId, productIdMap, m.getComponentEntities()));
                entity.setType(m.getType());
                entity.setScene(m.getScene());
                entity.setShelf(m.getShelf());
                entity.setLayer(m.getLayer());
                entity.setSkuSn(m.getSkuSn());
                entity.setSubSkuCount(m.getSubSkuCount());
                PositionEntity positionEntity = new PositionEntity();
                if (m.getPosition() != null) {
                    positionEntity.setX(m.getPosition().getX());
                    positionEntity.setY(m.getPosition().getY());
                    positionEntity.setW(m.getPosition().getW());
                    positionEntity.setH(m.getPosition().getH());
                    entity.setPosition(positionEntity);
                }
                result.add(entity);
            });
        }

        return result;
    }

    public String getObjectName(int ei, String apiName, String key, Map<String, String> map) throws AiProviderException {
        apiName = Strings.isNullOrEmpty(apiName) ? "ProductObj" : apiName;
        if (!map.containsKey(key)) {
            Map<String, String> productIdMap = getObjectNameMap(ei, apiName, Lists.newArrayList(key));
            map.putAll(productIdMap);
            if (MapUtils.isNullOrEmpty(map))
                map.put(key, "");
        }
        return map.get(key);
    }

    private Map<String, ObjectMapPo> queryObjectMap(Integer tenantId, String modelId) {

        List<ObjectMapPo> objectMapList = oldObjectMapDao.query(tenantId, modelId);
        if (objectMapList == null || objectMapList.isEmpty())
            objectMapList = objectMapDao.query(tenantId, modelId);

        Map<String, ObjectMapPo> objectMap = new HashMap<>();
        for (ObjectMapPo objectMapPo : objectMapList) {
            if (!objectMap.containsKey(objectMapPo.getKey())) {
                objectMap.put(objectMapPo.getKey(), objectMapPo);
            }
        }
        return objectMap;
    }

    private ModelPo insureModel(String modelId, String tenantId) throws AiProviderException {
        ModelPo model = oldModelDao.get(Integer.valueOf(tenantId), modelId);
        if (model == null && isMongoIdString(modelId)) {
            model = modelDao.query(modelId, Integer.valueOf(tenantId));
        }
        if (model == null) {
            throw new AiProviderException("600404", String.format("tenant:%s cant map any ai model.", tenantId));
        }
        return model;
    }

    private boolean isMongoIdString(String id) {
        if (id == null || id.length() > 24)
            return false;
        for (int i = 0; i < id.length(); i++) {
            char c = id.charAt(i);
            boolean t = (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
            if (!t)
                return false;
        }
        return true;
    }

    public Map<String, String> getObjectNameMap(int ei, String apiName, List<String> productIds) throws AiProviderException {
        Map<String, String> nameMap = new HashMap<>();
        List<Map<String, Object>> products = productCache.queryObjectData(ei, apiName, productIds);
        products.forEach(product -> nameMap.put((String) product.get("_id"), (String) product.get("name")));
        productIds.forEach(id -> nameMap.putIfAbsent(id, ""));
        return nameMap;
    }
}
