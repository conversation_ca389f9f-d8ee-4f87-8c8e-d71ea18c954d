package com.facishare.ai.detector.provider.adapter.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 19-8-20  上午11:34
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ImageDto {

    private String path;

    private PositionInfo position;

    public ImageDto(String path, int x, int y) {
        this.path = path;
        this.position = new PositionInfo(x, y);
    }
}
