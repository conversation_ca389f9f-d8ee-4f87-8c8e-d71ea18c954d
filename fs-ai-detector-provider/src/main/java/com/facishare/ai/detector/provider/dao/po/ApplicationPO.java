package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.Map;

@Data
@NoArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = true)
@Entity(noClassnameStored = true, value = "fmcg_ai_application")
public class ApplicationPO extends MongoPOBase {

    public static final String F_IDENTITY_KEY = "IK";

    public static final String F_PLAT_FORM = "PF";

    public static final String F_USE_REDIS = "UR";

    public static final String F_PARAMS = "PS";

    @Property(F_IDENTITY_KEY)
    private String identityKey;

    @Property(F_PLAT_FORM)
    private String platForm;

    @Property(F_USE_REDIS)
    private boolean useRedis;

    @Embedded(F_PARAMS)
    private Map<String, Object> params;
}
