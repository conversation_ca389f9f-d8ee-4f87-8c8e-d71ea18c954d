package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.fs.fmcg.sdk.ai.common.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/15 下午7:10
 */
@Slf4j
@Component(value ="huaWeiModelArtsDetectorImpl" )
public class HuaWeiModelArtsDetectorImpl implements Detector {


    @Resource
    private TokenFactory tokenFactory;


    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        String url = model.getParams().getString("url");
        Map<String, Object> headers = new HashMap<>();
        String tokenKey = Strings.isNullOrEmpty(model.getTokenKey()) ? AppEnum.HUAWEI_FSHUAWEICLOUD.value() : model.getTokenKey();
        headers.put("X-Auth-Token", tokenFactory.getToken(tokenKey));
        JSONObject rst = HttpUtil.multiFromFilePost(url, headers, null,"images",  "a.jpg", imageCache, JSONObject.class);
        log.info("huawei original rst:{}", rst);
        List<BoxDto> boxes = new ArrayList<>();

        if (rst != null && rst.containsKey("error_code")) {
            log.error("call huawei api err .arg:{}", arg);
            throw new AiProviderException("AI", "call huawei api err ." + rst.getString("error_msg"));
        }
        JSONArray clazzArray = rst.getJSONArray("detection_classes");
        JSONArray scoreArray = rst.getJSONArray("detection_scores");
        JSONArray boxArray = rst.getJSONArray("detection_boxes");
        for (int i = 0; i < clazzArray.size(); i++) {
            BoxDto dto = new BoxDto();
            dto.setName(clazzArray.getString(i));
            dto.setScore(scoreArray.getString(i));
            Double[] bnd = new Double[4];
            JSONArray tmp = boxArray.getJSONArray(i);

            bnd[0] = tmp.getDouble(0);
            bnd[1] = tmp.getDouble(1);
            bnd[2] = tmp.getDouble(2);
            bnd[3] = tmp.getDouble(3);
            dto.setBox(bnd);
            boxes.add(dto);
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }
}
