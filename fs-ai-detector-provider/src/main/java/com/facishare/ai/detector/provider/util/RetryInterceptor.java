package com.facishare.ai.detector.provider.util;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/3/13 下午12:55
 */
public class RetryInterceptor implements Interceptor {

    public static final Logger log = LoggerFactory.getLogger(RetryInterceptor.class);
    public int maxRetry = 1;
    private int count = 0 ;
    public RetryInterceptor(int maxRetry){
        this.maxRetry=maxRetry;
    }
    @Override
    public Response intercept(Chain chain) throws IOException {
        Response response = retry(chain);
        if(response == null){
            throw new IOException("网络波动，请稍后再试。");
        }
        return response;
    }

    public Response retry(Chain chain){
        Response response = null;
        Request request = chain.request();
        try {
            response = chain.proceed(request);
            while (!response.isSuccessful() && count < maxRetry) {
                log.info("request fail.status:{},msg:{}",response.code(),response.body().string());
                count++;
                response = retry(chain);
            }
        } catch (Exception e){
            log.info("http retry fail.",e);
            while (count < maxRetry){
                count++;
                response = retry(chain);
            }
        }
        return response;
    }
}
