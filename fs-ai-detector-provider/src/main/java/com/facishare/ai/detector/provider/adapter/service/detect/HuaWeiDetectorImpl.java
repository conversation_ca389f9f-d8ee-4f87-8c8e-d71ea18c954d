package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/9/22 下午3:58
 */
@Component(value = "huaweiDetectorImpl")
public class HuaWeiDetectorImpl implements Detector {


    private static final Logger log = LoggerFactory.getLogger(HuaWeiDetectorImpl.class);

    @Resource
    private TokenFactory tokenFactory;



    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        log.info("start huawei detect detect.arg:{},model:{} ", arg, model);
        JSONObject data = new JSONObject();
        data.put("threshold", model.getConfidence());
        data.put("image", Base64.encodeBase64String(imageCache));
        String url = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.HUAWEI_OBJECT_DETECT_POC);
        Map<String, String> header = new HashMap<>();
        String tokenKey = Strings.isNullOrEmpty(model.getTokenKey()) ? AppEnum.HUAWEI_FSHUAWEICLOUD.value() : model.getTokenKey();
        header.put("X-Auth-Token", tokenFactory.getToken(tokenKey));
        JSONObject result = (JSONObject) HttpUtil.post(url, header, data);

        log.info("object detect finished. {}", result);

        List<BoxDto> boxes = new ArrayList<>();

        if (result != null && result.containsKey("error_code")) {
            data.put("image", "");
            log.error("call huawei api err .arg:{}", data);
            throw new AiProviderException("AI", "call huawei api err ." + result.getString("error_msg"));
        }
        if (result != null ) {
            result.getJSONArray("result").forEach(v -> {
                BoxDto dto = new BoxDto();
                JSONObject o = (JSONObject) v;
                dto.setName(o.getString("Label"));
                dto.setScore(o.getString("Confidence"));
                Double[] bnd = new Double[4];
                String[] tmp = ((JSONObject) v).getString("Box").split(",");
                bnd[0] = Double.parseDouble(tmp[1]);
                bnd[1] = Double.parseDouble(tmp[0]);
                bnd[2] = Double.parseDouble(tmp[3]);
                bnd[3] = Double.parseDouble(tmp[2]);
                dto.setBox(bnd);
                boxes.add(dto);
            });

        } else {
            log.info("call huawei api but return null.arg:{}", arg);
            throw new AiProviderException("AI", "call huawei api but return null.");
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }
}
