package com.facishare.ai.detector.provider.dao.po;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-9-19  下午5:59
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity(value = "ai_detect_record", noClassnameStored = true)
public class DetectRecordPo extends MongoPOBase {

    public static final String F_MODEL_ID = "MI";
    public static final String F_ORIGINAL_PATH = "SIP";
    public static final String F_PROCESSED_PATH = "PIP";
    public static final String F_RESULT = "R";
    public static final String F_PATH = "P";
    public static final String F_CREATE_TIME = "CT";
    public static final String F_EXTRA_DATA = "ED";
    public static final String F_EXPIRED_DATE = "EDD";

    public DetectRecordPo(Integer tenantId, String modelId, String path, String originalPath, String processedPath, List<ObjectEntity> objects) {
        this.tenantId = tenantId;
        this.modelId = modelId;
        this.path = path;
        this.originalPath = originalPath;
        this.processedPath = processedPath;
        this.objects = objects;
        this.createTime = System.currentTimeMillis();
    }


    @Property(F_MODEL_ID)
    private String modelId;

    @Property(F_ORIGINAL_PATH)
    private String originalPath;

    @Property(F_PROCESSED_PATH)
    private String processedPath;

    @Property(F_PATH)
    private String path;

    @Embedded(F_RESULT)
    private List<ObjectEntity> objects;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Embedded(F_EXTRA_DATA)
    private Map<String, Object> extraData;

    @Property(F_EXPIRED_DATE)
    private Date expiredDate;
}
