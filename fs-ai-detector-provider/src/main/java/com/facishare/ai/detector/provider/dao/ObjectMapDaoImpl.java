package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.google.common.base.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ObjectMapDaoImpl extends DaoBase<ObjectMapPo> implements ObjectMapDao {

    @Override
    public List<ObjectMapPo> query(Integer tenantId, String modelId) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.field(ObjectMapPo.F_MODEL_ID).equal(modelId);
        return query.asList();
    }


    @Override
    public void batchAdd(List<ObjectMapPo> pos) {
        saveAll(pos);
    }

    @Override
    public Set<String> checkContainsIds(Integer tenantId, List<String> ids) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.field(ObjectMapPo.F_UNIQUE_ID).in(ids);
        return query.asList().stream().map(MongoPOBase::getUniqueId).collect(Collectors.toSet());
    }

    @Override
    public List<ObjectMapPo> queryByUniqueId(Integer tenantId, String modelId, List<String> uniqueIds) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.field(ObjectMapPo.F_MODEL_ID).equal(modelId);
        query.field(ObjectMapPo.F_UNIQUE_ID).in(uniqueIds);
        return query.asList();
    }

    @Override
    public Integer deleteByUniqueId(Integer tenantId, String modelId, List<String> uniqueIds) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        if (tenantId != null) {
            query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        }
        if (!Strings.isNullOrEmpty(modelId)) {
            query.field(ObjectMapPo.F_MODEL_ID).equal(modelId);
        }
        if (CollectionUtils.isNotEmpty(uniqueIds)) {
            query.or(query.criteria(ObjectMapPo.F_UNIQUE_ID).in(uniqueIds), query.criteria("_id").in(uniqueIds.stream().map(ObjectId::new).collect(Collectors.toList())));
        }
        if (CollectionUtils.isEmpty(uniqueIds) && Strings.isNullOrEmpty(modelId)) {
            return 0;
        }
        return dbContext.delete(query).getN();
    }

    @Override
    public Integer deleteByKeys(Integer tenantId, String modelId, List<String> keys) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.field(ObjectMapPo.F_MODEL_ID).equal(modelId);
        if (CollectionUtils.isNotEmpty(keys)) {
            query.field(ObjectMapPo.F_KEY).in(keys);
        }
        return dbContext.delete(query).getN();
    }

    @Override
    public List<ObjectMapPo> queryByKeys(Integer tenantId, String modelId, List<String> keys) {
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.field(ObjectMapPo.F_MODEL_ID).equal(modelId);
        if (CollectionUtils.isNotEmpty(keys)) {
            query.field(ObjectMapPo.F_KEY).in(keys);
        }
        return query.asList();
    }

    @Override
    public Integer updateWithMap(Integer tenantId, String id, Map<String, Object> map) {
        Query<ObjectMapPo> query = buildIdQuery(id, ObjectMapPo.class);
        UpdateOperations<ObjectMapPo> updateOperations = dbContext.createUpdateOperations(ObjectMapPo.class);
        if (MapUtils.isNotEmpty(map)) {
            map.forEach(updateOperations::set);
        }
        dbContext.update(query, updateOperations);
        return 1;
    }

    @Override
    public List<ObjectMapPo> batchUpdate(List<ObjectMapPo> pos, Integer tenantId) {
        pos.forEach(objectMapPo -> objectMapPo.setTenantId(tenantId));
        dbContext.save(pos);
        return pos;
    }

    @Override
    public List<ObjectMapPo> queryByTenantIdAndIds(Integer tenantId, List<String> ids) {
        // 根据tenantId和ids查询ObjectMapPo列表
        Query<ObjectMapPo> query = dbContext.createQuery(ObjectMapPo.class);
        query.field(ObjectMapPo.F_TENANT_ID).equal(tenantId);
        query.or(query.criteria(ObjectMapPo.F_UNIQUE_ID).in(ids), query.criteria("_id").in(ids.stream().map(ObjectId::new).collect(Collectors.toList())));
        // 执行查询
        return query.asList();
    }

}
