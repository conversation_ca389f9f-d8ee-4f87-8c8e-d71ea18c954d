package com.facishare.ai.detector.provider.util;

import com.google.common.collect.Sets;

import java.util.Set;

public class CommonUtil {

    private CommonUtil() {
    }

    public static final String JPG = "jpg";
    public static final String DOT_JPG = ".jpg";
    public static final String DOT_0_JPG = "0.jpg";
    public static final String DOT_1_JPG = "1.jpg";
    public static final String DOT_2_JPG = "2.jpg";
    public static final String DOT_3_JPG = "3.jpg";

    public static Set<String> queryRelatedPath(String path) {
        Set<String> arr = Sets.newHashSet();
        arr.add(path);
        if (!path.toLowerCase().endsWith(DOT_JPG)) {
            arr.add(path + DOT_JPG);
            arr.add(path + DOT_0_JPG);
            arr.add(path + DOT_1_JPG);
            arr.add(path + DOT_2_JPG);
            arr.add(path + DOT_3_JPG);
        } else {
            arr.add(path.substring(0, path.length() - 4));
            if (path.toLowerCase().endsWith(DOT_0_JPG)
                    || path.toLowerCase().endsWith(DOT_1_JPG)
                    || path.toLowerCase().endsWith(DOT_2_JPG)
                    || path.toLowerCase().endsWith(DOT_3_JPG)
            ) {
                String realPath = path.substring(0, path.length() - 5);
                arr.add(realPath);
                arr.add(realPath + DOT_JPG);
                arr.add(realPath + DOT_0_JPG);
                arr.add(realPath + DOT_1_JPG);
                arr.add(realPath + DOT_2_JPG);
                arr.add(realPath + DOT_3_JPG);
            }
        }
        return arr;
    }
}
