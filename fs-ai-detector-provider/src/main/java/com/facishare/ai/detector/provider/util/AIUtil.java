package com.facishare.ai.detector.provider.util;

import com.facishare.ai.detector.api.dto.BoxDto;
import com.google.common.collect.Lists;

import java.util.*;

/**
 * <AUTHOR>
 * @date 19-12-6  下午6:58
 */
public class AIUtil {

    private static final double X_RANGE = 0.5;
    private static final double Y_RANGE_RATIO = 0.25;

    public static List<BoxDto> countRowFace(List<BoxDto> boxes) {
        if (boxes == null || boxes.isEmpty())
            return new ArrayList<>();
        boxes.sort((a, b) -> (int) (a.getBox()[1] - b.getBox()[1]));
        Map<BoxDto, Boolean> visited = new HashMap<>();
        List<List<BoxDto>> faces = new ArrayList<>();
        for (BoxDto item : boxes) {
            if (visited.containsKey(item)) {
                continue;
            }
            visited.put(item, true);
            List<BoxDto> group = Lists.newArrayList(item);
            for (BoxDto nextItem : boxes) {
                if (item.equals(nextItem)) {
                    continue;
                }
                double width = item.getBox()[3] - item.getBox()[1];
                if (item.getBox()[1] <= nextItem.getBox()[1] && item.getBox()[1] + X_RANGE * width >= nextItem.getBox()[1] && !visited.containsKey(nextItem)) {
                    visited.put(nextItem, true);
                    group.add(nextItem);
                }
            }
            group.sort((a, b) -> (int) (a.getBox()[0] - b.getBox()[0]));
            dealRowFaceGroup(group, visited, faces);
        }
        List<BoxDto> result = new ArrayList<>();
        for (List<BoxDto> face : faces) {
            if (face.size() > 1) {
                BoxDto tmp = new BoxDto();
                tmp.setName(face.get(0).getName());
                tmp.setScore(face.get(0).getScore());
                Double[] position = face.get(0).getBox().clone();
                Double[] last = face.get(face.size() - 1).getBox();
                position[2] = last[2];
                position[3] = last[3];
                tmp.setBox(position);
                tmp.setComponents(face.size());
                tmp.setIsRotated(face.get(0).getIsRotated());
                tmp.setIsFront(face.get(0).getIsFront());
                result.add(tmp);
            } else
                result.add(face.get(0));
        }
        for (BoxDto box : boxes) {
            if (!visited.containsKey(box)) {
                result.add(box);
            }
        }
        return result;
    }

    private static void dealRowFaceGroup(List<BoxDto> group, Map<BoxDto, Boolean> visited, List<List<BoxDto>> faces) {
        List<BoxDto> face = Lists.newArrayList(group.get(0));
        for (int i = 1; i < group.size(); i++) {
            BoxDto now = group.get(i);
            BoxDto pre = group.get(i - 1);
            double space = Math.max(now.getBox()[2] - now.getBox()[0], pre.getBox()[2] - pre.getBox()[0]);
            if (now.getBox()[0] - pre.getBox()[2] < Y_RANGE_RATIO * space && now.getName().equals(pre.getName())) {
                face.add(now);
            } else {
                if (face.size() > 1) {
                    faces.add(face);
                } else {
                    visited.remove(face.get(0));
                }
                face = Lists.newArrayList(now);
            }
        }
        if (face.size() > 1) {
            faces.add(face);
        } else {
            visited.remove(face.get(0));
        }
    }
}
