package com.facishare.ai.detector.provider.dao.po;

import org.mongodb.morphia.annotations.Property;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 19-10-15  下午7:53
 */
@Data
@ToString
public class ClassEntity {

    public static  final String F_CLASS_NAME = "CN";

    public static  final String F_CONFIDENCE = "CDC";
    @Property(F_CLASS_NAME)
    private String className;

    @Property(F_CONFIDENCE)
    private Double confidence;
}
