package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.ObjectMapDto;
import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.service.ObjectMapService;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.util.ConvertUtil;
import com.facishare.ai.detector.provider.util.FmcgBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 19-9-26  上午10:49
 */
public class ObjectMapServiceImpl implements ObjectMapService {

    private static final Logger log = LoggerFactory.getLogger(ObjectMapServiceImpl.class);

    @Resource
    private ObjectMapDao objectMapDao;


    @Override
    public BatchAddObjectMapResult batchAddObjectMap(BatchAddObjectMapArg arg) {
        BatchAddObjectMapResult rst = new BatchAddObjectMapResult();
        rst.setStatus("SUCCESS");
        try {
            List<ObjectMapPo> pos = arg.getMaps().stream().map(v -> {
                ObjectMapPo po = new ObjectMapPo();
                FmcgBeanUtil.copyProperties(v, po);
                return po;
            }).collect(Collectors.toList());
            objectMapDao.batchAdd(pos);
            List<ObjectMapDto> dtoList = pos.stream().map(ConvertUtil::convertToDto).collect(Collectors.toList());
            rst.setObjectMapDtoList(dtoList);
        } catch (Exception e) {
            rst.setMsg(e.toString());
            rst.setStatus("FAILURE");
            log.error("batchAddObjectMap err.", e);
        }
        return rst;
    }

    @Override
    public DeleteModelMappingResult deleteModelMapping(DeleteModelMappingArg arg) {
        log.info("arg:{}", arg);
        if (arg.getTenantId() == null && CollectionUtils.isEmpty(arg.getUniqueIds())) {
            return new DeleteModelMappingResult("企业id和uniqueIds不能同时为空");
        }
        if (arg.getTenantId() != null && CollectionUtils.isNotEmpty(arg.getKeys())) {
            return new DeleteModelMappingResult(objectMapDao.deleteByKeys(arg.getTenantId(), arg.getModelId(), arg.getKeys()));
        }
        return new DeleteModelMappingResult(objectMapDao.deleteByUniqueId(arg.getTenantId(), arg.getModelId(), arg.getUniqueIds()));
    }

    @Override
    public UpdateObjectMapWithMapResult updateObjectMapWithMap(UpdateObjectMapWithMapArg arg) {
        log.info("arg:{}", arg);
        return new UpdateObjectMapWithMapResult(objectMapDao.updateWithMap(arg.getTenantId(), arg.getId(), arg.getUpdateMap()));
    }

    @Override
    public QueryObjectMapByKeysResult queryObjectMapByKeys(QueryObjectMapByKeysArg arg) {
        log.info("arg:{}", arg);
        List<ObjectMapPo> pos = objectMapDao.queryByKeys(arg.getTenantId(), arg.getModelId(), arg.getKeys());
        return new QueryObjectMapByKeysResult(pos.stream().map(po -> {
            ObjectMapDto dto = new ObjectMapDto();
            FmcgBeanUtil.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public QueryObjectMapByModelIdResult queryObjectMapByModelIdAndTenantId(QueryObjectMapByModelIdArg arg) {
        log.info("arg:{}", arg);
        List<ObjectMapPo> pos = objectMapDao.query(arg.getTenantId(), arg.getModelId());
        return new QueryObjectMapByModelIdResult(pos.stream().map(po -> {
            ObjectMapDto dto = new ObjectMapDto();
            FmcgBeanUtil.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public QueryObjectMapByIdsResult queryObjectMapByIds(QueryObjectMapByIdsArg arg) {
        log.info("queryObjectMapByIds arg:{}", arg);

        try {
            // 参数校验
            if (arg.getTenantId() == null) {
                throw new IllegalArgumentException("企业ID不能为空");
            }
            if (CollectionUtils.isEmpty(arg.getIds())) {
                throw new IllegalArgumentException("数据ID列表不能为空");
            }

            // 根据企业ID和数据ID列表查询
            List<ObjectMapPo> pos = objectMapDao.queryByTenantIdAndIds(arg.getTenantId(), arg.getIds());

            // 将PO转换为DTO
            List<ObjectMapDto> dtoList = pos.stream().map(po -> {
                ObjectMapDto dto = new ObjectMapDto();
                FmcgBeanUtil.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());

            QueryObjectMapByIdsResult result = new QueryObjectMapByIdsResult();
            result.setObjectMapList(dtoList);
            return result;

        } catch (Exception e) {
            log.error("queryObjectMapByIds error", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public BatchUpdateObjectMapResult batchUpdateObjectMap(BatchUpdateObjectMapArg arg) {
        log.info("batchUpdateObjectMap arg:{}", arg);

        try {
            // 将DTO转换为PO
            List<ObjectMapPo> olds = objectMapDao.queryByTenantIdAndIds(arg.getTenantId(), arg.getObjectMapList().stream().map(ObjectMapDto::getUniqueId).collect(Collectors.toList()));
            Map<String, ObjectId> unique2Id = olds.stream().collect(Collectors.toMap(ObjectMapPo::getUniqueId, ObjectMapPo::getOriginalId, (a, b) -> a));
            List<ObjectMapPo> pos = arg.getObjectMapList().stream().map(dto -> {
                ObjectMapPo po = new ObjectMapPo();
                FmcgBeanUtil.copyProperties(dto, po);
                po.setId(unique2Id.getOrDefault(dto.getUniqueId(), new ObjectId(dto.getUniqueId())));
                return po;
            }).collect(Collectors.toList());

            // 批量更新
            List<ObjectMapPo> updatedPos = objectMapDao.batchUpdate(pos, arg.getTenantId());

            // 将更新后的PO转换回DTO
            List<ObjectMapDto> updatedDtos = updatedPos.stream().map(po -> {
                ObjectMapDto dto = new ObjectMapDto();
                FmcgBeanUtil.copyProperties(po, dto);
                return dto;
            }).collect(Collectors.toList());

            BatchUpdateObjectMapResult result = new BatchUpdateObjectMapResult();
            result.setObjectMapDtoList(updatedDtos);
            return result;

        } catch (Exception e) {
            log.error("batchUpdateObjectMap error", e);
            throw new RuntimeException("批量更新失败: " + e.getMessage());
        }
    }


}
