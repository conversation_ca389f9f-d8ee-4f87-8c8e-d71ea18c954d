package com.facishare.ai.detector.provider.facade;

import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.service.ModelService;
import com.facishare.ai.detector.api.service.ObjectMapService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 19-10-14  下午6:26
 */
@RestController
@RequestMapping(value = "/model")
public class ModelController {

    @Resource
    private ModelService modelService;

    @Resource
    private ObjectMapService objectMapService;

    @RequestMapping(value = "/batchAddModel")
    public BatchAddModelResult batchAddModel(@RequestBody BatchAddModelArg arg) {
        return modelService.batchAddModel(arg);
    }


    @RequestMapping(value = "/BatchAddObjectMap")
    public BatchAddObjectMapResult batchAddObjectMap(@RequestBody BatchAddObjectMapArg arg) {
        return objectMapService.batchAddObjectMap(arg);
    }

    @RequestMapping(value = "/getModelsByTenantId")
    public GetModelsByTenantIdResult getModelsByTenantId(@RequestBody GetModelsByTenantIdArg arg) {
        return modelService.getModelsByTenantId(arg);
    }

    @RequestMapping(value = "/saveModel")
    public SaveModelResult getModelsByTenantId(@RequestBody SaveModelArg arg) {
        return modelService.saveModel(arg);
    }

    @RequestMapping(value = "/updateModel")
    public UpdateModelResult updateModel(@RequestBody UpdateModelArg arg) {
        return modelService.updateModel(arg);
    }

    @RequestMapping(value = "/copyModelAndMapping")
    public CopyModelAndMappingResult copyModelAndMapping(@RequestBody CopyModelAndMappingArg arg) {
        return modelService.copyModelAndMapping(arg);
    }

    @RequestMapping(value = "/deleteModelMapping")
    public DeleteModelMappingResult deleteModelMapping(@RequestBody DeleteModelMappingArg arg){
        return objectMapService.deleteModelMapping(arg);
    }
}
