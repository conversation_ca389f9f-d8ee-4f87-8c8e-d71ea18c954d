// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: XDService.proto

package com.facishare.ai.detector.provider.adapter.grpc.yq;

public interface ResultTrackOrBuilder extends
    // @@protoc_insertion_point(interface_extends:XDService.ResultTrack)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string imgId = 1;</code>
   * @return The imgId.
   */
  String getImgId();
  /**
   * <code>string imgId = 1;</code>
   * @return The bytes for imgId.
   */
  com.google.protobuf.ByteString
      getImgIdBytes();

  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  String getCode();
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>string result = 3;</code>
   * @return The result.
   */
  String getResult();
  /**
   * <code>string result = 3;</code>
   * @return The bytes for result.
   */
  com.google.protobuf.ByteString
      getResultBytes();
}
