package com.facishare.ai.detector.provider.service;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.ai.detector.api.dto.arg.IdCardDetectArg;
import com.facishare.ai.detector.api.dto.arg.VATInvoiceDetectArg;
import com.facishare.ai.detector.api.dto.result.IdCardDetectResult;
import com.facishare.ai.detector.api.dto.result.VATInvoiceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.OcrService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.OcrDetector;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.IdCardDetectAO;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.VATInvoiceDetectAO;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.qixin.converter.QXEIEAConverterImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/3 下午2:34
 */
public class OcrServiceImpl implements OcrService {


    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private OcrDetector ocrDetector;

    @Resource
    private DetectCounterDao detectCounterDao;

    @Resource
    private QXEIEAConverterImpl qxeieaConverter;

    private static final Logger log = LoggerFactory.getLogger(OcrServiceImpl.class);

    @Override
    public IdCardDetectResult idCardDetect(IdCardDetectArg arg) throws AiProviderException, IOException {
        log.info("id card detect. arg:{}",arg);
        IdCardDetectResult result = new IdCardDetectResult();
        if(StringUtils.isNotEmpty(arg.getImagePath())||StringUtils.isNotEmpty(arg.getTenantAccount())){
            result.setErrorCode(600);
            result.setErrorMsg("account or imagePath is null.");
        }
        long start = System.currentTimeMillis();
        byte[] image = fileAdapter.downloadByPath(arg.getTenantAccount(),arg.getImagePath());
        long imageTime = System.currentTimeMillis();
        log.info("download pic cost:{}ms",imageTime-start);
        IdCardDetectAO ao = ocrDetector.idCardDetect(image,arg.getCardSide(),arg.getDetectDirection(),arg.getDetectRisk(),arg.getDetectPhoto(),arg.getDetectRectify());
        log.info("id card detect  cost:{}ms",System.currentTimeMillis()-start);
        if(ao.getErrorCode()==0||ao.getErrorCode()==216633){
            detectCounterDao.success(qxeieaConverter.enterpriseAccountToId(arg.getTenantAccount()), ConstantUtil.ID_CARD_DETECT);
        }else {
            detectCounterDao.fail(qxeieaConverter.enterpriseAccountToId(arg.getTenantAccount()), ConstantUtil.ID_CARD_DETECT);
        }
        BeanUtils.copyProperties(ao,result);
        return result;
    }

    @Override
    public VATInvoiceDetectResult vatInvoiceDetect(VATInvoiceDetectArg arg) throws AiProviderException, IOException {
        log.info("vat invoice  detect. arg:{}",arg);
        VATInvoiceDetectResult result = new VATInvoiceDetectResult();
        if(StringUtils.isNotEmpty(arg.getImagePath())||StringUtils.isNotEmpty(arg.getTenantAccount())){
            result.setErrorCode(600);
            result.setErrorMsg("account or imagePath is null.");
        }
        long start = System.currentTimeMillis();
        byte[] image = fileAdapter.downloadByPath(arg.getTenantAccount(),arg.getImagePath());
        long imageTime = System.currentTimeMillis();
        log.info("download pic cost:{}ms",imageTime-start);
        VATInvoiceDetectAO ao = ocrDetector.VATInvoiceDetect(image,arg.getAccuracy(),arg.getType());
        log.info("vat detect  cost:{}ms",System.currentTimeMillis()-start);
        BeanUtils.copyProperties(ao,result);
        if(ao.getProducts()!=null && !ao.getProducts().isEmpty()){
            List<VATInvoiceDetectResult.Item> list  = new ArrayList<>();
            result.setProducts(list);
            ao.getProducts().forEach(v->{
                VATInvoiceDetectResult.Item it = new VATInvoiceDetectResult.Item();
                BeanUtils.copyProperties(v,it);
                list.add(it);
            });
        }
        if(ao.getErrorCode()==0||ao.getErrorCode()==216633){
            detectCounterDao.success(qxeieaConverter.enterpriseAccountToId(arg.getTenantAccount()), ConstantUtil.VAT_INVOICE_DETECT);
        }else {
            detectCounterDao.fail(qxeieaConverter.enterpriseAccountToId(arg.getTenantAccount()), ConstantUtil.VAT_INVOICE_DETECT);
        }
        return result;
    }
}
