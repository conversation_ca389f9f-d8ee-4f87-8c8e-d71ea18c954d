package com.facishare.ai.detector.provider.util;

import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;

import com.facishare.ai.detector.api.dto.DtoBase;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;

public class FmcgBeanUtil {

    public static void copyProperties(Object source, Object target) {
        BeanUtils.copyProperties(source, target);
        if(source instanceof MongoPOBase && target instanceof DtoBase){
            MongoPOBase sourcePO = (MongoPOBase) source;
            DtoBase targetDto = (DtoBase) target;
            if(sourcePO.getId() != null && ObjectId.isValid(sourcePO.getId().toString())){
                targetDto.setId(sourcePO.getId().toString());
            }
        }
        if(target instanceof MongoPOBase && source instanceof DtoBase){
            MongoPOBase targetPO = (MongoPOBase) target;
            DtoBase sourceDto = (DtoBase) source;
            if(sourceDto.getId() != null && ObjectId.isValid(sourceDto.getId())){
                targetPO.setUniqueId(sourceDto.getId());
            }
        }
    }
}
