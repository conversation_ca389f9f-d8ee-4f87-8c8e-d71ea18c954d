package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam;
import com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack;
import com.facishare.ai.detector.provider.adapter.grpc.yq.XDRecogizeServiceGrpc;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.protobuf.ByteString;
import io.grpc.HttpConnectProxiedSocketAddress;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/29 下午3:04
 */

@Component("yqDetectorImpl")
public class YQDetectorImpl implements Detector {

    private Logger logger = LoggerFactory.getLogger(YQDetectorImpl.class);

    private ManagedChannel channel;

    private XDRecogizeServiceGrpc.XDRecogizeServiceBlockingStub blockingStub;

    private String GRPC_HOST = "**************";

    private Integer GRPC_PORT = 50101;

    private String PROXY = "";

    private static final String CONFIG_NAME = "fs-fmcg-sdk-apis";

    private static final String CONFIG_FIELD = "grpc_config";

    @PostConstruct
    public void init() {
        loadConfig();
        initChannel();
    }

    private void initChannel() {
        if (channel != null && !channel.isShutdown()) {
            channel.shutdown();
        }
        ManagedChannelBuilder builder = ManagedChannelBuilder.forAddress(GRPC_HOST, GRPC_PORT).usePlaintext();
        if (!Strings.isNullOrEmpty(PROXY)) {
            String [] proxyStr = PROXY.split(":");
            builder.proxyDetector((socketAddress) -> {
                SocketAddress resolvedProxyAddr = new InetSocketAddress(proxyStr[0],Integer.parseInt(proxyStr[1]));
                return  HttpConnectProxiedSocketAddress.newBuilder()
                        .setTargetAddress((InetSocketAddress) socketAddress)
                        .setProxyAddress(resolvedProxyAddr).build();
            });
        }
        channel = builder.build();
        blockingStub = XDRecogizeServiceGrpc.newBlockingStub(channel);
    }

    public void loadConfig() {
        ConfigFactory.getConfig(CONFIG_NAME, iConfig -> {
            String jsonString = iConfig.get(CONFIG_FIELD);
            if (!Strings.isNullOrEmpty(jsonString)) {
                JSONObject allHost = JSON.parseObject(jsonString);
                JSONObject yqHost = allHost.getJSONObject("yqsl");
                GRPC_HOST = yqHost.getString("host");
                GRPC_PORT = yqHost.getInteger("port");
                PROXY = yqHost.getString("proxy");
            }
            initChannel();
        });
    }


    @PreDestroy
    public void destroy() {
        if (channel != null && !channel.isShutdown()) {
            channel.shutdown();
        }
    }

    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        RequestParam requestParam = RequestParam.newBuilder().setImgId(arg.getPath()).setImage(ByteString.copyFrom(imageCache)).build();
        ResultTrack result = blockingStub.xDRecogize(requestParam);
        List<BoxDto> boxes = new ArrayList<>();
        if ("00000".equals(result.getCode())) {
            JSONObject resultObj = JSON.parseObject(result.getResult());
            resultObj.getJSONArray("detail").stream().map(o -> (JSONObject) o).forEach(detail -> {
                JSONObject location = detail.getJSONObject("location");
                Double[] size = new Double[4];
                size[0] = location.getDouble("top");
                size[1] = location.getDouble("left");
                size[2] = location.getDouble("top") + location.getDouble("height");
                size[3] = location.getDouble("left") + location.getDouble("width");

                BoxDto box = new BoxDto();
                box.setScore(detail.getDouble("score").toString());
                box.setName(detail.getString("sku_code"));
                box.setBox(size);
                if (model.getConfidence() != null && Double.parseDouble(box.getScore()) >= model.getConfidence()) {
                    boxes.add(box);
                }
            });
        } else {
            logger.error("detect err.rst:{}", result);
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }
}
