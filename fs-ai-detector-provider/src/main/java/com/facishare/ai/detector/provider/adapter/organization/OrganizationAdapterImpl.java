package com.facishare.ai.detector.provider.adapter.organization;

import com.facishare.ai.detector.api.dto.share.EmployeeInfoVo;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.*;
import com.facishare.organization.adapter.api.model.biz.department.result.*;
import com.facishare.organization.adapter.api.model.biz.departmentmember.MainDepartment;
import com.facishare.organization.adapter.api.model.biz.employee.Employee;
import com.facishare.organization.adapter.api.model.biz.employee.FindEmployeeByNames;
import com.facishare.organization.adapter.api.model.biz.employee.arg.*;
import com.facishare.organization.adapter.api.model.biz.employee.result.*;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeesDtoByDepartmentIdResult;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrganizationAdapterImpl implements OrganizationAdapter {

    @Resource
    private EmployeeService employeeService;

    @Resource
    private DepartmentService departmentService;

    private static final String SEPARATOR = ",";

    @Override
    public List<EmployeeDto> batchGetEmployees(int ei, List<Integer> employeeIds) {
        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeIds(employeeIds);
        arg.setRunStatus(RunStatus.ALL);
        BatchGetEmployeeDtoResult rst = employeeService.batchGetEmployeeDto(arg);
        return rst.getEmployees();
    }

    @Override
    public List<EmployeeInfoVo> queryEmployees(int ei, List<Integer> employeeIds) {
        BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeIds(employeeIds);
        arg.setRunStatus(RunStatus.ALL);
        BatchGetEmployeeDtoResult rst = employeeService.batchGetEmployeeDto(arg);
        List<Integer> departmentIds = Lists.newArrayList();

        List<EmployeeInfoVo> result = rst.getEmployees().stream().map(dto -> {

            EmployeeInfoVo employee = new EmployeeInfoVo();
            employee.setEmployeeId(dto.getEmployeeId());
            employee.setProfile(dto.getProfileImage());
            employee.setMobile(dto.getMobile());
            employee.setName(dto.getName());
            if (dto.getMainDepartmentIds() != null) {
                employee.setMainDepartmentId(dto.getMainDepartmentIds().stream().findFirst().orElse(0));
                if (employee.getMainDepartmentId() != 0) {
                    departmentIds.add(employee.getMainDepartmentId());
                }
            }
            employee.setMainDepartment(null);
            employee.setPost(dto.getPost());
            employee.setDepartments(null);
            return employee;
        }).collect(Collectors.toList());

        Map<Integer, String> departments = this.batchGetDepartments(ei, departmentIds).stream().collect(Collectors.toMap(Department::getDepartmentId, Department::getName));
        for (EmployeeInfoVo employee : result) {
            if (employee.getMainDepartmentId() != 0) {
                employee.setMainDepartment(departments.get(employee.getMainDepartmentId()));
            }
        }
        return result;
    }

    @Override
    public EmployeeInfoVo getEmployee(int ei, int employeeId) {

        List<Integer> employeeIds = new ArrayList<>();
        employeeIds.add(employeeId);
        EmployeeDto source = batchGetEmployees(ei, employeeIds).stream().findFirst().orElse(null);
        if (source == null) {
            return null;
        }
        List<Department> departmentDtoList = batchGetDepartments(ei, source.getDepartmentIds());
        EmployeeInfoVo employeeInfoDTO = new EmployeeInfoVo();

        employeeInfoDTO.setEmployeeId(source.getEmployeeId());
        employeeInfoDTO.setName(source.getName());
        employeeInfoDTO.setPost(source.getPost());
        employeeInfoDTO.setProfile(source.getProfileImage());
        employeeInfoDTO.setMobile(source.getMobile());

        employeeInfoDTO.setMainDepartmentId(source.getMainDepartmentIds().stream().findFirst().orElse(0));

        String main = StringUtils.join(departmentDtoList.stream()
                .filter(f -> source.getMainDepartmentIds().contains(f.getDepartmentId()))
                .map(Department::getName).collect(Collectors.toList()), SEPARATOR);
        employeeInfoDTO.setMainDepartment(main);

        String departments = StringUtils.join(departmentDtoList.stream()
                .filter(f -> !source.getMainDepartmentIds().contains(f.getDepartmentId()))
                .map(Department::getName).collect(Collectors.toList()), SEPARATOR);
        employeeInfoDTO.setDepartments(departments);

        return employeeInfoDTO;
    }

    @Override
    public List<Integer> getEmployeeIds(int ei, List<Integer> departmentIds) {
        BatchGetEmployeeIdsByDepartmentIdsArg arg = new BatchGetEmployeeIdsByDepartmentIdsArg();
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(true);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setMainDepartment(MainDepartment.MAIN);
        BatchGetEmployeeIdsByDepartmentIdResult rst = employeeService.batchGetEmployeeIdsByDepartmentIds(arg);
        return rst.getEmployeeIds();
    }

    @Override
    public List<Integer> getAllEmployeeIds(int ei, List<Integer> departmentIds) {
        BatchGetEmployeeIdsByDepartmentIdsArg arg = new BatchGetEmployeeIdsByDepartmentIdsArg();
        arg.setEnterpriseId(ei);
        arg.setIncludeLowDepartment(true);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setMainDepartment(MainDepartment.ALL);
        BatchGetEmployeeIdsByDepartmentIdResult rst = employeeService.batchGetEmployeeIdsByDepartmentIds(arg);
        return rst.getEmployeeIds();
    }

    @Override
    public List<Department> batchGetDepartments(int ei, List<Integer> departmentIds) {
        BatchGetDepartmentArg arg = new BatchGetDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        return departmentService.batchGetDepartment(arg).getDepartments();
    }

    @Override
    public Department getDepartment(int ei, Integer departmentId) {
        BatchGetDepartmentArg arg = new BatchGetDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentIds(Lists.newArrayList());
        arg.getDepartmentIds().add(departmentId);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        return departmentService.batchGetDepartment(arg).getDepartments().stream().findFirst().orElse(null);
    }

    @Override
    public List<Integer> getDepartmentByPrincipalIds(Integer ei, Integer employeeId, List<Integer> principalIds) {
        BatchGetDepartmentByPrincipalArg batchGetDepartmentByPrincipalArg = new BatchGetDepartmentByPrincipalArg();
        batchGetDepartmentByPrincipalArg.setEnterpriseId(ei);
        batchGetDepartmentByPrincipalArg.setCurrentEmployeeId(employeeId);
        batchGetDepartmentByPrincipalArg.setPrincipalIds(principalIds);
        batchGetDepartmentByPrincipalArg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        BatchGetDepartmentByPrincipalResult batchGetDepartmentByPrincipalResult = departmentService.batchGetDepartmentByPrincipal(batchGetDepartmentByPrincipalArg);
        return batchGetDepartmentByPrincipalResult.getDepartments().stream().map(DepartmentDto::getDepartmentId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getSubordinateEmployeeIds(Integer ei, Integer employeeId) {
        GetSubordinateEmployeesArg getSubordinateEmployeesArg = new GetSubordinateEmployeesArg();
        getSubordinateEmployeesArg.setRunStatus(com.facishare.organization.adapter.api.model.biz.RunStatus.ACTIVE);
        getSubordinateEmployeesArg.setLeaderId(employeeId);
        getSubordinateEmployeesArg.setEnterpriseId(ei);
        getSubordinateEmployeesArg.setCurrentEmployeeId(employeeId);

        GetSubordinateEmployeesResult getSubordinateEmployeesResult = employeeService.getSubordinateEmployees(getSubordinateEmployeesArg);
        return getSubordinateEmployeesResult.getEmployees().stream().map(Employee::getEmployeeId).collect(Collectors.toList());
    }

    @Override
    public GetAllEmployeesResult getAllEmployees(Integer ei) {

        GetAllEmployeesArg arg = new GetAllEmployeesArg();
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(ei);
        return employeeService.getAllEmployees(arg);
    }

    @Override
    public FindEmployeeByNames.Result getEmployeesByNames(Integer ei,List<String> names) {
        FindEmployeeByNames.Argument arg = new FindEmployeeByNames.Argument();
        arg.setEnterpriseId(ei);
        arg.setNickName(names);
        arg.setRunStatus(RunStatus.ACTIVE);
        return employeeService.findEmployeeByNames(arg);
    }

    @Override
    public GetAllDepartmentResult getAllDepartments(int ei, int currentEmployeeId) {
        GetAllDepartmentArg arg = new GetAllDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setCurrentEmployeeId(currentEmployeeId);
        arg.setRunStatus(com.facishare.organization.api.model.RunStatus.ACTIVE);
        return departmentService.getAllDepartment(arg);
    }

    @Override
    public GetLowDepartmentResult getLowDepartment(int ei, int departmentId) {
        GetLowDepartmentArg arg = new GetLowDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentId(departmentId);
        return departmentService.getLowDepartment(arg);
    }

    @Override
    public BatchGetEmployeesDtoByDepartmentIdResult batchGetEmployeesDtoByDepartmentIds(Integer ei, List<Integer> departmentIds) {
        BatchGetEmployeesDtoByDepartmentIdsArg arg = new BatchGetEmployeesDtoByDepartmentIdsArg();
        arg.setEnterpriseId(ei);
        arg.setMainDepartment(MainDepartment.MAIN);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(RunStatus.ACTIVE);
        return employeeService.batchGetEmployeeDtoByDepartmentIds(arg);
    }

    @Override
    public BatchGetEmployeesDtoByDepartmentIdResult batchGetAllEmployeesDtoByDepartmentIds(Integer ei, List<Integer> departmentIds) {
        BatchGetEmployeesDtoByDepartmentIdsArg arg = new BatchGetEmployeesDtoByDepartmentIdsArg();
        arg.setEnterpriseId(ei);
        arg.setMainDepartment(MainDepartment.ALL);
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(RunStatus.ACTIVE);
        return employeeService.batchGetEmployeeDtoByDepartmentIds(arg);
    }

    @Override
    public GetChildrenDepartmentResult getChildrenDepartment(Integer ei, Integer departmentId) {
        GetChildrenDepartmentArg arg = new GetChildrenDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentId(departmentId);
        arg.setSelf(false);
        return departmentService.getChildrenDepartment(arg);
    }

    @Override
    public Department getParentDepartment(Integer ei, Integer employeeId, Integer departmentId) {
        GetParentDepartmentArg arg = new GetParentDepartmentArg();
        arg.setEnterpriseId(ei);
        arg.setDepartmentId(departmentId);
        arg.setCurrentEmployeeId(employeeId);
        GetParentDepartmentResult result = departmentService.getParentDepartment(arg);
        return result.getDepartment();
    }

    @Override
    public List<Integer> getAllDepartmentIdsOfEmployee(Integer ei, Integer employeeId) {
        GetDepartmentByEmployeeIdArg arg = new GetDepartmentByEmployeeIdArg();
        arg.setEnterpriseId(ei);
        arg.setEmployeeId(employeeId);
        arg.setCurrentEmployeeId(employeeId);
        GetDepartmentByEmployeeIdResult rst = departmentService.getDepartmentByEmployeeId(arg);
        if(rst.getDepartments().isEmpty())
            return new ArrayList<>();
        return rst.getDepartments().get(0).getAncestors();
    }
}
