// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: XDService.proto

package com.facishare.ai.detector.provider.adapter.grpc.yq;

/**
 * Protobuf type {@code XDService.RequestParam}
 */
public final class RequestParam extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:XDService.RequestParam)
        RequestParamOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RequestParam.newBuilder() to construct.
  private RequestParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RequestParam() {
    imgId_ = "";
    image_ = com.google.protobuf.ByteString.EMPTY;
  }

  @Override
  @SuppressWarnings({"unused"})
  protected Object newInstance(
      UnusedPrivateParameter unused) {
    return new RequestParam();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RequestParam(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            String s = input.readStringRequireUtf8();

            imgId_ = s;
            break;
          }
          case 18: {

            image_ = input.readBytes();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_RequestParam_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_RequestParam_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.class, com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.Builder.class);
  }

  public static final int IMGID_FIELD_NUMBER = 1;
  private volatile Object imgId_;
  /**
   * <code>string imgId = 1;</code>
   * @return The imgId.
   */
  @Override
  public String getImgId() {
    Object ref = imgId_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs =
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      imgId_ = s;
      return s;
    }
  }
  /**
   * <code>string imgId = 1;</code>
   * @return The bytes for imgId.
   */
  @Override
  public com.google.protobuf.ByteString
      getImgIdBytes() {
    Object ref = imgId_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b =
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      imgId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMAGE_FIELD_NUMBER = 2;
  private com.google.protobuf.ByteString image_;
  /**
   * <code>bytes image = 2;</code>
   * @return The image.
   */
  @Override
  public com.google.protobuf.ByteString getImage() {
    return image_;
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getImgIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, imgId_);
    }
    if (!image_.isEmpty()) {
      output.writeBytes(2, image_);
    }
    unknownFields.writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getImgIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, imgId_);
    }
    if (!image_.isEmpty()) {
      size += com.google.protobuf.CodedOutputStream
        .computeBytesSize(2, image_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam)) {
      return super.equals(obj);
    }
    com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam other = (com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam) obj;

    if (!getImgId()
        .equals(other.getImgId())) return false;
    if (!getImage()
        .equals(other.getImage())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + IMGID_FIELD_NUMBER;
    hash = (53 * hash) + getImgId().hashCode();
    hash = (37 * hash) + IMAGE_FIELD_NUMBER;
    hash = (53 * hash) + getImage().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code XDService.RequestParam}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:XDService.RequestParam)
      com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParamOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_RequestParam_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_RequestParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.class, com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.Builder.class);
    }

    // Construct using com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @Override
    public Builder clear() {
      super.clear();
      imgId_ = "";

      image_ = com.google.protobuf.ByteString.EMPTY;

      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_RequestParam_descriptor;
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam getDefaultInstanceForType() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.getDefaultInstance();
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam build() {
      com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam buildPartial() {
      com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam result = new com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam(this);
      result.imgId_ = imgId_;
      result.image_ = image_;
      onBuilt();
      return result;
    }

    @Override
    public Builder clone() {
      return super.clone();
    }
    @Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.setField(field, value);
    }
    @Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.addRepeatedField(field, value);
    }
    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam) {
        return mergeFrom((com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam other) {
      if (other == com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.getDefaultInstance()) return this;
      if (!other.getImgId().isEmpty()) {
        imgId_ = other.imgId_;
        onChanged();
      }
      if (other.getImage() != com.google.protobuf.ByteString.EMPTY) {
        setImage(other.getImage());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private Object imgId_ = "";
    /**
     * <code>string imgId = 1;</code>
     * @return The imgId.
     */
    public String getImgId() {
      Object ref = imgId_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        imgId_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string imgId = 1;</code>
     * @return The bytes for imgId.
     */
    public com.google.protobuf.ByteString
        getImgIdBytes() {
      Object ref = imgId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        imgId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string imgId = 1;</code>
     * @param value The imgId to set.
     * @return This builder for chaining.
     */
    public Builder setImgId(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }

      imgId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string imgId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearImgId() {

      imgId_ = getDefaultInstance().getImgId();
      onChanged();
      return this;
    }
    /**
     * <code>string imgId = 1;</code>
     * @param value The bytes for imgId to set.
     * @return This builder for chaining.
     */
    public Builder setImgIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

      imgId_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.ByteString image_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>bytes image = 2;</code>
     * @return The image.
     */
    @Override
    public com.google.protobuf.ByteString getImage() {
      return image_;
    }
    /**
     * <code>bytes image = 2;</code>
     * @param value The image to set.
     * @return This builder for chaining.
     */
    public Builder setImage(com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }

      image_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bytes image = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearImage() {

      image_ = getDefaultInstance().getImage();
      onChanged();
      return this;
    }
    @Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:XDService.RequestParam)
  }

  // @@protoc_insertion_point(class_scope:XDService.RequestParam)
  private static final com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam();
  }

  public static com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RequestParam>
      PARSER = new com.google.protobuf.AbstractParser<RequestParam>() {
    @Override
    public RequestParam parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RequestParam(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RequestParam> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<RequestParam> getParserForType() {
    return PARSER;
  }

  @Override
  public com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

