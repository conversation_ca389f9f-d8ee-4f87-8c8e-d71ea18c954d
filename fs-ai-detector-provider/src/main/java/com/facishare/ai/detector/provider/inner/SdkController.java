package com.facishare.ai.detector.provider.inner;

import com.facishare.ai.detector.api.dto.api.GetModel;
import com.facishare.ai.detector.api.dto.api.SaveDetectRecord;
import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordCountArg;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordCountResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.DetectRecordService;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.ai.detector.api.service.SdkService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/API/inner/v1/sdk")
public class SdkController {

    @Resource
    private SdkService sdkService;

    @Resource
    private DetectorService detectorService;

    @Resource
    private DetectRecordService detectRecordService;

    @PostMapping(value = "/get_model")
    public GetModel.Result getModel(
            @RequestHeader("x-fs-ei") Integer tenantId,
            @RequestHeader("x-fs-userInfo") Integer currentEmployeeId,
            @RequestBody GetModel.Arg arg) {
        return sdkService.getModel(tenantId, currentEmployeeId, arg);
    }

    @PostMapping(value = "/save_detect_record")
    public SaveDetectRecord.Result saveDetectRecord(
            @RequestHeader("x-fs-ei") Integer tenantId,
            @RequestHeader("x-fs-userInfo") Integer currentEmployeeId,
            @RequestBody SaveDetectRecord.Arg arg) {
        return sdkService.saveDetectRecord(tenantId, currentEmployeeId, arg);
    }

    @PostMapping(value = "/batchDetect")
    public BatchDetectResult batchDetect(@RequestBody BatchDetectArg arg) throws AiProviderException {
        return detectorService.batchDetect(arg);
    }

    @PostMapping(value = "/detect")
    public DetectResult detect(@RequestBody DetectArg arg) throws AiProviderException {
        return detectorService.detect(arg);
    }

    @PostMapping(value = "/queryDetectRecord")
    public QueryDetectRecordResult queryDetectRecord(@RequestBody QueryDetectRecordArg arg) throws AiProviderException {
        return detectRecordService.queryDetectRecord(arg);
    }

    @PostMapping(value = "/queryDetectRecordCount")
    public QueryDetectRecordCountResult queryDetectRecordCount(@RequestBody QueryDetectRecordCountArg arg) throws AiProviderException {
        return detectRecordService.queryDetectRecordCount(arg);
    }
}
