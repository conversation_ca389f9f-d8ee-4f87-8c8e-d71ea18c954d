package com.facishare.ai.detector.provider.inner;

import com.facishare.ai.detector.api.dto.arg.AddApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetApplicationArg;
import com.facishare.ai.detector.api.dto.arg.GetModelByIdArg;
import com.facishare.ai.detector.api.dto.arg.GetModelListArg;
import com.facishare.ai.detector.api.dto.result.AddApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetApplicationResult;
import com.facishare.ai.detector.api.dto.result.GetModelByIdResult;
import com.facishare.ai.detector.api.dto.result.GetModelListResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.ApplicationService;
import com.facishare.ai.detector.api.service.ModelService;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/11/15 上午11:46
 */
@RestController
@RequestMapping(value = "/API/inner/v1/ai/model/")
public class ModelInnerController {

    @Resource
    private ModelService modelService;

    @Resource
    private ApplicationService applicationService;

    @PostMapping(value = "/getModelList")
    public GetModelListResult getModelList(@RequestBody GetModelListArg arg) throws AiProviderException {
        return modelService.getModelList(arg);
    }

    @PostMapping(value = "/getModelById")
    public GetModelByIdResult getModelById(@RequestBody GetModelByIdArg arg) throws AiProviderException {
        return modelService.getModelById(arg);
    }

    @PostMapping(value = "/addApplication")
    public AddApplicationResult addApplication(@RequestBody AddApplicationArg arg) throws AiProviderException {
        return applicationService.addApplication(arg);
    }

    @PostMapping(value = "/getApplication")
    public GetApplicationResult getApplication(@RequestBody GetApplicationArg arg) throws AiProviderException {
        return applicationService.getApplication(arg);
    }

}
