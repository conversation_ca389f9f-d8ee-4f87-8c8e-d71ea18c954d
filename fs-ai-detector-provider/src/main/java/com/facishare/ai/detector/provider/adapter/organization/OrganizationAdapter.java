package com.facishare.ai.detector.provider.adapter.organization;

import com.facishare.ai.detector.api.dto.share.EmployeeInfoVo;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.result.GetAllDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.department.result.GetChildrenDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.department.result.GetLowDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.FindEmployeeByNames;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetAllEmployeesResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.BatchGetEmployeesDtoByDepartmentIdResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrganizationAdapter {

    List<EmployeeDto> batchGetEmployees(int ei, List<Integer> employeeIds);

    List<EmployeeInfoVo> queryEmployees(int ei, List<Integer> employeeIds);

    EmployeeInfoVo getEmployee(int ei, int employeeId);

    List<Integer> getEmployeeIds(int ei, List<Integer> departmentIds);

    List<Integer> getAllEmployeeIds(int ei, List<Integer> departmentIds);

    List<Department> batchGetDepartments(int ei, List<Integer> departmentIds);

    Department getDepartment(int ei, Integer departmentId);

    List<Integer> getDepartmentByPrincipalIds(Integer ei, Integer employeeId, List<Integer> principalIds);

    List<Integer> getSubordinateEmployeeIds(Integer ei, Integer employeeId);

    GetAllEmployeesResult getAllEmployees(Integer ei);

    FindEmployeeByNames.Result getEmployeesByNames(Integer ei, List<String> names);

    GetAllDepartmentResult getAllDepartments(int ei, int currentEmployeeId);

    GetLowDepartmentResult getLowDepartment(int ei, int departmentId);

    BatchGetEmployeesDtoByDepartmentIdResult batchGetEmployeesDtoByDepartmentIds(Integer ei, List<Integer> departmentIds);

    BatchGetEmployeesDtoByDepartmentIdResult batchGetAllEmployeesDtoByDepartmentIds(Integer ei, List<Integer> departmentIds);

    GetChildrenDepartmentResult getChildrenDepartment(Integer ei, Integer departmentId);

    Department getParentDepartment(Integer ei, Integer employeeId, Integer departmentId);

    List<Integer> getAllDepartmentIdsOfEmployee(Integer ei, Integer employeeId);
}