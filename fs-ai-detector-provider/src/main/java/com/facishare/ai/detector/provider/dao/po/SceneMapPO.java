package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

@Entity(value = "ai_scene_map", noClassnameStored = true)
@Data
@ToString
public class SceneMapPO extends MongoPOBase {


    public static final String F_MODEL_ID = "MI";
    public static final String F_KEY = "K";
    public static final String F_VALUE = "V";


    @Property(F_MODEL_ID)
    private String modelId;

    @Property(F_KEY)
    private String key;

    @Property(F_VALUE)
    private String value;

}
