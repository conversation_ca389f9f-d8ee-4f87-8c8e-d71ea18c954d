package com.facishare.ai.detector.provider.adapter.grpc.yq;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.41.1)",
    comments = "Source: XDService.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class XDRecogizeServiceGrpc {

  private XDRecogizeServiceGrpc() {}

  public static final String SERVICE_NAME = "XDService.XDRecogizeService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<RequestParam,
      ResultTrack> getXDRecogizeMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "XDRecogize",
      requestType = com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.class,
      responseType = com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<RequestParam,
      ResultTrack> getXDRecogizeMethod() {
    io.grpc.MethodDescriptor<RequestParam, ResultTrack> getXDRecogizeMethod;
    if ((getXDRecogizeMethod = XDRecogizeServiceGrpc.getXDRecogizeMethod) == null) {
      synchronized (XDRecogizeServiceGrpc.class) {
        if ((getXDRecogizeMethod = XDRecogizeServiceGrpc.getXDRecogizeMethod) == null) {
          XDRecogizeServiceGrpc.getXDRecogizeMethod = getXDRecogizeMethod =
              io.grpc.MethodDescriptor.<com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam, com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "XDRecogize"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.getDefaultInstance()))
              .setSchemaDescriptor(new XDRecogizeServiceMethodDescriptorSupplier("XDRecogize"))
              .build();
        }
      }
    }
    return getXDRecogizeMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static XDRecogizeServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceStub>() {
        @Override
        public XDRecogizeServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new XDRecogizeServiceStub(channel, callOptions);
        }
      };
    return XDRecogizeServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static XDRecogizeServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceBlockingStub>() {
        @Override
        public XDRecogizeServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new XDRecogizeServiceBlockingStub(channel, callOptions);
        }
      };
    return XDRecogizeServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static XDRecogizeServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<XDRecogizeServiceFutureStub>() {
        @Override
        public XDRecogizeServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new XDRecogizeServiceFutureStub(channel, callOptions);
        }
      };
    return XDRecogizeServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class XDRecogizeServiceImplBase implements io.grpc.BindableService {

    /**
     */
    public void xDRecogize(com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam request,
        io.grpc.stub.StreamObserver<ResultTrack> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getXDRecogizeMethod(), responseObserver);
    }

    @Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getXDRecogizeMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam,
                com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack>(
                  this, METHODID_XDRECOGIZE)))
          .build();
    }
  }

  /**
   */
  public static final class XDRecogizeServiceStub extends io.grpc.stub.AbstractAsyncStub<XDRecogizeServiceStub> {
    private XDRecogizeServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected XDRecogizeServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new XDRecogizeServiceStub(channel, callOptions);
    }

    /**
     */
    public void xDRecogize(com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam request,
        io.grpc.stub.StreamObserver<ResultTrack> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getXDRecogizeMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class XDRecogizeServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<XDRecogizeServiceBlockingStub> {
    private XDRecogizeServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected XDRecogizeServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new XDRecogizeServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack xDRecogize(com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getXDRecogizeMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class XDRecogizeServiceFutureStub extends io.grpc.stub.AbstractFutureStub<XDRecogizeServiceFutureStub> {
    private XDRecogizeServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected XDRecogizeServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new XDRecogizeServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<ResultTrack> xDRecogize(
        com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getXDRecogizeMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_XDRECOGIZE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final XDRecogizeServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(XDRecogizeServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_XDRECOGIZE:
          serviceImpl.xDRecogize((com.facishare.ai.detector.provider.adapter.grpc.yq.RequestParam) request,
              (io.grpc.stub.StreamObserver<ResultTrack>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @Override
    @SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class XDRecogizeServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    XDRecogizeServiceBaseDescriptorSupplier() {}

    @Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.getDescriptor();
    }

    @Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("XDRecogizeService");
    }
  }

  private static final class XDRecogizeServiceFileDescriptorSupplier
      extends XDRecogizeServiceBaseDescriptorSupplier {
    XDRecogizeServiceFileDescriptorSupplier() {}
  }

  private static final class XDRecogizeServiceMethodDescriptorSupplier
      extends XDRecogizeServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    XDRecogizeServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (XDRecogizeServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new XDRecogizeServiceFileDescriptorSupplier())
              .addMethod(getXDRecogizeMethod())
              .build();
        }
      }
    }
    return result;
  }
}
