// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: XDService.proto

package com.facishare.ai.detector.provider.adapter.grpc.yq;

public interface RequestParamOrBuilder extends
    // @@protoc_insertion_point(interface_extends:XDService.RequestParam)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string imgId = 1;</code>
   * @return The imgId.
   */
  String getImgId();
  /**
   * <code>string imgId = 1;</code>
   * @return The bytes for imgId.
   */
  com.google.protobuf.ByteString
      getImgIdBytes();

  /**
   * <code>bytes image = 2;</code>
   * @return The image.
   */
  com.google.protobuf.ByteString getImage();
}
