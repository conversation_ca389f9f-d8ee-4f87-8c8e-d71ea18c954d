package com.facishare.ai.detector.provider.resolver;

import com.facishare.ai.detector.provider.resolver.annotation.FMCGOuterUserInfo;
import com.facishare.ai.detector.provider.resolver.annotation.FMCGUserInfo;
import com.facishare.ai.detector.provider.resolver.model.OuterUserData;
import com.facishare.ai.detector.provider.resolver.model.UserData;
import com.facishare.converter.EIEAConverter;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.util.List;
import java.util.Random;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/1/6 下午5:40
 */
public class FMCGRequestResolver implements HandlerMethodArgumentResolver {

    private final static List<Class> SUPPORT_ANN = Lists.newArrayList(FMCGUserInfo.class, FMCGOuterUserInfo.class);
    private final static Random RANDOM = new Random();
    @Resource
    private EIEAConverter eieaConverterImpl;

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        Annotation[] annotations = methodParameter.getParameterAnnotations();
        return Stream.of(annotations).anyMatch(ann -> SUPPORT_ANN.stream().anyMatch(support -> support.isInstance(ann)));
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {

        Annotation[] annotations = methodParameter.getParameterAnnotations();
        HttpServletRequest request = (HttpServletRequest) nativeWebRequest.getNativeRequest();
        TraceContext traceContext = TraceContext.get();
        for (Annotation ann : annotations) {
            if (ann instanceof FMCGOuterUserInfo) {
                OuterUserData outerUserData = new OuterUserData();
                if (request.getHeader("X-app-id") != null)
                    outerUserData.setAppId(request.getHeader("X-app-id"));
                if (request.getHeader("X-out-user-id") != null)
                    outerUserData.setOuterUserId(Long.valueOf(request.getHeader("X-out-user-id")));
                if (request.getHeader("X-out-tenant-id") != null)
                    outerUserData.setOuterTenantId(Long.valueOf(request.getHeader("X-out-tenant-id")));
                if (request.getHeader("X-upstream-tenant-id") != null)
                    outerUserData.setUpstreamTenantId(Integer.valueOf(request.getHeader("X-upstream-tenant-id")));
                traceContext.setEi(String.valueOf(outerUserData.getOuterTenantId()));
                traceContext.setEmployeeId(String.valueOf(outerUserData.getOuterUserId()));
                traceContext.setTraceId(String.format("E.%s.%s.%s-%s", outerUserData.getOuterTenantId(), outerUserData.getOuterUserId(), System.currentTimeMillis(), RANDOM.nextInt(100)));
                return outerUserData;
            } else if (ann instanceof FMCGUserInfo) {
                UserData userData = new UserData();
                if (request.getHeader("X-fs-Enterprise-Id") != null)
                    userData.setTenantId(request.getIntHeader("X-fs-Enterprise-Id"));
                if (request.getHeader("x-fs-ei") != null)
                    userData.setTenantId(request.getIntHeader("x-fs-ei"));
                if (request.getHeader("x-fs-userInfo") != null)
                    userData.setUserId(request.getIntHeader("x-fs-userInfo"));
                if (request.getHeader("X-fs-Employee-Id") != null)
                    userData.setUserId(request.getIntHeader("X-fs-Employee-Id"));
                if (userData.getTenantId() != null) {
                    userData.setTenantAccount(eieaConverterImpl.enterpriseIdToAccount(userData.getTenantId()));
                }
                traceContext.setEi(String.valueOf(userData.getTenantId()));
                traceContext.setEmployeeId(String.valueOf(userData.getUserId()));
                traceContext.setTraceId(String.format("E.%s.%s.%s-%s", userData.getTenantId(), userData.getUserId(), System.currentTimeMillis(), RANDOM.nextInt(100)));
                return userData;
            }
        }
        return null;
    }

}
