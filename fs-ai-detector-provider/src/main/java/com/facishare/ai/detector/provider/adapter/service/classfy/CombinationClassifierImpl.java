package com.facishare.ai.detector.provider.adapter.service.classfy;

import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-13  上午11:57
 */
@Component
public class CombinationClassifierImpl implements Classifier {

    private static final Logger log = LoggerFactory.getLogger(CombinationClassifierImpl.class);

    @Resource
    private LangJingClassifierImpl langJingClassifier;

    @Resource
    private BaiduClassifierImpl baiduClassifier;

    @Resource
    private HuaWeiClassifierImpl huaWeiClassifier;

    @Override
    public List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {

        ModelPo langJingModel = new ModelPo();
        langJingModel.setKey("recapture");
        langJingModel.setConfidence(0.8);

        ModelPo baiduModel1 = new ModelPo();
        baiduModel1.setKey("image_recapture_detection_v2");
        baiduModel1.setConfidence(0.8);

        List<ClassDto> d1 =  new ArrayList<>();
        List<ClassDto> d2 = null;

        //List<ClassDto> d2 = baiduCustomizedClassifier.classify(arg,baiduModel1,imageCache);
       /* try {
            d2 = huaWeiClassifier.classify(arg,langJingModel,imageCache);
            d1.addAll(d2);
        }catch (Exception e){
            log.info("huawei fail.may be  no monney");
        }*/
        try {
            d2 = baiduClassifier.classify(arg,langJingModel,imageCache);
            d1.addAll(d2);
        }catch (Exception e){
            log.info("baidu fail.");
        }
        try {
            d2 =langJingClassifier.classify(arg,langJingModel,imageCache);
            d1.addAll(d2);
        }catch (Exception e){
            log.info("langjing fail.{}",e);
        }
        if(d1.isEmpty()) return  d1;
        ClassDto max = d1.get(0);
        for(ClassDto dto:d1){
            if(dto.getClassName().equals("recapture")&&dto.getConfidence()>max.getConfidence())
                max = dto;
        }
        d2 = Lists.newArrayList(max);
        return  d2;
    }
}
