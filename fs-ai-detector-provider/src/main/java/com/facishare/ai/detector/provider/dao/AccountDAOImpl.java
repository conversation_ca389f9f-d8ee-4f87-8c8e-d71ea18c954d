package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.AccountDAO;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.po.AccountPO;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午3:12
 */
@Repository
public class AccountDAOImpl extends DaoBase<AccountPO> implements AccountDAO {

    @Override
    public String insert(AccountPO po) {
        po.setCreateTime(System.currentTimeMillis());
        po.setCreator(po.getCreator() == null ? -10000 : po.getCreator());
        po.setIsDeleted(false);
        po.setBalance(po.getBalance() == null ? 0D : po.getBalance());
        AccountPO oldPo = query(po.getTenantId(), po.getType());
        if (oldPo == null)
            return dbContext.save(po).getId().toString();
        else
            return oldPo.getId().toString();
    }

    @Override
    public AccountPO get(String id) {
        return buildUniqueIdQuery(id, AccountPO.class).get();
    }

    @Override
    public List<AccountPO> query(int tenantId) {
        Query<AccountPO> query = dbContext.createQuery(AccountPO.class);
        query.field(AccountPO.F_TENANT_ID).equal(tenantId);
        query.field(AccountPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    @Override
    public AccountPO query(int tenantId, String type) {
        Query<AccountPO> query = dbContext.createQuery(AccountPO.class);
        query.field(AccountPO.F_TENANT_ID).equal(tenantId);
        query.field(AccountPO.F_IS_DELETED).equal(false);
        if (type != null)
            query.field(AccountPO.F_TYPE).equal(type);
        return query.get();
    }

    @Override
    public void riseBalance(String id, Double amount) {
        Query<AccountPO> query = buildUniqueIdQuery(id, AccountPO.class);
        UpdateOperations<AccountPO> updateOperations = dbContext.createUpdateOperations(AccountPO.class);
        updateOperations.inc(AccountPO.F_BALANCE, amount);
        updateOperations.set(AccountPO.F_UPDATE_TIME, System.currentTimeMillis());
        updateOperations.set(AccountPO.F_UPDATER, -10000);
        dbContext.update(query, updateOperations);
    }
}
