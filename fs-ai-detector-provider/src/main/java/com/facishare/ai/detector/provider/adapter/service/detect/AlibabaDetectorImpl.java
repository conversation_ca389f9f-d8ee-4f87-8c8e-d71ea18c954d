package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.model.ImageDto;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-16  下午7:37
 */
@SuppressWarnings("Duplicates")
public class AlibabaDetectorImpl implements Detector {

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private TokenFactory tokenFactory;

    private static final Logger log = LoggerFactory.getLogger(AlibabaDetectorImpl.class);

    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        List<BoxDto> boxes = new ArrayList<>();
        List<ImageDto> images = new ArrayList<>();
        String path = fileAdapter.createShareFile(arg.getTenantAccount(), Integer.valueOf(arg.getUserId()), arg.getPath());
        images.add(new ImageDto(path, 0, 0));
        long interval = 500;
        String tokenKey = Strings.isNullOrEmpty(model.getTokenKey())? AppEnum.ALIBABA_DEFAULT_GUANFANG.value():model.getTokenKey();
        String token = tokenFactory.getToken(tokenKey);
        JSONObject data = new JSONObject();
        data.put("model_id", model.getModelCode());
        data.put("images", images);
        IConfig docConfig = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG);
        String url = docConfig.get(ConstantUtil.ALIBABA_OBJECT_RECOGNITION_SUBMIT);
        String appKey = "default";
        String userId = "default";
        String namespace = "ShelfInsight";

        try {
            url = String.format(url, appKey, userId, namespace, URLEncoder.encode(JSON.toJSONString(data), "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            log.error("url format err! url:{}", url);
        }

        JSONObject submitRst = submit(url, token);
        if (isRequestSuccess(submitRst)) {
            JSONObject response = submitRst.getJSONObject("data");
            String job;
            if (response != null) {
                job = response.getString("job_id");
                String queryUrl = String.format(docConfig.get(ConstantUtil.ALIBABA_OBJECT_RECOGNITION_QUERY), job, userId);
                int seed = 20;
                while (seed > 0) {
                    JSONObject rst = query(queryUrl, token);
                    if (isRequestSuccess(rst)) {
                        JSONObject obj = rst.getJSONObject("data");

                        log.info("query finished : {} ", obj);

                        if ("COMPLETED".equals(obj.getString("status")) && "SUCCESS".equals(obj.getString("status_message"))) {
                            obj.getJSONObject("response").getJSONArray("data").forEach(v -> {
                                JSONObject imageInfo = (JSONObject) v;
                                BoxDto boxDto = new BoxDto();
                                boxDto.setScore(imageInfo.getDouble("conf").toString());
                                String skuId = imageInfo.getInteger("skuid").toString();
                                if (!"0".equals(skuId)) {
                                    boxDto.setName(skuId);
                                    Double[] bound = new Double[4];
                                    bound[0] = imageInfo.getDouble("y1");
                                    bound[1] = imageInfo.getDouble("x1");
                                    bound[2] = imageInfo.getDouble("y2");
                                    bound[3] = imageInfo.getDouble("x2");
                                    boxDto.setBox(bound);
                                    if (Double.parseDouble(boxDto.getScore()) > model.getConfidence()) {
                                        boxes.add(boxDto);
                                    }
                                }
                            });
                            return boxes;
                        } else {

                            log.info("job is executing or error; rst : {}", obj);
                        }
                    } else {

                        log.info("query fail. rst : {}", rst);
                    }

                    seed = seed - 1;

                    try {
                        Thread.sleep(interval);
                    } catch (InterruptedException e) {
                        log.error("time sleep err in query alibaba interface.");
                        Thread.currentThread().interrupt();
                        throw new AiProviderException("AI", "time sleep err in query alibaba interface.");
                    }
                    if (seed == 0) {
                        throw new AiProviderException("AI", "query fail!");
                    }
                }
            }
        } else {
            log.error("summit task fail. rst : {}", submitRst);
            throw new AiProviderException("AI", "summit task fail.");
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }

    private JSONObject submit(String url, String token) throws AiProviderException {
        Map<String, String> header = new HashMap<>(1);
        header.put("X-NLS-Token", token);

        return (JSONObject) HttpUtil.post(url, header, new Object());
    }

    public JSONObject query(String url, String token) throws AiProviderException {
        Map<String, String> header = new HashMap<>();
        header.put("X-NLS-Token", token);
        return (JSONObject) HttpUtil.get(url, header);
    }

    private boolean isRequestSuccess(JSONObject object) {
        return object.getInteger("status") == 200 && object.getString("error_message").equals("SUCCESS");
    }

}
