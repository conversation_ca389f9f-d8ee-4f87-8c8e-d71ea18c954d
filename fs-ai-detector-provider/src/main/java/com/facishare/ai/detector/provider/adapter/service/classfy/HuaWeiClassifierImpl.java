package com.facishare.ai.detector.provider.adapter.service.classfy;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-6  下午3:07
 */
public class HuaWeiClassifierImpl  implements Classifier {

    private Logger log = LoggerFactory.getLogger(HuaWeiClassifierImpl.class);

    @Autowired
    private TokenFactory tokenFactory;

    @Override
    public List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {

        JSONObject data = new JSONObject();
        data.put("threshold",model.getConfidence());
        data.put("image", Base64.encodeBase64String(imageCache));
        data.put("scene", Lists.newArrayList("recapture"));
        String url = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.HUAWEI_CLASSIFICATION);
        Map<String,String> header = new HashMap<>();
        String tokenKey = Strings.isNullOrEmpty(model.getTokenKey())? AppEnum.HUAWEI_FSHUAWEICLOUD.value():model.getTokenKey();
        header.put("X-Auth-Token",tokenFactory.getToken(tokenKey));
        JSONObject result = (JSONObject) HttpUtil.post(url, header, data);

        log.info("classification finished. {}", result);

        List<ClassDto> classes = new ArrayList<>();

        if (result != null && result.containsKey("error_code")) {
            data.put("image","");
            log.error("call huawei api err .arg:{}", data);
            throw new AiProviderException("AI", "call huawei api err ."+result.getString("error_msg"));
        }
        if (result != null && result.getJSONObject("result") != null) {
            result.getJSONObject("result").getJSONArray("detail").forEach(v -> {
                ClassDto dto = new ClassDto();
                JSONObject o = (JSONObject) v;
                dto.setClassName(o.getString("label"));
                dto.setConfidence(o.getDouble("confidence"));
                classes.add(dto);
            });

        } else {
            log.warn("call huawei api but return null.arg:{}", data);
            throw new AiProviderException("AI", "call huawei api but return null.");
        }
        return classes;
    }
}
