package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.DetectRecordDao;
import com.facishare.ai.detector.provider.dao.po.DetectRecordPo;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 19-9-24  下午6:56
 */
public class DetectRecordDaoImpl extends DaoBase<DetectRecordPo> implements DetectRecordDao {

    public static final Long MONTH = 3600 * 24 * 30L;

    @Override
    public DetectRecordPo get(String id) {
        return buildIdQuery(id, DetectRecordPo.class).get();
    }

    @Override
    public String save(DetectRecordPo po) {
        if (po.getTenantId() != null && GrayRelease.isAllow("fmcg", "AI_DETECT_RECORD_EXPIRED", po.getTenantId().toString())) {
            po.setExpiredDate(new Date(System.currentTimeMillis() + MONTH));
        }
        return dbContext.save(po).getId().toString();
    }

    @Override
    public DetectRecordPo get(Integer tenantId, String modelId, String path) {
        Query<DetectRecordPo> pathQuery = dbContext.createQuery(DetectRecordPo.class);
        pathQuery.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        pathQuery.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        pathQuery.field(DetectRecordPo.F_PATH).startsWith(path);
        DetectRecordPo result = pathQuery.get();
        if (result == null) {
            Query<DetectRecordPo> originalPathQuery = dbContext.createQuery(DetectRecordPo.class);
            originalPathQuery.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
            originalPathQuery.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
            originalPathQuery.field(DetectRecordPo.F_ORIGINAL_PATH).startsWith(path);
            result = originalPathQuery.get();
        }
        return result;
    }

    @Override
    public DetectRecordPo get(Integer tenantId, String modelId, Set<String> srcImg) {
        Query<DetectRecordPo> pathQuery = dbContext.createQuery(DetectRecordPo.class);
        pathQuery.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        pathQuery.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        pathQuery.field(DetectRecordPo.F_PATH).in(srcImg);
        DetectRecordPo result = pathQuery.get();
        if (result == null) {
            Query<DetectRecordPo> originalPathQuery = dbContext.createQuery(DetectRecordPo.class);
            originalPathQuery.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
            originalPathQuery.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
            originalPathQuery.field(DetectRecordPo.F_ORIGINAL_PATH).in(srcImg);
            result = originalPathQuery.get();
        }
        return result;
    }

    @Override
    public DetectRecordPo queryByProcessPath(Integer tenantId, String modelId, Set<String> srcImg) {
        Query<DetectRecordPo> processPathQuery = dbContext.createQuery(DetectRecordPo.class);
        processPathQuery.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        processPathQuery.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        processPathQuery.field(DetectRecordPo.F_PROCESSED_PATH).in(srcImg);
        return processPathQuery.get();
    }

    @Override
    public List<DetectRecordPo> query(Integer tenantId, String modelId, List<String> originalPathList) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        query.field(DetectRecordPo.F_ORIGINAL_PATH).in(originalPathList);
        return query.asList();
    }

    @Override
    public long count(Integer tenantId, String modelId, long start, long end) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        if (!Strings.isNullOrEmpty(modelId))
            query.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        query.field(DetectRecordPo.F_CREATE_TIME).greaterThanOrEq(start);
        query.field(DetectRecordPo.F_CREATE_TIME).lessThanOrEq(end);
        return query.countAll();
    }

    @Override
    public void deleteAll(Integer tenantId, String modelId, List<String> npathList) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectRecordPo.F_MODEL_ID).equal(modelId);
        query.field(DetectRecordPo.F_PATH).in(npathList);
        dbContext.delete(query);
    }

    @Override
    public void deleteByIds(Integer tenantId, List<String> ids) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field("_id").in(ids.stream().map(ObjectId::new).collect(Collectors.toSet()));
        dbContext.delete(query);
    }

    @Override
    public List<DetectRecordPo> queryByTenantId(Integer tenantId, Long maxCreateTime) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectRecordPo.F_CREATE_TIME).lessThanOrEq(maxCreateTime);
        query.limit(1000);
        query.retrievedFields(true,"_id");
        return query.asList();
    }

    @Override
    public List<DetectRecordPo> queryByPath(Integer tenantId, Set<String> path) {
        Query<DetectRecordPo> query = dbContext.createQuery(DetectRecordPo.class);
        query.field(DetectRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(DetectRecordPo.F_PATH).in(path);
        return query.asList();
    }
}
