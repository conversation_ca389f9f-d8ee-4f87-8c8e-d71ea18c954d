package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-9-19  下午5:59
 */
@Data
@ToString
@Entity(value = "ai_classify_record",noClassnameStored = true)
public class ClassifyRecordPo extends MongoPOBase {

    public static final String F_MODEL_ID = "MI";
    public static final String F_SRC_IMG_PATH = "SIP";
    public static final String F_RESULT = "R";
    public static final String F_STATUS = "S";
    public static final String F_MESSAGE = "M";


    @Property(F_MODEL_ID)
    private String modelPoId;

    @Property(F_SRC_IMG_PATH)
    private String srcImgPath;

    //todo:不要再PO中使用DTO的ENTITY
    @Embedded(F_RESULT)
    private List<ClassEntity> result;

    @Property(F_STATUS)
    private String status;

    @Property(F_MESSAGE)
    private String message;

}
