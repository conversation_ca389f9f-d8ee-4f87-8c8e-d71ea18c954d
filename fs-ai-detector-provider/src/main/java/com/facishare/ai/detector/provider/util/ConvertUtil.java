package com.facishare.ai.detector.provider.util;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.ApplicationDto;
import com.facishare.ai.detector.api.dto.ModelDto;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;
import com.facishare.ai.detector.provider.dao.po.ApplicationPO;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;

public class ConvertUtil {

    public static <D, P> P convertToPo(D dto, P po) {
        FmcgBeanUtil.copyProperties(dto, po);
        return po;
    }

    public static <P, D> D convertToDto(P po, D dto) {
        FmcgBeanUtil.copyProperties(po, dto);
        return dto;
    }

    // 示例用法
    public static ApplicationPO convertToPo(ApplicationDto dto) {
        return convertToPo(dto, new ApplicationPO());
    }

    public static ApplicationDto convertToDto(ApplicationPO po) {
        return convertToDto(po, new ApplicationDto());
    }

    public static AIDetectRulePO convertToPo(AIDetectRuleDto dto) {
        return convertToPo(dto, new AIDetectRulePO());
    }

    public static AIDetectRuleDto convertToDto(AIDetectRulePO po) {
        return convertToDto(po, new AIDetectRuleDto());
    }

    public static ModelPo convertToPo(ModelDto dto) {
        return convertToPo(dto, new ModelPo());
    }

    public static ModelDto convertToDto(ModelPo po) {
        return convertToDto(po, new ModelDto());
    }

    public static ObjectMapPo convertToPo(ObjectMapDto dto) {
        return convertToPo(dto, new ObjectMapPo());
    }

    public static ObjectMapDto convertToDto(ObjectMapPo po) {
        return convertToDto(po, new ObjectMapDto());
    }

    // 可以根据需要继续添加其他Dto和Po之间的转换方法。
}
