package com.facishare.ai.detector.provider.adapter.service;

import com.facishare.ai.detector.provider.adapter.SpringContextHolder;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-1  下午4:09
 */
public class ClassifierFactory {

    private static final Map<String, String> BEAN_NAME_MAP = new HashMap<>();

    static {
        // 百度场景实备云服务版
        BEAN_NAME_MAP.put("baidu", "baiduClassifier");

        // 百度场景识别私有部署版本
        BEAN_NAME_MAP.put("baidu_local", "baiduLocalClassifier");

        // 华为场景实备云服务版
        BEAN_NAME_MAP.put("huawei", "huaweiClassifier");

        BEAN_NAME_MAP.put("baidu_customized", "baiduCustomizedClassifier");

        BEAN_NAME_MAP.put("langjing", "langJingClassifier");

        BEAN_NAME_MAP.put("combination","combinationClassifierImpl");
    }

    public static Classifier getClassifier(String platform) {
        return SpringContextHolder.getBean(BEAN_NAME_MAP.get(platform));
    }
}
