package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.PriceDAO;
import com.facishare.ai.detector.provider.dao.po.PricePO;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午3:13
 */
@Repository
public class PriceDAOImpl extends DaoBase<PricePO> implements PriceDAO {
    @Override
    public String insert(PricePO po) {
        po.setCreateTime(System.currentTimeMillis());
        po.setCreator(po.getCreator() == null ? -10000 : po.getCreator());
        po.setIsDeleted(false);
        po.setUnitPrice(po.getUnitPrice() == null ? 0D : po.getUnitPrice());
        PricePO old = query(po.getTenantId(), po.getModelId());
        if (old == null)
            return dbContext.save(po).getId().toString();
        else
            return old.getId().toString();
    }

    @Override
    public PricePO get(String id) {
        return buildIdQuery(id, PricePO.class).get();
    }

    @Override
    public PricePO query(int tenantId, String modelId) {
        Query<PricePO> query = dbContext.createQuery(PricePO.class);
        query.field(PricePO.F_TENANT_ID).equal(tenantId);
        query.field(PricePO.F_IS_DELETED).equal(false);
        query.field(PricePO.F_MODEL_ID).equal(modelId);
        return query.get();
    }

    @Override
    public void changePrice(String id, double unitPrice) {
        Query<PricePO> query = buildIdQuery(id, PricePO.class);
        UpdateOperations<PricePO> updateOperations = dbContext.createUpdateOperations(PricePO.class);
        updateOperations.inc(PricePO.F_UNIT_PRICE, unitPrice);
        updateOperations.set(PricePO.F_UPDATE_TIME, System.currentTimeMillis());
        updateOperations.set(PricePO.F_UPDATER, -10000);
        dbContext.update(query, updateOperations);
    }
}
