package com.facishare.ai.detector.provider.facade;

import com.facishare.ai.detector.api.dto.arg.BatchClassifyArg;
import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.result.BatchClassifyResult;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.dto.result.ClassifyResult;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.ClassifierService;
import com.facishare.ai.detector.api.service.DetectorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/detector")
public class DetectorController {

    private static final Logger logger = LoggerFactory.getLogger(DetectorController.class);

    @Resource
    private DetectorService detectorService;

    @Resource
    private ClassifierService classifierService;

    @RequestMapping(value = "/detect", method = RequestMethod.POST)
    public DetectResult detect(@RequestBody DetectArg arg) throws AiProviderException {
        try {
            return detectorService.detect(arg);
        } catch (Exception ex) {
            logger.error("detect error.", ex);
            throw ex;
        }
    }

    @RequestMapping(value = "/classify", method = RequestMethod.POST)
    public ClassifyResult classify(@RequestBody ClassifyArg arg) throws AiProviderException {
        try {
            return classifierService.classify(arg);
        } catch (Exception ex) {
            logger.error("classify error.", ex);
            throw ex;
        }
    }

    @RequestMapping(value = "/batchDetect",method = RequestMethod.POST)
    public BatchDetectResult batchDetect(@RequestBody BatchDetectArg arg)throws AiProviderException{
        return detectorService.batchDetect(arg);
    }

    @RequestMapping(value = "/batchClassify",method = RequestMethod.POST)
    public BatchClassifyResult batchClassify(@RequestBody BatchClassifyArg arg)throws AiProviderException{
        return classifierService.batchClassify(arg);
    }
}