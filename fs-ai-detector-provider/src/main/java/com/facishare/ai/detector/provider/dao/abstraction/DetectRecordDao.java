package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.DetectRecordPo;

import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 19-9-23  下午3:45
 */
public interface DetectRecordDao {

    DetectRecordPo get(String id);

    String save(DetectRecordPo po);

    DetectRecordPo get(Integer tenantId, String modelId, String srcImg);

    DetectRecordPo get(Integer tenantId, String modelId, Set<String> srcImg);

    DetectRecordPo queryByProcessPath(Integer tenantId, String modelId, Set<String> srcImg);

    List<DetectRecordPo> query(Integer tenantId, String modelId, List<String> srcImg);

    long count(Integer tenantId,String modelId,long start,long end);

    void deleteAll(Integer tenantId,String modelId,List<String> npathList);

    void deleteByIds(Integer tenantId,List<String> ids);

    List<DetectRecordPo> queryByTenantId(Integer tenantId,Long maxCreateTime);

    List<DetectRecordPo> queryByPath(Integer tenantId, Set<String> path);

}
