package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Repository(value = "oldObjectMapDao")
public class OldObjectMapDaoImpl {

    private static List<ObjectMapPo> mockTable = new ArrayList<>();

    static {

        ObjectMapPo o1 = new ObjectMapPo();
        o1.setTenantId(608158);
        o1.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o1.setKey("JML_PM_YDB_HSNR");
        o1.setAppId("CRM");
        o1.setApiName("ProductObj");
        o1.setObjectId("5c74ddf1494f6f95b07e0fea");
        o1.setName("一袋半红烧牛肉面");
        o1.setColor("3dd4c4");
        mockTable.add(o1);

        ObjectMapPo o2 = new ObjectMapPo();
        o2.setTenantId(608158);
        o2.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o2.setKey("JML_PM_YDB_XLNR");
        o2.setAppId("CRM");
        o2.setApiName("ProductObj");
        o2.setObjectId("5c74de23f125ae3f1e12f51d");
        o2.setName("一袋半香辣牛肉面");
        o2.setColor("3f3f98");
        mockTable.add(o2);

        ObjectMapPo o3 = new ObjectMapPo();
        o3.setTenantId(608158);
        o3.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o3.setKey("JML_PM_YDB_LTSC");
        o3.setAppId("CRM");
        o3.setApiName("ProductObj");
        o3.setObjectId("5c74de485aea47000168fd08");
        o3.setName("一袋半老坛酸菜面");
        o3.setColor("fce011");
        mockTable.add(o3);

        ObjectMapPo o4 = new ObjectMapPo();
        o4.setTenantId(608158);
        o4.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o4.setKey("FS_LOGO");
        o4.setAppId("CRM");
        o4.setApiName("ProductObj");
        o4.setObjectId("06c5417da6de4773bd28e9d2b580c051");
        o4.setName("纷享销客");
        o4.setColor("ec377f");
        mockTable.add(o1);

/**
 * 样板间-泡面
 */
        ObjectMapPo o5 = new ObjectMapPo();
        o5.setTenantId(590103);
        o5.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o5.setKey("JML_PM_YDB_HSNR");
        o5.setAppId("CRM");
        o5.setApiName("ProductObj");
        o5.setObjectId("5caeca06b2b52700017da363");
        o5.setName("一袋半红烧牛肉面");
        o5.setColor("3dd4c4");
        mockTable.add(o5);

        ObjectMapPo o6 = new ObjectMapPo();
        o6.setTenantId(590103);
        o6.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o6.setKey("JML_PM_YDB_XLNR");
        o6.setAppId("CRM");
        o6.setApiName("ProductObj");
        o6.setObjectId("5caecadca06bc300017ed51f");
        o6.setName("一袋半香辣牛肉面");
        o6.setColor("3f3f98");
        mockTable.add(o6);

        ObjectMapPo o7 = new ObjectMapPo();
        o7.setTenantId(590103);
        o7.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o7.setKey("JML_PM_YDB_LTSC");
        o7.setAppId("CRM");
        o7.setApiName("ProductObj");
        o7.setObjectId("5caecb09a06bc300017ed5c8");
        o7.setName("一袋半老坛酸菜面");
        o7.setColor("fce011");
        mockTable.add(o7);

        ObjectMapPo o13 = new ObjectMapPo();
        o13.setTenantId(590103);
        o13.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o13.setKey("FS_LOGO");
        o13.setAppId("CRM");
        o13.setApiName("ProductObj");
        o13.setObjectId("5caed91db2b52700017dc0f7");
        o13.setName("纷享销客");
        o13.setColor("ec377f");
        mockTable.add(o13);

        ObjectMapPo o8 = new ObjectMapPo();
        o8.setTenantId(590103);
        o8.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o8.setKey("1");
        o8.setAppId("CRM");
        o8.setApiName("ProductObj");
        o8.setObjectId("5d6cedd950a3f000016905e1");
        o8.setName("大瓶山楂树下");
        o8.setColor("3dd4c4");
        mockTable.add(o8);

        ObjectMapPo o9 = new ObjectMapPo();
        o9.setTenantId(590103);
        o9.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o9.setKey("2");
        o9.setAppId("CRM");
        o9.setApiName("ProductObj");
        o9.setObjectId("5d6cedf250a3f00001690f1f");
        o9.setName("小瓶山楂树下");
        o9.setColor("3f3f98");
        mockTable.add(o9);

/**
 * fktest106
 */
        ObjectMapPo o14 = new ObjectMapPo();
        o14.setTenantId(590081);
        o14.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o14.setKey("JML_PM_YDB_HSNR");
        o14.setAppId("CRM");
        o14.setApiName("ProductObj");
        o14.setObjectId("5cb7e2c22a51630001b18fe4");
        o14.setName("一袋半红烧牛肉面");
        o14.setColor("3dd4c4");
        mockTable.add(o14);

        ObjectMapPo o15 = new ObjectMapPo();
        o15.setTenantId(590081);
        o15.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o15.setKey("JML_PM_YDB_XLNR");
        o15.setAppId("CRM");
        o15.setApiName("ProductObj");
        o15.setObjectId("5cb7e2a697da9a00016c86e8");
        o15.setName("一袋半香辣牛肉面");
        o15.setColor("3f3f98");
        mockTable.add(o15);

        ObjectMapPo o16 = new ObjectMapPo();
        o16.setTenantId(590081);
        o16.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o16.setKey("JML_PM_YDB_LTSC");
        o16.setAppId("CRM");
        o16.setApiName("ProductObj");
        o16.setObjectId("5cb7e27d2a51630001b1772c");
        o16.setName("一袋半老坛酸菜面");
        o16.setColor("fce011");
        mockTable.add(o16);

        ObjectMapPo o17 = new ObjectMapPo();
        o17.setTenantId(590081);
        o17.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o17.setKey("FS_LOGO");
        o17.setAppId("CRM");
        o17.setApiName("ProductObj");
        o17.setObjectId("5cb7e2e02a51630001b194db");
        o17.setName("纷享销客");
        o17.setColor("ec377f");
        mockTable.add(o14);

/**
 * 644282
 */
        ObjectMapPo o18 = new ObjectMapPo();
        o18.setTenantId(644282);
        o18.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o18.setKey("JML_PM_YDB_HSNR");
        o18.setAppId("CRM");
        o18.setApiName("ProductObj");
        o18.setObjectId("1500e8d0fcea4751ae79e40e5f491199");
        o18.setName("一袋半红烧牛肉面");
        o18.setColor("3dd4c4");
        mockTable.add(o18);

        ObjectMapPo o19 = new ObjectMapPo();
        o19.setTenantId(644282);
        o19.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o19.setKey("JML_PM_YDB_XLNR");
        o19.setAppId("CRM");
        o19.setApiName("ProductObj");
        o19.setObjectId("b128ebbd6a2d4aae84be063bafad0b81");
        o19.setName("一袋半香辣牛肉面");
        o19.setColor("3f3f98");
        mockTable.add(o19);

        ObjectMapPo o20 = new ObjectMapPo();
        o20.setTenantId(644282);
        o20.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o20.setKey("JML_PM_YDB_LTSC");
        o20.setAppId("CRM");
        o20.setApiName("ProductObj");
        o20.setObjectId("cb7e3b80959b6e358a9e06d");
        o20.setName("一袋半老坛酸菜面");
        o20.setColor("fce011");
        mockTable.add(o20);

        ObjectMapPo o21 = new ObjectMapPo();
        o21.setTenantId(644282);
        o21.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o21.setKey("FS_LOGO");
        o21.setAppId("CRM");
        o21.setApiName("ProductObj");
        o21.setObjectId("5cb7e3e2b287140001cb9262");
        o21.setName("纷享销客");
        o21.setColor("ec377f");
        mockTable.add(o18);

/**
 * 590251
 */
        ObjectMapPo o22 = new ObjectMapPo();
        o22.setTenantId(590251);
        o22.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o22.setKey("CYSX150X3_XXY_150X3");
        o22.setAppId("CRM");
        o22.setApiName("ProductObj");
        o22.setObjectId("5d1a0cffb8d6e000014a4d07");
        o22.setName("心相印纸巾");
        o22.setColor("3dd4c4");
        mockTable.add(o22);

        ObjectMapPo o23 = new ObjectMapPo();
        o23.setTenantId(590251);
        o23.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o23.setKey("CBCM420MM_SPACE7_420MM");
        o23.setAppId("CRM");
        o23.setApiName("ProductObj");
        o23.setObjectId("5d1a0c1ce94c9b0001343813");
        o23.setName("七度空间卫生巾");
        o23.setColor("3f3f98");
        mockTable.add(o23);

        ObjectMapPo o24 = new ObjectMapPo();
        o24.setTenantId(590081);
        o24.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o24.setAppId("CRM");
        o24.setApiName("ProductObj");
        o24.setKey("午后奶茶_统一_500ml");
        o24.setName("午后奶茶_统一_500ml");
        o24.setObjectId("5d6e510e246fd0000146cbf5");
        o24.setColor("679363");
        mockTable.add(o24);

        ObjectMapPo o25 = new ObjectMapPo();
        o25.setTenantId(590081);
        o25.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o25.setAppId("CRM");
        o25.setApiName("ProductObj");
        o25.setKey("百事blue_百事_450ml");
        o25.setName("百事blue_百事_450ml");
        o25.setObjectId("5d6e510e246fd0000146cc56");
        o25.setColor("afdcd6");
        mockTable.add(o25);

        ObjectMapPo o26 = new ObjectMapPo();
        o26.setTenantId(590081);
        o26.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o26.setAppId("CRM");
        o26.setApiName("ProductObj");
        o26.setKey("农夫果园胡橙30_农夫山泉_500ml");
        o26.setName("农夫果园胡橙30_农夫山泉_500ml");
        o26.setObjectId("5d6e510e246fd0000146ccb7");
        o26.setColor("cc6044");
        mockTable.add(o26);

        ObjectMapPo o27 = new ObjectMapPo();
        o27.setTenantId(590081);
        o27.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o27.setAppId("CRM");
        o27.setApiName("ProductObj");
        o27.setKey("芬达水蜜桃_可口可乐_500ml");
        o27.setName("芬达水蜜桃_可口可乐_500ml");
        o27.setObjectId("5d6e510e246fd0000146cd18");
        o27.setColor("b75c25");
        mockTable.add(o27);

        ObjectMapPo o28 = new ObjectMapPo();
        o28.setTenantId(590081);
        o28.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o28.setAppId("CRM");
        o28.setApiName("ProductObj");
        o28.setKey("绿茶茉莉味_统一_250ml");
        o28.setName("绿茶茉莉味_统一_250ml");
        o28.setObjectId("5d6e510e246fd0000146cd79");
        o28.setColor("e05829");
        mockTable.add(o28);

        ObjectMapPo o29 = new ObjectMapPo();
        o29.setTenantId(590081);
        o29.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o29.setAppId("CRM");
        o29.setApiName("ProductObj");
        o29.setKey("可乐纤维_可口可乐_500ml");
        o29.setName("可乐纤维_可口可乐_500ml");
        o29.setObjectId("5d6e510e246fd0000146cdda");
        o29.setColor("20da9a");
        mockTable.add(o29);

        ObjectMapPo o30 = new ObjectMapPo();
        o30.setTenantId(590081);
        o30.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o30.setAppId("CRM");
        o30.setApiName("ProductObj");
        o30.setKey("芬达橙_可口可乐_500ml");
        o30.setName("芬达橙_可口可乐_500ml");
        o30.setObjectId("5d6e510f246fd0000146ce3b");
        o30.setColor("9bcdb5");
        mockTable.add(o30);

        ObjectMapPo o31 = new ObjectMapPo();
        o31.setTenantId(590081);
        o31.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o31.setAppId("CRM");
        o31.setApiName("ProductObj");
        o31.setKey("芬达橙_可口可乐_330ml");
        o31.setName("芬达橙_可口可乐_330ml");
        o31.setObjectId("5d6e510f246fd0000146d020");
        o31.setColor("063664");
        mockTable.add(o31);

        ObjectMapPo o32 = new ObjectMapPo();
        o32.setTenantId(590081);
        o32.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o32.setAppId("CRM");
        o32.setApiName("ProductObj");
        o32.setKey("芬达橙_可口可乐_300ml");
        o32.setName("芬达橙_可口可乐_300ml");
        o32.setObjectId("5d6e510f246fd0000146ce9c");
        o32.setColor("c5bc9c");
        mockTable.add(o32);

        ObjectMapPo o33 = new ObjectMapPo();
        o33.setTenantId(590081);
        o33.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o33.setAppId("CRM");
        o33.setApiName("ProductObj");
        o33.setKey("冰红茶_统一_1L");
        o33.setName("冰红茶_统一_1L");
        o33.setObjectId("5d6e510f246fd0000146cefd");
        o33.setColor("ce1654");
        mockTable.add(o33);

        ObjectMapPo o34 = new ObjectMapPo();
        o34.setTenantId(590081);
        o34.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o34.setAppId("CRM");
        o34.setApiName("ProductObj");
        o34.setKey("无糖可乐_百事_500ml");
        o34.setName("无糖可乐_百事_500ml");
        o34.setObjectId("5d6e510f246fd0000146cfbf");
        o34.setColor("2470a0");
        mockTable.add(o34);

        ObjectMapPo o35 = new ObjectMapPo();
        o35.setTenantId(590081);
        o35.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o35.setAppId("CRM");
        o35.setApiName("ProductObj");
        o35.setKey("冰露饮用水_可口可乐_550ml");
        o35.setName("冰露饮用水_可口可乐_550ml");
        o35.setObjectId("5d6e510f246fd0000146d081");
        o35.setColor("ffa92e");
        mockTable.add(o35);

        ObjectMapPo o36 = new ObjectMapPo();
        o36.setTenantId(590081);
        o36.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o36.setAppId("CRM");
        o36.setApiName("ProductObj");
        o36.setKey("无糖可乐_百事_330ml");
        o36.setName("无糖可乐_百事_330ml");
        o36.setObjectId("5d6e510f246fd0000146d0e2");
        o36.setColor("f96474");
        mockTable.add(o36);

        ObjectMapPo o37 = new ObjectMapPo();
        o37.setTenantId(590081);
        o37.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o37.setAppId("CRM");
        o37.setApiName("ProductObj");
        o37.setKey("茉莉蜜茶_康师傅_500ml");
        o37.setName("茉莉蜜茶_康师傅_500ml");
        o37.setObjectId("5d6e510f246fd0000146d143");
        o37.setColor("d9daf2");
        mockTable.add(o37);

        ObjectMapPo o38 = new ObjectMapPo();
        o38.setTenantId(590081);
        o38.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o38.setAppId("CRM");
        o38.setApiName("ProductObj");
        o38.setKey("茉莉蜜茶_康师傅_2L");
        o38.setName("茉莉蜜茶_康师傅_2L");
        o38.setObjectId("5d6e510f246fd0000146d1a4");
        o38.setColor("715f5b");
        mockTable.add(o38);

        ObjectMapPo o39 = new ObjectMapPo();
        o39.setTenantId(590081);
        o39.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o39.setAppId("CRM");
        o39.setApiName("ProductObj");
        o39.setKey("茉莉蜜茶_康师傅_1L");
        o39.setName("茉莉蜜茶_康师傅_1L");
        o39.setObjectId("5d6e510f246fd0000146d205");
        o39.setColor("5d1677");
        mockTable.add(o39);

        ObjectMapPo o40 = new ObjectMapPo();
        o40.setTenantId(590081);
        o40.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o40.setAppId("CRM");
        o40.setApiName("ProductObj");
        o40.setKey("芬达青柠_可口可乐_500ml");
        o40.setName("芬达青柠_可口可乐_500ml");
        o40.setObjectId("5d6e510f246fd0000146d266");
        o40.setColor("d25565");
        mockTable.add(o40);

        ObjectMapPo o41 = new ObjectMapPo();
        o41.setTenantId(590081);
        o41.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o41.setAppId("CRM");
        o41.setApiName("ProductObj");
        o41.setKey("阿萨姆小奶茶_统一_360ml");
        o41.setName("阿萨姆小奶茶_统一_360ml");
        o41.setObjectId("5d6e5110246fd0000146d56e");
        o41.setColor("c91f25");
        mockTable.add(o41);

        ObjectMapPo o42 = new ObjectMapPo();
        o42.setTenantId(590081);
        o42.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o42.setAppId("CRM");
        o42.setApiName("ProductObj");
        o42.setKey("阿萨姆原味奶茶_统一_500ml");
        o42.setName("阿萨姆原味奶茶_统一_500ml");
        o42.setObjectId("5d6e510f246fd0000146d2c7");
        o42.setColor("43d9fd");
        mockTable.add(o42);

        ObjectMapPo o43 = new ObjectMapPo();
        o43.setTenantId(590081);
        o43.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o43.setAppId("CRM");
        o43.setApiName("ProductObj");
        o43.setKey("可乐_可口可乐_500ml");
        o43.setName("可乐_可口可乐_500ml");
        o43.setObjectId("5d6e510f246fd0000146d328");
        o43.setColor("59c2c4");
        mockTable.add(o43);

        ObjectMapPo o44 = new ObjectMapPo();
        o44.setTenantId(590081);
        o44.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o44.setAppId("CRM");
        o44.setApiName("ProductObj");
        o44.setKey("饮用水_农夫山泉_380ml");
        o44.setName("饮用水_农夫山泉_380ml");
        o44.setObjectId("5d6e5110246fd0000146d389");
        o44.setColor("4f658a");
        mockTable.add(o44);

        ObjectMapPo o45 = new ObjectMapPo();
        o45.setTenantId(590081);
        o45.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o45.setAppId("CRM");
        o45.setApiName("ProductObj");
        o45.setKey("可乐_百事_330ml");
        o45.setName("可乐_百事_330ml");
        o45.setObjectId("5d6e5110246fd0000146d3ea");
        o45.setColor("98185e");
        mockTable.add(o45);

        ObjectMapPo o46 = new ObjectMapPo();
        o46.setTenantId(590081);
        o46.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o46.setAppId("CRM");
        o46.setApiName("ProductObj");
        o46.setKey("水溶C100青皮桔_农夫山泉_445ml");
        o46.setName("水溶C100青皮桔_农夫山泉_445ml");
        o46.setObjectId("5d6e510e246fd0000146cb33");
        o46.setColor("18979f");
        mockTable.add(o46);

        ObjectMapPo o47 = new ObjectMapPo();
        o47.setTenantId(590081);
        o47.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o47.setAppId("CRM");
        o47.setApiName("ProductObj");
        o47.setKey("绿茶_统一_1L");
        o47.setName("绿茶_统一_1L");
        o47.setObjectId("5d6e510e246fd0000146cb94");
        o47.setColor("508fb3");
        mockTable.add(o47);

        ObjectMapPo o48 = new ObjectMapPo();
        o48.setTenantId(590081);
        o48.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o48.setAppId("CRM");
        o48.setApiName("ProductObj");
        o48.setKey("冰糖雪梨_康师傅_1L");
        o48.setName("冰糖雪梨_康师傅_1L");
        o48.setObjectId("5d6e5110246fd0000146d44b");
        o48.setColor("f92a82");
        mockTable.add(o48);

        ObjectMapPo o49 = new ObjectMapPo();
        o49.setTenantId(590081);
        o49.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o49.setAppId("CRM");
        o49.setApiName("ProductObj");
        o49.setKey("海晶柠檬_康师傅_500ml");
        o49.setName("海晶柠檬_康师傅_500ml");
        o49.setObjectId("5d6e510e246fd0000146cad2");
        o49.setColor("edb67c");
        mockTable.add(o49);

        ObjectMapPo o50 = new ObjectMapPo();
        o50.setTenantId(590081);
        o50.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o50.setAppId("CRM");
        o50.setApiName("ProductObj");
        o50.setKey("茉莉清茶_康师傅_1L");
        o50.setName("茉莉清茶_康师傅_1L");
        o50.setObjectId("5d6e510e246fd0000146ca10");
        o50.setColor("f8061d");
        mockTable.add(o50);

        ObjectMapPo o51 = new ObjectMapPo();
        o51.setTenantId(590081);
        o51.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o51.setAppId("CRM");
        o51.setApiName("ProductObj");
        o51.setKey("雪碧纤维_可口可乐_500ml");
        o51.setName("雪碧纤维_可口可乐_500ml");
        o51.setObjectId("5d6e510f246fd0000146cf5e");
        o51.setColor("fc8e1a");
        mockTable.add(o51);

        ObjectMapPo o52 = new ObjectMapPo();
        o52.setTenantId(590081);
        o52.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o52.setAppId("CRM");
        o52.setApiName("ProductObj");
        o52.setKey("绿茶蜂蜜茉莉味_康师傅_2L");
        o52.setName("绿茶蜂蜜茉莉味_康师傅_2L");
        o52.setObjectId("5d6e5110246fd0000146d4ac");
        o52.setColor("9385ec");
        mockTable.add(o52);

        ObjectMapPo o53 = new ObjectMapPo();
        o53.setTenantId(590081);
        o53.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o53.setAppId("CRM");
        o53.setApiName("ProductObj");
        o53.setKey("冰红茶_统一_500ml");
        o53.setName("冰红茶_统一_500ml");
        o53.setObjectId("5d6e5110246fd0000146d50d");
        o53.setColor("b9cf44");
        mockTable.add(o53);

        ObjectMapPo o54 = new ObjectMapPo();
        o54.setTenantId(590081);
        o54.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o54.setAppId("CRM");
        o54.setApiName("ProductObj");
        o54.setKey("水溶C100西柚_农夫山泉_445ml");
        o54.setName("水溶C100西柚_农夫山泉_445ml");
        o54.setObjectId("5d6e5110246fd0000146d5cf");
        o54.setColor("244b67");
        mockTable.add(o54);

        ObjectMapPo o55 = new ObjectMapPo();
        o55.setTenantId(590081);
        o55.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o55.setAppId("CRM");
        o55.setApiName("ProductObj");
        o55.setKey("饮用水_农夫山泉_4L");
        o55.setName("饮用水_农夫山泉_4L");
        o55.setObjectId("5d6e5110246fd0000146d630");
        o55.setColor("90fee7");
        mockTable.add(o55);

        ObjectMapPo o56 = new ObjectMapPo();
        o56.setTenantId(590081);
        o56.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o56.setAppId("CRM");
        o56.setApiName("ProductObj");
        o56.setKey("茶兀柚子绿茶_农夫山泉_500ml");
        o56.setName("茶兀柚子绿茶_农夫山泉_500ml");
        o56.setObjectId("5d6e5110246fd0000146d691");
        o56.setColor("f97576");
        mockTable.add(o56);

        ObjectMapPo o57 = new ObjectMapPo();
        o57.setTenantId(590081);
        o57.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o57.setAppId("CRM");
        o57.setApiName("ProductObj");
        o57.setKey("绿茶_统一_500ml");
        o57.setName("绿茶_统一_500ml");
        o57.setObjectId("5d6e510e246fd0000146ca71");
        o57.setColor("99b8c1");
        mockTable.add(o57);

        ObjectMapPo o59 = new ObjectMapPo();
        o59.setTenantId(590081);
        o59.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o59.setAppId("CRM");
        o59.setApiName("ProductObj");
        o59.setKey("一桶半香辣牛肉面_今麦郎_一桶半");
        o59.setName("一桶半香辣牛肉面");
        o59.setObjectId("5d75f29835ec0b0001c08167");
        o59.setColor("3dd4c4");
        mockTable.add(o59);

        ObjectMapPo o60 = new ObjectMapPo();
        o60.setTenantId(590081);
        o60.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o60.setAppId("CRM");
        o60.setApiName("ProductObj");
        o60.setKey("一桶半葱香排骨面_今麦郎_一桶半");
        o60.setName("一桶半葱香排骨面");
        o60.setObjectId("5d75f29835ec0b0001c0815e");
        o60.setColor("3f3f98");
        mockTable.add(o60);

        ObjectMapPo o61 = new ObjectMapPo();
        o61.setTenantId(590081);
        o61.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o61.setAppId("CRM");
        o61.setApiName("ProductObj");
        o61.setKey("一桶半红烧牛肉面_今麦郎_一桶半");
        o61.setName("一桶半红烧牛肉面");
        o61.setObjectId("5d75f29835ec0b0001c0815f");
        o61.setColor("fce011");
        mockTable.add(o61);

        ObjectMapPo o62 = new ObjectMapPo();
        o62.setTenantId(590081);
        o62.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o62.setAppId("CRM");
        o62.setApiName("ProductObj");
        o62.setKey("一桶半鲜虾鱼板面_今麦郎_一桶半");
        o62.setName("一桶半鲜虾鱼板面");
        o62.setObjectId("5d75f29835ec0b0001c08160");
        o62.setColor("ec377f");
        mockTable.add(o62);

        ObjectMapPo o63 = new ObjectMapPo();
        o63.setTenantId(590081);
        o63.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o63.setAppId("CRM");
        o63.setApiName("ProductObj");
        o63.setKey("一桶半麻酱担担面_今麦郎_一桶半");
        o63.setName("一桶半麻酱担担面");
        o63.setObjectId("5d75f29835ec0b0001c08161");
        o63.setColor("679363");
        mockTable.add(o63);

        ObjectMapPo o64 = new ObjectMapPo();
        o64.setTenantId(590081);
        o64.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o64.setAppId("CRM");
        o64.setApiName("ProductObj");
        o64.setKey("一桶半小鸡炖蘑菇面_今麦郎_一桶半");
        o64.setName("一桶半小鸡炖蘑菇面");
        o64.setObjectId("5d75f29835ec0b0001c08162");
        o64.setColor("afdcd6");
        mockTable.add(o64);

        ObjectMapPo o65 = new ObjectMapPo();
        o65.setTenantId(590081);
        o65.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o65.setAppId("CRM");
        o65.setApiName("ProductObj");
        o65.setKey("一桶半西红柿鸡蛋打卤面_今麦郎_一桶半");
        o65.setName("一桶半西红柿鸡蛋打卤面");
        o65.setObjectId("5d75f29835ec0b0001c08163");
        o65.setColor("cc6044");
        mockTable.add(o65);

        ObjectMapPo o66 = new ObjectMapPo();
        o66.setTenantId(590081);
        o66.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o66.setAppId("CRM");
        o66.setApiName("ProductObj");
        o66.setKey("一桶半酸豆角排骨面_今麦郎_一桶半");
        o66.setName("一桶半酸豆角排骨面");
        o66.setObjectId("5d75f29835ec0b0001c08164");
        o66.setColor("b75c25");
        mockTable.add(o66);

        ObjectMapPo o67 = new ObjectMapPo();
        o67.setTenantId(590081);
        o67.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o67.setAppId("CRM");
        o67.setApiName("ProductObj");
        o67.setKey("一桶半老坛酸菜牛肉面_今麦郎_一桶半");
        o67.setName("一桶半老坛酸菜牛肉面");
        o67.setObjectId("5d75f29835ec0b0001c08165");
        o67.setColor("e05829");
        mockTable.add(o67);

        ObjectMapPo o68 = new ObjectMapPo();
        o68.setTenantId(590081);
        o68.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o68.setAppId("CRM");
        o68.setApiName("ProductObj");
        o68.setKey("一桶半重庆小面_今麦郎_一桶半");
        o68.setName("一桶半重庆小面");
        o68.setObjectId("5d75f29835ec0b0001c08166");
        o68.setColor("20da9a");
        mockTable.add(o68);

        ObjectMapPo o69 = new ObjectMapPo();
        o69.setTenantId(590081);
        o69.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o69.setAppId("CRM");
        o69.setApiName("ProductObj");
        o69.setKey("一桶半青花椒牛肉面_今麦郎_一桶半");
        o69.setName("一桶半青花椒牛肉面");
        o69.setObjectId("5d75f29835ec0b0001c08168");
        o69.setColor("9bcdb5");
        mockTable.add(o69);

        ObjectMapPo o70 = new ObjectMapPo();
        o70.setTenantId(590081);
        o70.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o70.setAppId("CRM");
        o70.setApiName("ProductObj");
        o70.setKey("一桶半酸辣牛肉面_今麦郎_一桶半");
        o70.setName("一桶半酸辣牛肉面");
        o70.setObjectId("5d75f29835ec0b0001c08169");
        o70.setColor("063664");
        mockTable.add(o70);

        ObjectMapPo o71 = new ObjectMapPo();
        o71.setTenantId(590081);
        o71.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o71.setAppId("CRM");
        o71.setApiName("ProductObj");
        o71.setKey("一桶半红油爆椒牛肉面_今麦郎_一桶半");
        o71.setName("一桶半红油爆椒牛肉面");
        o71.setObjectId("5d75f29835ec0b0001c0816a");
        o71.setColor("c5bc9c");
        mockTable.add(o71);

        ObjectMapPo o72 = new ObjectMapPo();
        o72.setTenantId(590081);
        o72.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o72.setAppId("CRM");
        o72.setApiName("ProductObj");
        o72.setKey("一桶半麻辣牛肉面_今麦郎_一桶半");
        o72.setName("一桶半麻辣牛肉面");
        o72.setObjectId("5d75f29835ec0b0001c0816b");
        o72.setColor("ce1654");
        mockTable.add(o72);

        ObjectMapPo o73 = new ObjectMapPo();
        o73.setTenantId(590081);
        o73.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o73.setAppId("CRM");
        o73.setApiName("ProductObj");
        o73.setKey("一桶半泡椒牛肉面_今麦郎_一桶半");
        o73.setName("一桶半泡椒牛肉面");
        o73.setObjectId("5d75f29835ec0b0001c0816c");
        o73.setColor("2470a0");
        mockTable.add(o73);

        ObjectMapPo o74 = new ObjectMapPo();
        o74.setTenantId(590081);
        o74.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o74.setAppId("CRM");
        o74.setApiName("ProductObj");
        o74.setKey("一桶半麻辣排骨面_今麦郎_一桶半");
        o74.setName("一桶半麻辣排骨面");
        o74.setObjectId("5d75f29835ec0b0001c0816d");
        o74.setColor("ffa92e");
        mockTable.add(o74);

        ObjectMapPo o75 = new ObjectMapPo();
        o75.setTenantId(590081);
        o75.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o75.setAppId("CRM");
        o75.setApiName("ProductObj");
        o75.setKey("一桶半辣椒炒肉面_今麦郎_一桶半");
        o75.setName("一桶半辣椒炒肉面");
        o75.setObjectId("5d75f29835ec0b0001c0816e");
        o75.setColor("f96474");
        mockTable.add(o75);

        ObjectMapPo o76 = new ObjectMapPo();
        o76.setTenantId(590081);
        o76.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o76.setAppId("CRM");
        o76.setApiName("ProductObj");
        o76.setKey("一桶半猪骨白汤面_今麦郎_一桶半");
        o76.setName("一桶半猪骨白汤面");
        o76.setObjectId("5d75f29835ec0b0001c0816f");
        o76.setColor("d9daf2");
        mockTable.add(o76);

        ObjectMapPo o77 = new ObjectMapPo();
        o77.setTenantId(590081);
        o77.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o77.setAppId("CRM");
        o77.setApiName("ProductObj");
        o77.setKey("一桶半辣白菜牛肉面_今麦郎_一桶半");
        o77.setName("一桶半辣白菜牛肉面");
        o77.setObjectId("5d75f29835ec0b0001c08170");
        o77.setColor("715f5b");
        mockTable.add(o77);

        ObjectMapPo o78 = new ObjectMapPo();
        o78.setTenantId(590081);
        o78.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o78.setAppId("CRM");
        o78.setApiName("ProductObj");
        o78.setKey("一桶半清真红烧牛肉面_今麦郎_一桶半");
        o78.setName("一桶半清真红烧牛肉面");
        o78.setObjectId("5d75f29835ec0b0001c08171");
        o78.setColor("5d1677");
        mockTable.add(o78);

        ObjectMapPo o79 = new ObjectMapPo();
        o79.setTenantId(590081);
        o79.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o79.setAppId("CRM");
        o79.setApiName("ProductObj");
        o79.setKey("一桶半清真香辣牛肉面_今麦郎_一桶半");
        o79.setName("一桶半清真香辣牛肉面");
        o79.setObjectId("5d75f29835ec0b0001c08172");
        o79.setColor("d25565");
        mockTable.add(o79);

        ObjectMapPo o80 = new ObjectMapPo();
        o80.setTenantId(590081);
        o80.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o80.setAppId("CRM");
        o80.setApiName("ProductObj");
        o80.setKey("一桶半清真老坛酸菜面_今麦郎_一桶半");
        o80.setName("一桶半清真老坛酸菜面");
        o80.setObjectId("5d75f29835ec0b0001c08173");
        o80.setColor("c91f25");
        mockTable.add(o80);

        ObjectMapPo o81 = new ObjectMapPo();
        o81.setTenantId(590081);
        o81.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o81.setAppId("CRM");
        o81.setApiName("ProductObj");
        o81.setKey("一桶半清真油泼辣子酸汤面_今麦郎_一桶半");
        o81.setName("一桶半清真油泼辣子酸汤面");
        o81.setObjectId("5d75f29835ec0b0001c08174");
        o81.setColor("43d9fd");
        mockTable.add(o81);

        ObjectMapPo o82 = new ObjectMapPo();
        o82.setTenantId(590081);
        o82.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o82.setAppId("CRM");
        o82.setApiName("ProductObj");
        o82.setKey("一桶半清真麻辣牛肉面_今麦郎_一桶半");
        o82.setName("一桶半清真麻辣牛肉面");
        o82.setObjectId("5d75f29835ec0b0001c08175");
        o82.setColor("59c2c4");
        mockTable.add(o82);

        ObjectMapPo o83 = new ObjectMapPo();
        o83.setTenantId(590081);
        o83.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o83.setAppId("CRM");
        o83.setApiName("ProductObj");
        o83.setKey("一桶半清真酸豆角牛肉面_今麦郎_一桶半");
        o83.setName("一桶半清真酸豆角牛肉面");
        o83.setObjectId("5d75f29835ec0b0001c08176");
        o83.setColor("4f658a");
        mockTable.add(o83);

        ObjectMapPo o84 = new ObjectMapPo();
        o84.setTenantId(590081);
        o84.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o84.setAppId("CRM");
        o84.setApiName("ProductObj");
        o84.setKey("一桶半清真鲜虾鱼板面_今麦郎_一桶半");
        o84.setName("一桶半清真鲜虾鱼板面");
        o84.setObjectId("5d75f29835ec0b0001c08177");
        o84.setColor("98185e");
        mockTable.add(o84);

        ObjectMapPo o85 = new ObjectMapPo();
        o85.setTenantId(590081);
        o85.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o85.setAppId("CRM");
        o85.setApiName("ProductObj");
        o85.setKey("一袋半西红柿鸡蛋打卤面_今麦郎_一袋半");
        o85.setName("一袋半西红柿鸡蛋打卤面");
        o85.setObjectId("5d75ed5eaff24300016c317e");
        o85.setColor("18979f");
        mockTable.add(o85);

        ObjectMapPo o86 = new ObjectMapPo();
        o86.setTenantId(590081);
        o86.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o86.setAppId("CRM");
        o86.setApiName("ProductObj");
        o86.setKey("一袋半小鸡炖蘑菇面_今麦郎_一袋半");
        o86.setName("一袋半小鸡炖蘑菇面");
        o86.setObjectId("5d75ed5eaff24300016c3189");
        o86.setColor("508fb3");
        mockTable.add(o86);

        ObjectMapPo o87 = new ObjectMapPo();
        o87.setTenantId(590081);
        o87.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o87.setAppId("CRM");
        o87.setApiName("ProductObj");
        o87.setKey("一袋半葱香排骨面_今麦郎_一袋半");
        o87.setName("一袋半葱香排骨面");
        o87.setObjectId("5d75ed5eaff24300016c318f");
        o87.setColor("f92a82");
        mockTable.add(o87);

        ObjectMapPo o88 = new ObjectMapPo();
        o88.setTenantId(590081);
        o88.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o88.setAppId("CRM");
        o88.setApiName("ProductObj");
        o88.setKey("一袋半红烧牛肉面_今麦郎_一袋半");
        o88.setName("一袋半红烧牛肉面");
        o88.setObjectId("5d75ed5eaff24300016c318e");
        o88.setColor("edb67c");
        mockTable.add(o88);

        ObjectMapPo o89 = new ObjectMapPo();
        o89.setTenantId(590081);
        o89.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o89.setAppId("CRM");
        o89.setApiName("ProductObj");
        o89.setKey("一袋半海带排骨面_今麦郎_一袋半");
        o89.setName("一袋半海带排骨面");
        o89.setObjectId("5d75ed5eaff24300016c318d");
        o89.setColor("f8061d");
        mockTable.add(o89);

        ObjectMapPo o90 = new ObjectMapPo();
        o90.setTenantId(590081);
        o90.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o90.setAppId("CRM");
        o90.setApiName("ProductObj");
        o90.setKey("一袋半鲜虾鱼饭面_今麦郎_一袋半");
        o90.setName("一袋半鲜虾鱼饭面");
        o90.setObjectId("5d75ed5eaff24300016c318c");
        o90.setColor("fc8e1a");
        mockTable.add(o90);

        ObjectMapPo o91 = new ObjectMapPo();
        o91.setTenantId(590081);
        o91.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o91.setAppId("CRM");
        o91.setApiName("ProductObj");
        o91.setKey("一袋半猪骨白汤面_今麦郎_一袋半");
        o91.setName("一袋半猪骨白汤面");
        o91.setObjectId("5d75ed5eaff24300016c318b");
        o91.setColor("9385ec");
        mockTable.add(o91);

        ObjectMapPo o92 = new ObjectMapPo();
        o92.setTenantId(590081);
        o92.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o92.setAppId("CRM");
        o92.setApiName("ProductObj");
        o92.setKey("一袋半麻酱担担面_今麦郎_一袋半");
        o92.setName("一袋半麻酱担担面");
        o92.setObjectId("5d75ed5eaff24300016c317a");
        o92.setColor("b9cf44");
        mockTable.add(o92);

        ObjectMapPo o93 = new ObjectMapPo();
        o93.setTenantId(590081);
        o93.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o93.setAppId("CRM");
        o93.setApiName("ProductObj");
        o93.setKey("一袋半香辣牛肉面_今麦郎_一袋半");
        o93.setName("一袋半香辣牛肉面");
        o93.setObjectId("5d75ed5eaff24300016c3179");
        o93.setColor("244b67");
        mockTable.add(o93);

        ObjectMapPo o94 = new ObjectMapPo();
        o94.setTenantId(590081);
        o94.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o94.setAppId("CRM");
        o94.setApiName("ProductObj");
        o94.setKey("一袋半酸豆角排骨面_今麦郎_一袋半");
        o94.setName("一袋半酸豆角排骨面");
        o94.setObjectId("5d75ed5eaff24300016c3178");
        o94.setColor("90fee7");
        mockTable.add(o94);

        ObjectMapPo o95 = new ObjectMapPo();
        o95.setTenantId(590081);
        o95.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o95.setAppId("CRM");
        o95.setApiName("ProductObj");
        o95.setKey("一袋半重庆小面_今麦郎_一袋半");
        o95.setName("一袋半重庆小面");
        o95.setObjectId("5d75ed5eaff24300016c3177");
        o95.setColor("f97576");
        mockTable.add(o95);

        ObjectMapPo o96 = new ObjectMapPo();
        o96.setTenantId(590081);
        o96.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o96.setAppId("CRM");
        o96.setApiName("ProductObj");
        o96.setKey("一袋半老坛酸菜牛肉面_今麦郎_一袋半");
        o96.setName("一袋半老坛酸菜牛肉面");
        o96.setObjectId("5d75ed5eaff24300016c3176");
        o96.setColor("99b8c1");
        mockTable.add(o96);

        ObjectMapPo o97 = new ObjectMapPo();
        o97.setTenantId(590081);
        o97.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o97.setAppId("CRM");
        o97.setApiName("ProductObj");
        o97.setKey("一袋半菌菇鸡汤面_今麦郎_一袋半");
        o97.setName("一袋半菌菇鸡汤面");
        o97.setObjectId("5d75ed5eaff24300016c3175");
        o97.setColor("b17737");
        mockTable.add(o97);

        ObjectMapPo o98 = new ObjectMapPo();
        o98.setTenantId(590081);
        o98.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o98.setAppId("CRM");
        o98.setApiName("ProductObj");
        o98.setKey("一袋半青花椒牛肉面_今麦郎_一袋半");
        o98.setName("一袋半青花椒牛肉面");
        o98.setObjectId("5d75ed5eaff24300016c3174");
        o98.setColor("f6acff");
        mockTable.add(o98);

        ObjectMapPo o99 = new ObjectMapPo();
        o99.setTenantId(590081);
        o99.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o99.setAppId("CRM");
        o99.setApiName("ProductObj");
        o99.setKey("一袋半酸辣牛肉面_今麦郎_一袋半");
        o99.setName("一袋半酸辣牛肉面");
        o99.setObjectId("5d75ed5eaff24300016c3173");
        o99.setColor("beded6");
        mockTable.add(o99);

        ObjectMapPo o100 = new ObjectMapPo();
        o100.setTenantId(590081);
        o100.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o100.setAppId("CRM");
        o100.setApiName("ProductObj");
        o100.setKey("一袋半红油爆椒牛肉面_今麦郎_一袋半");
        o100.setName("一袋半红油爆椒牛肉面");
        o100.setObjectId("5d75ed5eaff24300016c3172");
        o100.setColor("7fbfd9");
        mockTable.add(o100);

        ObjectMapPo o101 = new ObjectMapPo();
        o101.setTenantId(590081);
        o101.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o101.setAppId("CRM");
        o101.setApiName("ProductObj");
        o101.setKey("一袋半麻辣牛肉面_今麦郎_一袋半");
        o101.setName("一袋半麻辣牛肉面");
        o101.setObjectId("5d75ed5eaff24300016c3171");
        o101.setColor("3a9367");
        mockTable.add(o101);

        ObjectMapPo o102 = new ObjectMapPo();
        o102.setTenantId(590081);
        o102.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o102.setAppId("CRM");
        o102.setApiName("ProductObj");
        o102.setKey("一袋半泡椒牛肉面_今麦郎_一袋半");
        o102.setName("一袋半泡椒牛肉面");
        o102.setObjectId("5d75ed5eaff24300016c3170");
        o102.setColor("033a95");
        mockTable.add(o102);

        ObjectMapPo o103 = new ObjectMapPo();
        o103.setTenantId(590081);
        o103.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o103.setAppId("CRM");
        o103.setApiName("ProductObj");
        o103.setKey("一袋半麻辣排骨面_今麦郎_一袋半");
        o103.setName("一袋半麻辣排骨面");
        o103.setObjectId("5d75ed5eaff24300016c316f");
        o103.setColor("9b39ec");
        mockTable.add(o103);

        ObjectMapPo o104 = new ObjectMapPo();
        o104.setTenantId(590081);
        o104.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o104.setAppId("CRM");
        o104.setApiName("ProductObj");
        o104.setKey("一袋半辣椒炒肉面_今麦郎_一袋半");
        o104.setName("一袋半辣椒炒肉面");
        o104.setObjectId("5d75ed5eaff24300016c316e");
        o104.setColor("1f83ff");
        mockTable.add(o104);

        ObjectMapPo o105 = new ObjectMapPo();
        o105.setTenantId(590081);
        o105.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o105.setAppId("CRM");
        o105.setApiName("ProductObj");
        o105.setKey("一袋半清真红烧牛肉面_今麦郎_一袋半");
        o105.setName("一袋半清真红烧牛肉面");
        o105.setObjectId("5d75ed5eaff24300016c316d");
        o105.setColor("d2a6ce");
        mockTable.add(o105);

        ObjectMapPo o106 = new ObjectMapPo();
        o106.setTenantId(590081);
        o106.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o106.setAppId("CRM");
        o106.setApiName("ProductObj");
        o106.setKey("一袋半清真香辣牛肉面_今麦郎_一袋半");
        o106.setName("一袋半清真香辣牛肉面");
        o106.setObjectId("5d75ed5eaff24300016c316c");
        o106.setColor("d24c4f");
        mockTable.add(o106);

        ObjectMapPo o107 = new ObjectMapPo();
        o107.setTenantId(590081);
        o107.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o107.setAppId("CRM");
        o107.setApiName("ProductObj");
        o107.setKey("一袋半清真老坛酸菜牛肉面_今麦郎_一袋半");
        o107.setName("一袋半清真老坛酸菜牛肉面");
        o107.setObjectId("5d75ed5eaff24300016c316b");
        o107.setColor("e0c29d");
        mockTable.add(o107);

        ObjectMapPo o108 = new ObjectMapPo();
        o108.setTenantId(590081);
        o108.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o108.setAppId("CRM");
        o108.setApiName("ProductObj");
        o108.setKey("一袋半清真酸辣牛肉面_今麦郎_一袋半");
        o108.setName("一袋半清真酸辣牛肉面");
        o108.setObjectId("5d75ed5eaff24300016c316a");
        o108.setColor("259bc9");
        mockTable.add(o108);

        ObjectMapPo o109 = new ObjectMapPo();
        o109.setTenantId(590081);
        o109.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o109.setAppId("CRM");
        o109.setApiName("ProductObj");
        o109.setKey("一袋半清真麻辣牛肉面_今麦郎_一袋半");
        o109.setName("一袋半清真麻辣牛肉面");
        o109.setObjectId("5d75ed5eaff24300016c3169");
        o109.setColor("86c12d");
        mockTable.add(o109);

        ObjectMapPo o110 = new ObjectMapPo();
        o110.setTenantId(590081);
        o110.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o110.setAppId("CRM");
        o110.setApiName("ProductObj");
        o110.setKey("一袋半清真油泼辣子酸汤面_今麦郎_一袋半");
        o110.setName("一袋半清真油泼辣子酸汤面");
        o110.setObjectId("5d75ed5eaff24300016c3168");
        o110.setColor("6da9ff");
        mockTable.add(o110);

        ObjectMapPo o111 = new ObjectMapPo();
        o111.setTenantId(590081);
        o111.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o111.setAppId("CRM");
        o111.setApiName("ProductObj");
        o111.setKey("一袋半清真酸豆角牛肉面_今麦郎_一袋半");
        o111.setName("一袋半清真酸豆角牛肉面");
        o111.setObjectId("5d75ed5eaff24300016c3167");
        o111.setColor("5f54ce");
        mockTable.add(o111);

        ObjectMapPo o112 = new ObjectMapPo();
        o112.setTenantId(590081);
        o112.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o112.setAppId("CRM");
        o112.setApiName("ProductObj");
        o112.setKey("一袋半清真鲜虾鱼板面_今麦郎_一袋半");
        o112.setName("一袋半清真鲜虾鱼板面");
        o112.setObjectId("5d75ed5eaff24300016c3166");
        o112.setColor("bacd37");
        mockTable.add(o112);

        ObjectMapPo o113 = new ObjectMapPo();
        o113.setTenantId(590081);
        o113.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o113.setAppId("CRM");
        o113.setApiName("ProductObj");
        o113.setKey("一袋半清真重庆小面_今麦郎_一袋半");
        o113.setName("一袋半清真重庆小面");
        o113.setObjectId("5d75ed5eaff24300016c3165");
        o113.setColor("f8decb");
        mockTable.add(o113);

        ObjectMapPo o114 = new ObjectMapPo();
        o114.setTenantId(590081);
        o114.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o114.setAppId("CRM");
        o114.setApiName("ProductObj");
        o114.setKey("一袋半清真菌菇鸡汤面_今麦郎_一袋半");
        o114.setName("一袋半清真菌菇鸡汤面");
        o114.setObjectId("5d75ed5eaff24300016c3164");
        o114.setColor("70cefc");
        mockTable.add(o114);

        ObjectMapPo o115 = new ObjectMapPo();
        o115.setTenantId(590081);
        o115.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        o115.setAppId("CRM");
        o115.setApiName("ProductObj");
        o115.setKey("一袋半清真麻酱担担面_今麦郎_一袋半");
        o115.setName("一袋半清真麻酱担担面");
        o115.setObjectId("5d75ed5eaff24300016c3163");
        o115.setColor("f2b6c2");
        mockTable.add(o115);

        ObjectMapPo other118 = new ObjectMapPo();
        other118.setTenantId(590081);
        other118.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        other118.setAppId("CRM");
        other118.setApiName("ProductObj");
        other118.setKey("0");
        other118.setName("其他饮品");
        other118.setObjectId("D");
        other118.setColor("ff74cc");
        mockTable.add(other118);

        ObjectMapPo o116 = new ObjectMapPo();
        o116.setTenantId(590081);
        o116.setModelId("334d68fc-1234-4ee6-a4b0-5f8a6275f9b6");
        o116.setAppId("CRM");
        o116.setApiName("ProductObj");
        o116.setKey("1");
        o116.setName("大瓶山楂树下");
        o116.setObjectId("5d8992d2300aa60001ae24c4");
        o116.setColor("3dd4c4");
        mockTable.add(o116);

        ObjectMapPo o117 = new ObjectMapPo();
        o117.setTenantId(590081);
        o117.setModelId("334d68fc-1234-4ee6-a4b0-5f8a6275f9b6");
        o117.setAppId("CRM");
        o117.setApiName("ProductObj");
        o117.setKey("2");
        o117.setName("小瓶山楂树下");
        o117.setObjectId("5d8992d2300aa60001ae24c5");
        o117.setColor("3f3f98");
        mockTable.add(o117);






        ObjectMapPo o234 = new ObjectMapPo();
        o234.setTenantId(670624);
        o234.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o234.setAppId("CRM");
        o234.setApiName("ProductObj");
        o234.setKey("一袋半红油爆椒牛肉面_今麦郎_一袋半");
        o234.setName("一袋半[红油]");
        o234.setObjectId("5df45235501be400013bca8f");
        o234.setColor("3dd4c4");
        mockTable.add(o234);


        ObjectMapPo o235 = new ObjectMapPo();
        o235.setTenantId(670624);
        o235.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o235.setAppId("CRM");
        o235.setApiName("ProductObj");
        o235.setKey("一袋半酸豆角排骨面_今麦郎_一袋半");
        o235.setName("一袋半[酸豆角]");
        o235.setObjectId("5df45235501be400013bca97");
        o235.setColor("3f3f98");
        mockTable.add(o235);


        ObjectMapPo o236 = new ObjectMapPo();
        o236.setTenantId(670624);
        o236.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o236.setAppId("CRM");
        o236.setApiName("ProductObj");
        o236.setKey("一袋半辣白菜牛肉面_今麦郎_一袋半");
        o236.setName("一袋半[辣白菜]");
        o236.setObjectId("5df45235501be400013bca95");
        o236.setColor("fce011");
        mockTable.add(o236);


        ObjectMapPo o237 = new ObjectMapPo();
        o237.setTenantId(670624);
        o237.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o237.setAppId("CRM");
        o237.setApiName("ProductObj");
        o237.setKey("一袋半重庆小面_今麦郎_一袋半");
        o237.setName("一袋半[重庆小面]");
        o237.setObjectId("5df45235501be400013bca91");
        o237.setColor("ec377f");
        mockTable.add(o237);


        ObjectMapPo o238 = new ObjectMapPo();
        o238.setTenantId(670624);
        o238.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o238.setAppId("CRM");
        o238.setApiName("ProductObj");
        o238.setKey("一袋半青花椒牛肉面_今麦郎_一袋半");
        o238.setName("一袋半[青花椒]");
        o238.setObjectId("5df45235501be400013bcaa1");
        o238.setColor("679363");
        mockTable.add(o238);


        ObjectMapPo o239 = new ObjectMapPo();
        o239.setTenantId(670624);
        o239.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o239.setAppId("CRM");
        o239.setApiName("ProductObj");
        o239.setKey("一袋半麻酱担担面_今麦郎_一袋半");
        o239.setName("一袋半[担担面]");
        o239.setObjectId("5df45235501be400013bca9d");
        o239.setColor("afdcd6");
        mockTable.add(o239);


        ObjectMapPo o240 = new ObjectMapPo();
        o240.setTenantId(670624);
        o240.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o240.setAppId("CRM");
        o240.setApiName("ProductObj");
        o240.setKey("一袋半海带排骨面_今麦郎_一袋半");
        o240.setName("一袋半[海带]");
        o240.setObjectId("5df45235501be400013bca99");
        o240.setColor("cc6044");
        mockTable.add(o240);


        ObjectMapPo o241 = new ObjectMapPo();
        o241.setTenantId(670624);
        o241.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o241.setAppId("CRM");
        o241.setApiName("ProductObj");
        o241.setKey("一袋半泡椒牛肉面_今麦郎_一袋半");
        o241.setName("一袋半[泡椒]");
        o241.setObjectId("5df45237d31f100001491c8e");
        o241.setColor("b75c25");
        mockTable.add(o241);


        ObjectMapPo o242 = new ObjectMapPo();
        o242.setTenantId(670624);
        o242.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o242.setAppId("CRM");
        o242.setApiName("ProductObj");
        o242.setKey("一袋半菌菇鸡汤面_今麦郎_一袋半");
        o242.setName("一袋半[鸡汤]");
        o242.setObjectId("5df45237d31f100001491c7c");
        o242.setColor("e05829");
        mockTable.add(o242);


        ObjectMapPo o243 = new ObjectMapPo();
        o243.setTenantId(670624);
        o243.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o243.setAppId("CRM");
        o243.setApiName("ProductObj");
        o243.setKey("一袋半清真油泼辣子酸汤面_今麦郎_一袋半");
        o243.setName("一袋半[清油泼辣子酸汤]");
        o243.setObjectId("5df45237d31f100001491c90");
        o243.setColor("20da9a");
        mockTable.add(o243);


        ObjectMapPo o244 = new ObjectMapPo();
        o244.setTenantId(670624);
        o244.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o244.setAppId("CRM");
        o244.setApiName("ProductObj");
        o244.setKey("一袋半酸辣牛肉面_今麦郎_一袋半");
        o244.setName("一袋半[酸辣]");
        o244.setObjectId("5df45237d31f100001491c94");
        o244.setColor("9bcdb5");
        mockTable.add(o244);


        ObjectMapPo o245 = new ObjectMapPo();
        o245.setTenantId(670624);
        o245.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o245.setAppId("CRM");
        o245.setApiName("ProductObj");
        o245.setKey("一袋半老坛酸菜牛肉面_今麦郎_一袋半");
        o245.setName("一袋半[老坛]");
        o245.setObjectId("5df45237d31f100001491c98");
        o245.setColor("063664");
        mockTable.add(o245);


        ObjectMapPo o246 = new ObjectMapPo();
        o246.setTenantId(670624);
        o246.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o246.setAppId("CRM");
        o246.setApiName("ProductObj");
        o246.setKey("一袋半鲜虾鱼板面_今麦郎_一袋半");
        o246.setName("一袋半[鲜虾]");
        o246.setObjectId("5df45237d31f100001491c9a");
        o246.setColor("c5bc9c");
        mockTable.add(o246);


        ObjectMapPo o247 = new ObjectMapPo();
        o247.setTenantId(670624);
        o247.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o247.setAppId("CRM");
        o247.setApiName("ProductObj");
        o247.setKey("一袋半西红柿鸡蛋打卤面_今麦郎_一袋半");
        o247.setName("一袋半[西红柿]");
        o247.setObjectId("5df45237d31f100001491c9e");
        o247.setColor("ce1654");
        mockTable.add(o247);


        ObjectMapPo o248 = new ObjectMapPo();
        o248.setTenantId(670624);
        o248.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o248.setAppId("CRM");
        o248.setApiName("ProductObj");
        o248.setKey("一袋半猪骨白汤面_今麦郎_一袋半");
        o248.setName("一袋半[猪骨]");
        o248.setObjectId("5df45237d31f100001491ca0");
        o248.setColor("2470a0");
        mockTable.add(o248);


        ObjectMapPo o249 = new ObjectMapPo();
        o249.setTenantId(670624);
        o249.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o249.setAppId("CRM");
        o249.setApiName("ProductObj");
        o249.setKey("一袋半香辣牛肉面_今麦郎_一袋半");
        o249.setName("一袋半[香辣]");
        o249.setObjectId("5df45237d31f100001491c80");
        o249.setColor("ffa92e");
        mockTable.add(o249);


        ObjectMapPo o250 = new ObjectMapPo();
        o250.setTenantId(670624);
        o250.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o250.setAppId("CRM");
        o250.setApiName("ProductObj");
        o250.setKey("一袋半辣椒炒肉面_今麦郎_一袋半");
        o250.setName("一袋半[辣椒炒肉]");
        o250.setObjectId("5df45237d31f100001491c82");
        o250.setColor("f96474");
        mockTable.add(o250);


        ObjectMapPo o251 = new ObjectMapPo();
        o251.setTenantId(670624);
        o251.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o251.setAppId("CRM");
        o251.setApiName("ProductObj");
        o251.setKey("一袋半麻辣牛肉面_今麦郎_一袋半");
        o251.setName("一袋半[麻牛]");
        o251.setObjectId("5df45237d31f100001491c84");
        o251.setColor("d9daf2");
        mockTable.add(o251);


        ObjectMapPo o252 = new ObjectMapPo();
        o252.setTenantId(670624);
        o252.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o252.setAppId("CRM");
        o252.setApiName("ProductObj");
        o252.setKey("一袋半小鸡炖蘑菇面_今麦郎_一袋半");
        o252.setName("一袋半[小鸡]");
        o252.setObjectId("5df45237d31f100001491c88");
        o252.setColor("715f5b");
        mockTable.add(o252);


        ObjectMapPo o253 = new ObjectMapPo();
        o253.setTenantId(670624);
        o253.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o253.setAppId("CRM");
        o253.setApiName("ProductObj");
        o253.setKey("一袋半麻辣排骨面_今麦郎_一袋半");
        o253.setName("一袋半[麻排]");
        o253.setObjectId("5df45237d31f100001491c86");
        o253.setColor("5d1677");
        mockTable.add(o253);


        ObjectMapPo o254 = new ObjectMapPo();
        o254.setTenantId(670624);
        o254.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o254.setAppId("CRM");
        o254.setApiName("ProductObj");
        o254.setKey("一袋半红烧牛肉面_今麦郎_一袋半");
        o254.setName("一袋半[红牛]");
        o254.setObjectId("5df45237d31f100001491c8a");
        o254.setColor("d25565");
        mockTable.add(o254);


        ObjectMapPo o255 = new ObjectMapPo();
        o255.setTenantId(670624);
        o255.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o255.setAppId("CRM");
        o255.setApiName("ProductObj");
        o255.setKey("一袋半葱香排骨面_今麦郎_一袋半");
        o255.setName("一袋半[葱排]");
        o255.setObjectId("5df45237d31f100001491c8c");
        o255.setColor("c91f25");
        mockTable.add(o255);


        ObjectMapPo o256 = new ObjectMapPo();
        o256.setTenantId(670624);
        o256.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o256.setAppId("CRM");
        o256.setApiName("ProductObj");
        o256.setKey("一桶半香辣牛肉面_今麦郎_一桶半");
        o256.setName("一桶半[香辣]");
        o256.setObjectId("5df45236d31f100001491b2f");
        o256.setColor("43d9fd");
        mockTable.add(o256);


        ObjectMapPo o257 = new ObjectMapPo();
        o257.setTenantId(670624);
        o257.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o257.setAppId("CRM");
        o257.setApiName("ProductObj");
        o257.setKey("一桶半红油爆椒牛肉面_今麦郎_一桶半");
        o257.setName("一桶半[红油]");
        o257.setObjectId("5df45236d31f100001491b1b");
        o257.setColor("59c2c4");
        mockTable.add(o257);


        ObjectMapPo o258 = new ObjectMapPo();
        o258.setTenantId(670624);
        o258.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o258.setAppId("CRM");
        o258.setApiName("ProductObj");
        o258.setKey("一桶半辣白菜牛肉面_今麦郎_一桶半");
        o258.setName("一桶半[辣白菜]");
        o258.setObjectId("5df45236d31f100001491b1f");
        o258.setColor("4f658a");
        mockTable.add(o258);


        ObjectMapPo o259 = new ObjectMapPo();
        o259.setTenantId(670624);
        o259.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o259.setAppId("CRM");
        o259.setApiName("ProductObj");
        o259.setKey("一桶半重庆小面_今麦郎_一桶半");
        o259.setName("一桶半[重庆小面]");
        o259.setObjectId("5df45236d31f100001491b21");
        o259.setColor("98185e");
        mockTable.add(o259);


        ObjectMapPo o260 = new ObjectMapPo();
        o260.setTenantId(670624);
        o260.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o260.setAppId("CRM");
        o260.setApiName("ProductObj");
        o260.setKey("一桶半麻酱担担面_今麦郎_一桶半");
        o260.setName("一桶半[担担面]");
        o260.setObjectId("5df45236d31f100001491b23");
        o260.setColor("18979f");
        mockTable.add(o260);


        ObjectMapPo o261 = new ObjectMapPo();
        o261.setTenantId(670624);
        o261.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o261.setAppId("CRM");
        o261.setApiName("ProductObj");
        o261.setKey("一桶半酸豆角排骨面_今麦郎_一桶半");
        o261.setName("一桶半[酸豆角]");
        o261.setObjectId("5df45236d31f100001491b25");
        o261.setColor("508fb3");
        mockTable.add(o261);


        ObjectMapPo o262 = new ObjectMapPo();
        o262.setTenantId(670624);
        o262.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o262.setAppId("CRM");
        o262.setApiName("ProductObj");
        o262.setKey("一桶半红烧牛肉面_今麦郎_一桶半");
        o262.setName("一桶半[红牛]");
        o262.setObjectId("5df45236d31f100001491b29");
        o262.setColor("f92a82");
        mockTable.add(o262);


        ObjectMapPo o263 = new ObjectMapPo();
        o263.setTenantId(670624);
        o263.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o263.setAppId("CRM");
        o263.setApiName("ProductObj");
        o263.setKey("一桶半青花椒牛肉面_今麦郎_一桶半");
        o263.setName("一桶半[青花椒]");
        o263.setObjectId("5df45236d31f100001491b2b");
        o263.setColor("edb67c");
        mockTable.add(o263);


        ObjectMapPo o264 = new ObjectMapPo();
        o264.setTenantId(670624);
        o264.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o264.setAppId("CRM");
        o264.setApiName("ProductObj");
        o264.setKey("一桶半辣椒炒肉面_今麦郎_一桶半");
        o264.setName("一桶半[辣椒炒肉]");
        o264.setObjectId("5df45236d31f100001491b31");
        o264.setColor("f8061d");
        mockTable.add(o264);


        ObjectMapPo o265 = new ObjectMapPo();
        o265.setTenantId(670624);
        o265.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o265.setAppId("CRM");
        o265.setApiName("ProductObj");
        o265.setKey("一桶半小鸡炖蘑菇面_今麦郎_一桶半");
        o265.setName("一桶半[小鸡]");
        o265.setObjectId("5df45236d31f100001491b33");
        o265.setColor("fc8e1a");
        mockTable.add(o265);


        ObjectMapPo o266 = new ObjectMapPo();
        o266.setTenantId(670624);
        o266.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o266.setAppId("CRM");
        o266.setApiName("ProductObj");
        o266.setKey("一桶半麻辣牛肉面_今麦郎_一桶半");
        o266.setName("一桶半[麻牛]");
        o266.setObjectId("5df45236d31f100001491b35");
        o266.setColor("9385ec");
        mockTable.add(o266);


        ObjectMapPo o267 = new ObjectMapPo();
        o267.setTenantId(670624);
        o267.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o267.setAppId("CRM");
        o267.setApiName("ProductObj");
        o267.setKey("一桶半麻辣排骨面_今麦郎_一桶半");
        o267.setName("一桶半[麻排]");
        o267.setObjectId("5df45236d31f100001491b37");
        o267.setColor("b9cf44");
        mockTable.add(o267);


        ObjectMapPo o268 = new ObjectMapPo();
        o268.setTenantId(670624);
        o268.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o268.setAppId("CRM");
        o268.setApiName("ProductObj");
        o268.setKey("一桶半葱香排骨面_今麦郎_一桶半");
        o268.setName("一桶半[葱排]");
        o268.setObjectId("5df45236d31f100001491b39");
        o268.setColor("244b67");
        mockTable.add(o268);


        ObjectMapPo o269 = new ObjectMapPo();
        o269.setTenantId(670624);
        o269.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o269.setAppId("CRM");
        o269.setApiName("ProductObj");
        o269.setKey("一桶半泡椒牛肉面_今麦郎_一桶半");
        o269.setName("一桶半[泡椒]");
        o269.setObjectId("5df45236d31f100001491b3b");
        o269.setColor("90fee7");
        mockTable.add(o269);


        ObjectMapPo o270 = new ObjectMapPo();
        o270.setTenantId(670624);
        o270.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o270.setAppId("CRM");
        o270.setApiName("ProductObj");
        o270.setKey("一桶半鲜虾鱼板面_今麦郎_一桶半");
        o270.setName("一桶半[鲜虾]");
        o270.setObjectId("5df45238f6ee6f000162508e");
        o270.setColor("f97576");
        mockTable.add(o270);


        ObjectMapPo o271 = new ObjectMapPo();
        o271.setTenantId(670624);
        o271.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o271.setAppId("CRM");
        o271.setApiName("ProductObj");
        o271.setKey("一桶半清真油泼辣子酸汤面_今麦郎_一桶半");
        o271.setName("一桶半[清油泼辣子酸汤]");
        o271.setObjectId("5df45238f6ee6f0001625092");
        o271.setColor("99b8c1");
        mockTable.add(o271);


        ObjectMapPo o272 = new ObjectMapPo();
        o272.setTenantId(670624);
        o272.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o272.setAppId("CRM");
        o272.setApiName("ProductObj");
        o272.setKey("一桶半酸辣牛肉面_今麦郎_一桶半");
        o272.setName("一桶半[酸辣]");
        o272.setObjectId("5df45238f6ee6f0001625088");
        o272.setColor("b17737");
        mockTable.add(o272);


        ObjectMapPo o273 = new ObjectMapPo();
        o273.setTenantId(670624);
        o273.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o273.setAppId("CRM");
        o273.setApiName("ProductObj");
        o273.setKey("一桶半老坛酸菜牛肉面_今麦郎_一桶半");
        o273.setName("一桶半[老坛]");
        o273.setObjectId("5df45238f6ee6f000162508a");
        o273.setColor("f6acff");
        mockTable.add(o273);


        ObjectMapPo o274 = new ObjectMapPo();
        o274.setTenantId(670624);
        o274.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o274.setAppId("CRM");
        o274.setApiName("ProductObj");
        o274.setKey("一桶半西红柿鸡蛋打卤面_今麦郎_一桶半");
        o274.setName("一桶半[西红柿]");
        o274.setObjectId("5df45238f6ee6f000162508c");
        o274.setColor("beded6");
        mockTable.add(o274);


        ObjectMapPo o275 = new ObjectMapPo();
        o275.setTenantId(670624);
        o275.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o275.setAppId("CRM");
        o275.setApiName("ProductObj");
        o275.setKey("一桶半猪骨白汤面_今麦郎_一桶半");
        o275.setName("一桶半[猪骨]");
        o275.setObjectId("5df45238f6ee6f0001625094");
        o275.setColor("7fbfd9");
        mockTable.add(o275);


        ObjectMapPo o276 = new ObjectMapPo();
        o276.setTenantId(670624);
        o276.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o276.setAppId("CRM");
        o276.setApiName("ProductObj");
        o276.setKey("一桶半清真鲜虾鱼板面_今麦郎_一桶半");
        o276.setName("一桶半[清鲜虾]");
        o276.setObjectId("5df45238f6ee6f0001625090");
        o276.setColor("3a9367");
        mockTable.add(o276);


        ObjectMapPo o277 = new ObjectMapPo();
        o277.setTenantId(670624);
        o277.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o277.setAppId("CRM");
        o277.setApiName("ProductObj");
        o277.setKey("一桶半清真麻辣牛肉面_今麦郎_一桶半");
        o277.setName("一桶半[清麻牛]");
        o277.setObjectId("5df45238f6ee6f0001625086");
        o277.setColor("033a95");
        mockTable.add(o277);


        ObjectMapPo o278 = new ObjectMapPo();
        o278.setTenantId(670624);
        o278.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o278.setAppId("CRM");
        o278.setApiName("ProductObj");
        o278.setKey("一桶半清真红烧牛肉面_今麦郎_一桶半");
        o278.setName("一桶半[清红牛]");
        o278.setObjectId("5df45238f6ee6f0001625084");
        o278.setColor("9b39ec");
        mockTable.add(o278);


        ObjectMapPo o279 = new ObjectMapPo();
        o279.setTenantId(670624);
        o279.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o279.setAppId("CRM");
        o279.setApiName("ProductObj");
        o279.setKey("一袋半清真老坛酸菜牛肉面_今麦郎_一袋半");
        o279.setName("一袋半[清老坛]");
        o279.setObjectId("5df45237d31f100001491ca6");
        o279.setColor("1f83ff");
        mockTable.add(o279);


        ObjectMapPo o280 = new ObjectMapPo();
        o280.setTenantId(670624);
        o280.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o280.setAppId("CRM");
        o280.setApiName("ProductObj");
        o280.setKey("一袋半清真红烧牛肉面_今麦郎_一袋半");
        o280.setName("一袋半[清红牛]");
        o280.setObjectId("5df45237d31f100001491ca4");
        o280.setColor("d2a6ce");
        mockTable.add(o280);


        ObjectMapPo o281 = new ObjectMapPo();
        o281.setTenantId(670624);
        o281.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o281.setAppId("CRM");
        o281.setApiName("ProductObj");
        o281.setKey("一袋半清真香辣牛肉面_今麦郎_一袋半");
        o281.setName("一袋半[清香辣]");
        o281.setObjectId("5df45237d31f100001491ca2");
        o281.setColor("d24c4f");
        mockTable.add(o281);


        ObjectMapPo o282 = new ObjectMapPo();
        o282.setTenantId(670624);
        o282.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o282.setAppId("CRM");
        o282.setApiName("ProductObj");
        o282.setKey("一袋半清真鲜虾鱼板面_今麦郎_一袋半");
        o282.setName("一袋半[清鲜虾]");
        o282.setObjectId("5df45237d31f100001491c9c");
        o282.setColor("e0c29d");
        mockTable.add(o282);


        ObjectMapPo o283 = new ObjectMapPo();
        o283.setTenantId(670624);
        o283.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o283.setAppId("CRM");
        o283.setApiName("ProductObj");
        o283.setKey("一袋半清真酸辣牛肉面_今麦郎_一袋半");
        o283.setName("一袋半[清酸辣]");
        o283.setObjectId("5df45237d31f100001491c96");
        o283.setColor("259bc9");
        mockTable.add(o283);


        ObjectMapPo o284 = new ObjectMapPo();
        o284.setTenantId(670624);
        o284.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o284.setAppId("CRM");
        o284.setApiName("ProductObj");
        o284.setKey("一袋半清真麻辣牛肉面_今麦郎_一袋半");
        o284.setName("一袋半[清麻牛]");
        o284.setObjectId("5df45237d31f100001491c92");
        o284.setColor("86c12d");
        mockTable.add(o284);


        ObjectMapPo o285 = new ObjectMapPo();
        o285.setTenantId(670624);
        o285.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o285.setAppId("CRM");
        o285.setApiName("ProductObj");
        o285.setKey("一袋半清真菌菇鸡汤面_今麦郎_一袋半");
        o285.setName("一袋半[清鸡汤]");
        o285.setObjectId("5df45237d31f100001491c7e");
        o285.setColor("6da9ff");
        mockTable.add(o285);


        ObjectMapPo o286 = new ObjectMapPo();
        o286.setTenantId(670624);
        o286.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o286.setAppId("CRM");
        o286.setApiName("ProductObj");
        o286.setKey("一桶半清真酸豆角牛肉面_今麦郎_一桶半");
        o286.setName("一桶半[清酸豆角]");
        o286.setObjectId("5df45236d31f100001491b27");
        o286.setColor("5f54ce");
        mockTable.add(o286);


        ObjectMapPo o287 = new ObjectMapPo();
        o287.setTenantId(670624);
        o287.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o287.setAppId("CRM");
        o287.setApiName("ProductObj");
        o287.setKey("一桶半清真老坛酸菜面_今麦郎_一桶半");
        o287.setName("一桶半[清老坛]");
        o287.setObjectId("5df45236d31f100001491b1d");
        o287.setColor("bacd37");
        mockTable.add(o287);


        ObjectMapPo o288 = new ObjectMapPo();
        o288.setTenantId(670624);
        o288.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o288.setAppId("CRM");
        o288.setApiName("ProductObj");
        o288.setKey("一桶半清真香辣牛肉面_今麦郎_一桶半");
        o288.setName("一桶半[清香辣]");
        o288.setObjectId("5df45236d31f100001491b19");
        o288.setColor("f8decb");
        mockTable.add(o288);


        ObjectMapPo o289 = new ObjectMapPo();
        o289.setTenantId(670624);
        o289.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o289.setAppId("CRM");
        o289.setApiName("ProductObj");
        o289.setKey("一袋半清真麻酱担担面_今麦郎_一袋半");
        o289.setName("一袋半[清麻酱担担]");
        o289.setObjectId("5df45235501be400013bca9f");
        o289.setColor("70cefc");
        mockTable.add(o289);


        ObjectMapPo o290 = new ObjectMapPo();
        o290.setTenantId(670624);
        o290.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o290.setAppId("CRM");
        o290.setApiName("ProductObj");
        o290.setKey("一袋半清真酸豆角牛肉面_今麦郎_一袋半");
        o290.setName("一袋半[清酸豆角]");
        o290.setObjectId("5df45235501be400013bca9b");
        o290.setColor("f2b6c2");
        mockTable.add(o290);


        ObjectMapPo o291 = new ObjectMapPo();
        o291.setTenantId(670624);
        o291.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        o291.setAppId("CRM");
        o291.setApiName("ProductObj");
        o291.setKey("一袋半清真重庆小面_今麦郎_一袋半");
        o291.setName("一袋半[清重庆小面]");
        o291.setObjectId("5df45235501be400013bca93");
        o291.setColor("ff74cc");
        mockTable.add(o291);



        ObjectMapPo oo118 = new ObjectMapPo();
        oo118.setTenantId(71578);
        oo118.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo118.setAppId("CRM");
        oo118.setApiName("ProductObj");
        oo118.setKey("一袋半辣椒炒肉面_今麦郎_一袋半");
        oo118.setName("一袋半辣椒炒肉面");
        oo118.setObjectId("5d907bdbddf9fa0001ba6640");
        oo118.setColor("3dd4c4");
        mockTable.add(oo118);

        ObjectMapPo oo119 = new ObjectMapPo();
        oo119.setTenantId(71578);
        oo119.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo119.setAppId("CRM");
        oo119.setApiName("ProductObj");
        oo119.setKey("一袋半清真老坛酸菜牛肉面_今麦郎_一袋半");
        oo119.setName("一袋半清真老坛酸菜牛肉面");
        oo119.setObjectId("5d907bdbddf9fa0001ba663e");
        oo119.setColor("3f3f98");
        mockTable.add(oo119);

        ObjectMapPo oo120 = new ObjectMapPo();
        oo120.setTenantId(71578);
        oo120.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo120.setAppId("CRM");
        oo120.setApiName("ProductObj");
        oo120.setKey("一袋半清真香辣牛肉面_今麦郎_一袋半");
        oo120.setName("一袋半清真香辣牛肉面");
        oo120.setObjectId("5d907bdbddf9fa0001ba663c");
        oo120.setColor("fce011");
        mockTable.add(oo120);

        ObjectMapPo oo121 = new ObjectMapPo();
        oo121.setTenantId(71578);
        oo121.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo121.setAppId("CRM");
        oo121.setApiName("ProductObj");
        oo121.setKey("一袋半清真重庆小面_今麦郎_一袋半");
        oo121.setName("一袋半清真重庆小面");
        oo121.setObjectId("5d907bdbddf9fa0001ba663a");
        oo121.setColor("ec377f");
        mockTable.add(oo121);

        ObjectMapPo oo122 = new ObjectMapPo();
        oo122.setTenantId(71578);
        oo122.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo122.setAppId("CRM");
        oo122.setApiName("ProductObj");
        oo122.setKey("一袋半辣白菜牛肉面_今麦郎_一袋半");
        oo122.setName("一袋半辣白菜牛肉面");
        oo122.setObjectId("5d907bdbddf9fa0001ba6638");
        oo122.setColor("679363");
        mockTable.add(oo122);

        ObjectMapPo oo123 = new ObjectMapPo();
        oo123.setTenantId(71578);
        oo123.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo123.setAppId("CRM");
        oo123.setApiName("ProductObj");
        oo123.setKey("一袋半清真麻辣牛肉面_今麦郎_一袋半");
        oo123.setName("一袋半清真麻辣牛肉面");
        oo123.setObjectId("5d907bdbddf9fa0001ba6636");
        oo123.setColor("afdcd6");
        mockTable.add(oo123);

        ObjectMapPo oo124 = new ObjectMapPo();
        oo124.setTenantId(71578);
        oo124.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo124.setAppId("CRM");
        oo124.setApiName("ProductObj");
        oo124.setKey("一袋半清真酸豆角牛肉面_今麦郎_一袋半");
        oo124.setName("一袋半清真酸豆角牛肉面");
        oo124.setObjectId("5d907bdbddf9fa0001ba6634");
        oo124.setColor("cc6044");
        mockTable.add(oo124);

        ObjectMapPo oo125 = new ObjectMapPo();
        oo125.setTenantId(71578);
        oo125.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo125.setAppId("CRM");
        oo125.setApiName("ProductObj");
        oo125.setKey("一袋半清真鲜虾鱼板面_今麦郎_一袋半");
        oo125.setName("一袋半清真鲜虾鱼板面");
        oo125.setObjectId("5d907bdbddf9fa0001ba6632");
        oo125.setColor("b75c25");
        mockTable.add(oo125);

        ObjectMapPo oo126 = new ObjectMapPo();
        oo126.setTenantId(71578);
        oo126.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo126.setAppId("CRM");
        oo126.setApiName("ProductObj");
        oo126.setKey("一袋半清真菌菇鸡汤面_今麦郎_一袋半");
        oo126.setName("一袋半清真菌菇鸡汤面");
        oo126.setObjectId("5d907bdbddf9fa0001ba6630");
        oo126.setColor("e05829");
        mockTable.add(oo126);

        ObjectMapPo oo127 = new ObjectMapPo();
        oo127.setTenantId(71578);
        oo127.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo127.setAppId("CRM");
        oo127.setApiName("ProductObj");
        oo127.setKey("一袋半清真酸辣牛肉面_今麦郎_一袋半");
        oo127.setName("一袋半清真酸辣牛肉面");
        oo127.setObjectId("5d907bdbddf9fa0001ba662e");
        oo127.setColor("20da9a");
        mockTable.add(oo127);

        ObjectMapPo oo128 = new ObjectMapPo();
        oo128.setTenantId(71578);
        oo128.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo128.setAppId("CRM");
        oo128.setApiName("ProductObj");
        oo128.setKey("一袋半清真麻酱担担面_今麦郎_一袋半");
        oo128.setName("一袋半清真麻酱担担面");
        oo128.setObjectId("5d907bdbddf9fa0001ba662c");
        oo128.setColor("9bcdb5");
        mockTable.add(oo128);

        ObjectMapPo oo129 = new ObjectMapPo();
        oo129.setTenantId(71578);
        oo129.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo129.setAppId("CRM");
        oo129.setApiName("ProductObj");
        oo129.setKey("一袋半清真红烧牛肉面_今麦郎_一袋半");
        oo129.setName("一袋半清真红烧牛肉面");
        oo129.setObjectId("5d907bdbddf9fa0001ba662a");
        oo129.setColor("063664");
        mockTable.add(oo129);

        ObjectMapPo oo130 = new ObjectMapPo();
        oo130.setTenantId(71578);
        oo130.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo130.setAppId("CRM");
        oo130.setApiName("ProductObj");
        oo130.setKey("一袋半青花椒牛肉面_今麦郎_一袋半");
        oo130.setName("一袋半青花椒牛肉面");
        oo130.setObjectId("5cedd6fb426b790001edfee5");
        oo130.setColor("c5bc9c");
        mockTable.add(oo130);

        ObjectMapPo oo131 = new ObjectMapPo();
        oo131.setTenantId(71578);
        oo131.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo131.setAppId("CRM");
        oo131.setApiName("ProductObj");
        oo131.setKey("一袋半西红柿鸡蛋打卤面_今麦郎_一袋半");
        oo131.setName("一袋半西红柿鸡蛋打卤面");
        oo131.setObjectId("5cedd6fb426b790001edfee3");
        oo131.setColor("ce1654");
        mockTable.add(oo131);

        ObjectMapPo oo132 = new ObjectMapPo();
        oo132.setTenantId(71578);
        oo132.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo132.setAppId("CRM");
        oo132.setApiName("ProductObj");
        oo132.setKey("一袋半小鸡炖蘑菇面_今麦郎_一袋半");
        oo132.setName("一袋半小鸡炖蘑菇面");
        oo132.setObjectId("5cedd6fb426b790001edfee1");
        oo132.setColor("2470a0");
        mockTable.add(oo132);

        ObjectMapPo oo133 = new ObjectMapPo();
        oo133.setTenantId(71578);
        oo133.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo133.setAppId("CRM");
        oo133.setApiName("ProductObj");
        oo133.setKey("一袋半麻酱担担面_今麦郎_一袋半");
        oo133.setName("一袋半麻酱担担面");
        oo133.setObjectId("5cedd6fb426b790001edfedf");
        oo133.setColor("ffa92e");
        mockTable.add(oo133);

        ObjectMapPo oo134 = new ObjectMapPo();
        oo134.setTenantId(71578);
        oo134.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo134.setAppId("CRM");
        oo134.setApiName("ProductObj");
        oo134.setKey("一袋半菌菇鸡汤面_今麦郎_一袋半");
        oo134.setName("一袋半菌菇鸡汤面");
        oo134.setObjectId("5cedd6fb426b790001edfedd");
        oo134.setColor("f96474");
        mockTable.add(oo134);

        ObjectMapPo oo135 = new ObjectMapPo();
        oo135.setTenantId(71578);
        oo135.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo135.setAppId("CRM");
        oo135.setApiName("ProductObj");
        oo135.setKey("一袋半猪骨白汤面_今麦郎_一袋半");
        oo135.setName("一袋半猪骨白汤面");
        oo135.setObjectId("5cedd6fb426b790001edfedb");
        oo135.setColor("d9daf2");
        mockTable.add(oo135);

        ObjectMapPo oo136 = new ObjectMapPo();
        oo136.setTenantId(71578);
        oo136.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo136.setAppId("CRM");
        oo136.setApiName("ProductObj");
        oo136.setKey("一袋半重庆小面_今麦郎_一袋半");
        oo136.setName("一袋半重庆小面");
        oo136.setObjectId("5cedd6fb426b790001edfed9");
        oo136.setColor("715f5b");
        mockTable.add(oo136);

        ObjectMapPo oo137 = new ObjectMapPo();
        oo137.setTenantId(71578);
        oo137.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo137.setAppId("CRM");
        oo137.setApiName("ProductObj");
        oo137.setKey("一袋半海带排骨面_今麦郎_一袋半");
        oo137.setName("一袋半海带排骨面");
        oo137.setObjectId("5cedd6fb426b790001edfed7");
        oo137.setColor("5d1677");
        mockTable.add(oo137);

        ObjectMapPo oo138 = new ObjectMapPo();
        oo138.setTenantId(71578);
        oo138.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo138.setAppId("CRM");
        oo138.setApiName("ProductObj");
        oo138.setKey("一袋半鲜虾鱼板面_今麦郎_一袋半");
        oo138.setName("一袋半鲜虾鱼板面");
        oo138.setObjectId("5cedd6fb426b790001edfed5");
        oo138.setColor("d25565");
        mockTable.add(oo138);

        ObjectMapPo oo139 = new ObjectMapPo();
        oo139.setTenantId(71578);
        oo139.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo139.setAppId("CRM");
        oo139.setApiName("ProductObj");
        oo139.setKey("一袋半泡椒牛肉面_今麦郎_一袋半");
        oo139.setName("一袋半泡椒牛肉面");
        oo139.setObjectId("5cedd6fb426b790001edfed3");
        oo139.setColor("c91f25");
        mockTable.add(oo139);

        ObjectMapPo oo140 = new ObjectMapPo();
        oo140.setTenantId(71578);
        oo140.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo140.setAppId("CRM");
        oo140.setApiName("ProductObj");
        oo140.setKey("一袋半酸豆角排骨面_今麦郎_一袋半");
        oo140.setName("一袋半酸豆角排骨面");
        oo140.setObjectId("5cedd6fb426b790001edfed1");
        oo140.setColor("43d9fd");
        mockTable.add(oo140);

        ObjectMapPo oo141 = new ObjectMapPo();
        oo141.setTenantId(71578);
        oo141.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo141.setAppId("CRM");
        oo141.setApiName("ProductObj");
        oo141.setKey("一袋半麻辣牛肉面_今麦郎_一袋半");
        oo141.setName("一袋半麻辣牛肉面");
        oo141.setObjectId("5cedd6fb426b790001edfecf");
        oo141.setColor("59c2c4");
        mockTable.add(oo141);

        ObjectMapPo oo142 = new ObjectMapPo();
        oo142.setTenantId(71578);
        oo142.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo142.setAppId("CRM");
        oo142.setApiName("ProductObj");
        oo142.setKey("一袋半红油爆椒牛肉面_今麦郎_一袋半");
        oo142.setName("一袋半红油爆椒牛肉面");
        oo142.setObjectId("5cedd6fb426b790001edfecd");
        oo142.setColor("4f658a");
        mockTable.add(oo142);

        ObjectMapPo oo143 = new ObjectMapPo();
        oo143.setTenantId(71578);
        oo143.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo143.setAppId("CRM");
        oo143.setApiName("ProductObj");
        oo143.setKey("一袋半麻辣排骨面_今麦郎_一袋半");
        oo143.setName("一袋半麻辣排骨面");
        oo143.setObjectId("5cedd6fb426b790001edfecb");
        oo143.setColor("98185e");
        mockTable.add(oo143);

        ObjectMapPo oo144 = new ObjectMapPo();
        oo144.setTenantId(71578);
        oo144.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo144.setAppId("CRM");
        oo144.setApiName("ProductObj");
        oo144.setKey("一袋半清真油泼辣子酸汤面_今麦郎_一袋半");
        oo144.setName("一袋半清真油泼辣子酸汤面");
        oo144.setObjectId("5cedd6fb426b790001edfec9");
        oo144.setColor("18979f");
        mockTable.add(oo144);

        ObjectMapPo oo145 = new ObjectMapPo();
        oo145.setTenantId(71578);
        oo145.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo145.setAppId("CRM");
        oo145.setApiName("ProductObj");
        oo145.setKey("一袋半酸辣牛肉面_今麦郎_一袋半");
        oo145.setName("一袋半酸辣牛肉面");
        oo145.setObjectId("5cedd6fb426b790001edfec7");
        oo145.setColor("508fb3");
        mockTable.add(oo145);

        ObjectMapPo oo146 = new ObjectMapPo();
        oo146.setTenantId(71578);
        oo146.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo146.setAppId("CRM");
        oo146.setApiName("ProductObj");
        oo146.setKey("一袋半老坛酸菜牛肉面_今麦郎_一袋半");
        oo146.setName("一袋半老坛酸菜牛肉面");
        oo146.setObjectId("5cedd6fb426b790001edfec5");
        oo146.setColor("f92a82");
        mockTable.add(oo146);

        ObjectMapPo oo147 = new ObjectMapPo();
        oo147.setTenantId(71578);
        oo147.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo147.setAppId("CRM");
        oo147.setApiName("ProductObj");
        oo147.setKey("一袋半香辣牛肉面_今麦郎_一袋半");
        oo147.setName("一袋半香辣牛肉面");
        oo147.setObjectId("5cedd6fb426b790001edfec3");
        oo147.setColor("edb67c");
        mockTable.add(oo147);

        ObjectMapPo oo148 = new ObjectMapPo();
        oo148.setTenantId(71578);
        oo148.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo148.setAppId("CRM");
        oo148.setApiName("ProductObj");
        oo148.setKey("一袋半葱香排骨面_今麦郎_一袋半");
        oo148.setName("一袋半葱香排骨面");
        oo148.setObjectId("5cedd6fb426b790001edfec1");
        oo148.setColor("f8061d");
        mockTable.add(oo148);

        ObjectMapPo oo149 = new ObjectMapPo();
        oo149.setTenantId(71578);
        oo149.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo149.setAppId("CRM");
        oo149.setApiName("ProductObj");
        oo149.setKey("一袋半红烧牛肉面_今麦郎_一袋半");
        oo149.setName("一袋半红烧牛肉面");
        oo149.setObjectId("5cedd6fb426b790001edfebf");
        oo149.setColor("fc8e1a");
        mockTable.add(oo149);

        ObjectMapPo oo150 = new ObjectMapPo();
        oo150.setTenantId(71578);
        oo150.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo150.setAppId("CRM");
        oo150.setApiName("ProductObj");
        oo150.setKey("一桶半清真红烧牛肉面_今麦郎_一桶半");
        oo150.setName("一桶半清真红烧牛肉面");
        oo150.setObjectId("5d907c7ce91eb500011939be");
        oo150.setColor("9385ec");
        mockTable.add(oo150);

        ObjectMapPo oo151 = new ObjectMapPo();
        oo151.setTenantId(71578);
        oo151.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo151.setAppId("CRM");
        oo151.setApiName("ProductObj");
        oo151.setKey("一桶半清真鲜虾鱼板面_今麦郎_一桶半");
        oo151.setName("一桶半清真鲜虾鱼板面");
        oo151.setObjectId("5d907c7ce91eb500011939bc");
        oo151.setColor("b9cf44");
        mockTable.add(oo151);

        ObjectMapPo oo152 = new ObjectMapPo();
        oo152.setTenantId(71578);
        oo152.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo152.setAppId("CRM");
        oo152.setApiName("ProductObj");
        oo152.setKey("一桶半猪骨白汤面_今麦郎_一桶半");
        oo152.setName("一桶半猪骨白汤面");
        oo152.setObjectId("5d907c7ce91eb500011939ba");
        oo152.setColor("244b67");
        mockTable.add(oo152);

        ObjectMapPo oo153 = new ObjectMapPo();
        oo153.setTenantId(71578);
        oo153.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo153.setAppId("CRM");
        oo153.setApiName("ProductObj");
        oo153.setKey("一桶半辣白菜牛肉面_今麦郎_一桶半");
        oo153.setName("一桶半辣白菜牛肉面");
        oo153.setObjectId("5d907c7ce91eb500011939b8");
        oo153.setColor("90fee7");
        mockTable.add(oo153);

        ObjectMapPo oo154 = new ObjectMapPo();
        oo154.setTenantId(71578);
        oo154.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo154.setAppId("CRM");
        oo154.setApiName("ProductObj");
        oo154.setKey("一桶半清真老坛酸菜面_今麦郎_一桶半");
        oo154.setName("一桶半清真老坛酸菜面");
        oo154.setObjectId("5d907c7ce91eb500011939b6");
        oo154.setColor("f97576");
        mockTable.add(oo154);

        ObjectMapPo oo155 = new ObjectMapPo();
        oo155.setTenantId(71578);
        oo155.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo155.setAppId("CRM");
        oo155.setApiName("ProductObj");
        oo155.setKey("一桶半辣椒炒肉面_今麦郎_一桶半");
        oo155.setName("一桶半辣椒炒肉面");
        oo155.setObjectId("5d907c7ce91eb500011939b4");
        oo155.setColor("99b8c1");
        mockTable.add(oo155);

        ObjectMapPo oo156 = new ObjectMapPo();
        oo156.setTenantId(71578);
        oo156.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo156.setAppId("CRM");
        oo156.setApiName("ProductObj");
        oo156.setKey("一桶半清真麻辣牛肉面_今麦郎_一桶半");
        oo156.setName("一桶半清真麻辣牛肉面");
        oo156.setObjectId("5d907c7ce91eb500011939b2");
        oo156.setColor("b17737");
        mockTable.add(oo156);

        ObjectMapPo oo157 = new ObjectMapPo();
        oo157.setTenantId(71578);
        oo157.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo157.setAppId("CRM");
        oo157.setApiName("ProductObj");
        oo157.setKey("一桶半青花椒牛肉面_今麦郎_一桶半");
        oo157.setName("一桶半青花椒牛肉面");
        oo157.setObjectId("5d907c7ce91eb500011939b0");
        oo157.setColor("f6acff");
        mockTable.add(oo157);

        ObjectMapPo oo158 = new ObjectMapPo();
        oo158.setTenantId(71578);
        oo158.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo158.setAppId("CRM");
        oo158.setApiName("ProductObj");
        oo158.setKey("一桶半清真香辣牛肉面_今麦郎_一桶半");
        oo158.setName("一桶半清真香辣牛肉面");
        oo158.setObjectId("5d907c7ce91eb500011939ae");
        oo158.setColor("beded6");
        mockTable.add(oo158);

        ObjectMapPo oo159 = new ObjectMapPo();
        oo159.setTenantId(71578);
        oo159.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo159.setAppId("CRM");
        oo159.setApiName("ProductObj");
        oo159.setKey("一桶半清真酸豆角牛肉面_今麦郎_一桶半");
        oo159.setName("一桶半清真酸豆角牛肉面");
        oo159.setObjectId("5d907c7ce91eb500011939ac");
        oo159.setColor("7fbfd9");
        mockTable.add(oo159);

        ObjectMapPo oo160 = new ObjectMapPo();
        oo160.setTenantId(71578);
        oo160.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo160.setAppId("CRM");
        oo160.setApiName("ProductObj");
        oo160.setKey("一桶半麻酱担担面_今麦郎_一桶半");
        oo160.setName("一桶半麻酱担担面");
        oo160.setObjectId("5cedf6114869af00017793ac");
        oo160.setColor("3a9367");
        mockTable.add(oo160);

        ObjectMapPo oo161 = new ObjectMapPo();
        oo161.setTenantId(71578);
        oo161.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo161.setAppId("CRM");
        oo161.setApiName("ProductObj");
        oo161.setKey("一桶半西红柿鸡蛋打卤面_今麦郎_一桶半");
        oo161.setName("一桶半西红柿鸡蛋打卤面");
        oo161.setObjectId("5cedd6fd7b30990001a56871");
        oo161.setColor("033a95");
        mockTable.add(oo161);

        ObjectMapPo oo162 = new ObjectMapPo();
        oo162.setTenantId(71578);
        oo162.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo162.setAppId("CRM");
        oo162.setApiName("ProductObj");
        oo162.setKey("一桶半小鸡炖蘑菇面_今麦郎_一桶半");
        oo162.setName("一桶半小鸡炖蘑菇面");
        oo162.setObjectId("5dd3d09dc2b9870001a06725");
        oo162.setColor("9b39ec");
        mockTable.add(oo162);

        ObjectMapPo oo163 = new ObjectMapPo();
        oo163.setTenantId(71578);
        oo163.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo163.setAppId("CRM");
        oo163.setApiName("ProductObj");
        oo163.setKey("一桶半重庆小面_今麦郎_一桶半");
        oo163.setName("一桶半重庆小面");
        oo163.setObjectId("5cedd6fd7b30990001a5686b");
        oo163.setColor("1f83ff");
        mockTable.add(oo163);

        ObjectMapPo oo164 = new ObjectMapPo();
        oo164.setTenantId(71578);
        oo164.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo164.setAppId("CRM");
        oo164.setApiName("ProductObj");
        oo164.setKey("一桶半鲜虾鱼板面_今麦郎_一桶半");
        oo164.setName("一桶半鲜虾鱼板面");
        oo164.setObjectId("5cedd6fd7b30990001a56869");
        oo164.setColor("d2a6ce");
        mockTable.add(oo164);

        ObjectMapPo oo165 = new ObjectMapPo();
        oo165.setTenantId(71578);
        oo165.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo165.setAppId("CRM");
        oo165.setApiName("ProductObj");
        oo165.setKey("一桶半泡椒牛肉面_今麦郎_一桶半");
        oo165.setName("一桶半泡椒牛肉面");
        oo165.setObjectId("5cedd6fd7b30990001a56867");
        oo165.setColor("d24c4f");
        mockTable.add(oo165);

        ObjectMapPo oo166 = new ObjectMapPo();
        oo166.setTenantId(71578);
        oo166.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo166.setAppId("CRM");
        oo166.setApiName("ProductObj");
        oo166.setKey("一桶半酸豆角排骨面_今麦郎_一桶半");
        oo166.setName("一桶半酸豆角排骨面");
        oo166.setObjectId("5cedd6fd7b30990001a56865");
        oo166.setColor("e0c29d");
        mockTable.add(oo166);

        ObjectMapPo oo167 = new ObjectMapPo();
        oo167.setTenantId(71578);
        oo167.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo167.setAppId("CRM");
        oo167.setApiName("ProductObj");
        oo167.setKey("一桶半香辣牛肉面_今麦郎_一桶半");
        oo167.setName("一桶半香辣牛肉面");
        oo167.setObjectId("5cedd6fd7b30990001a56792");
        oo167.setColor("259bc9");
        mockTable.add(oo167);

        ObjectMapPo oo168 = new ObjectMapPo();
        oo168.setTenantId(71578);
        oo168.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo168.setAppId("CRM");
        oo168.setApiName("ProductObj");
        oo168.setKey("一桶半红油爆椒牛肉面_今麦郎_一桶半");
        oo168.setName("一桶半红油爆椒牛肉面");
        oo168.setObjectId("5cedd6fd7b30990001a56790");
        oo168.setColor("86c12d");
        mockTable.add(oo168);

        ObjectMapPo oo169 = new ObjectMapPo();
        oo169.setTenantId(71578);
        oo169.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo169.setAppId("CRM");
        oo169.setApiName("ProductObj");
        oo169.setKey("一桶半麻辣排骨面_今麦郎_一桶半");
        oo169.setName("一桶半麻辣排骨面");
        oo169.setObjectId("5cedd6fc426b790001ee004c");
        oo169.setColor("6da9ff");
        mockTable.add(oo169);

        ObjectMapPo oo170 = new ObjectMapPo();
        oo170.setTenantId(71578);
        oo170.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo170.setAppId("CRM");
        oo170.setApiName("ProductObj");
        oo170.setKey("一桶半麻辣牛肉面_今麦郎_一桶半");
        oo170.setName("一桶半麻辣牛肉面");
        oo170.setObjectId("5cedd6fc426b790001ee004a");
        oo170.setColor("5f54ce");
        mockTable.add(oo170);

        ObjectMapPo oo171 = new ObjectMapPo();
        oo171.setTenantId(71578);
        oo171.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo171.setAppId("CRM");
        oo171.setApiName("ProductObj");
        oo171.setKey("一桶半清真油泼辣子酸汤面_今麦郎_一桶半");
        oo171.setName("一桶半清真油泼辣子酸汤面");
        oo171.setObjectId("5cedd6fc426b790001ee0046");
        oo171.setColor("bacd37");
        mockTable.add(oo171);

        ObjectMapPo oo172 = new ObjectMapPo();
        oo172.setTenantId(71578);
        oo172.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo172.setAppId("CRM");
        oo172.setApiName("ProductObj");
        oo172.setKey("一桶半酸辣牛肉面_今麦郎_一桶半");
        oo172.setName("一桶半酸辣牛肉面");
        oo172.setObjectId("5cedd6fc426b790001ee0044");
        oo172.setColor("f8decb");
        mockTable.add(oo172);

        ObjectMapPo oo173 = new ObjectMapPo();
        oo173.setTenantId(71578);
        oo173.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo173.setAppId("CRM");
        oo173.setApiName("ProductObj");
        oo173.setKey("一桶半老坛酸菜牛肉面_今麦郎_一桶半");
        oo173.setName("一桶半老坛酸菜牛肉面");
        oo173.setObjectId("5cedd6fc426b790001ee0042");
        oo173.setColor("70cefc");
        mockTable.add(oo173);

        ObjectMapPo oo174 = new ObjectMapPo();
        oo174.setTenantId(71578);
        oo174.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo174.setAppId("CRM");
        oo174.setApiName("ProductObj");
        oo174.setKey("一桶半葱香排骨面_今麦郎_一桶半");
        oo174.setName("一桶半葱香排骨面");
        oo174.setObjectId("5cedd6fc426b790001ee003e");
        oo174.setColor("f2b6c2");
        mockTable.add(oo174);

        ObjectMapPo oo175 = new ObjectMapPo();
        oo175.setTenantId(71578);
        oo175.setModelId("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        oo175.setAppId("CRM");
        oo175.setApiName("ProductObj");
        oo175.setKey("一桶半红烧牛肉面_今麦郎_一桶半");
        oo175.setName("一桶半红烧牛肉面");
        oo175.setObjectId("5cedd6fc426b790001ee003c");
        oo175.setColor("ff74cc");
        mockTable.add(oo175);



        ObjectMapPo oo176 = new ObjectMapPo();
        oo176.setTenantId(683665);
        oo176.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo176.setAppId("CRM");
        oo176.setApiName("ProductObj");
        oo176.setKey("美汁源热带果粒_可口可乐_420ml");
        oo176.setName("美汁源热带果粒_可口可乐_420ml");
        oo176.setObjectId("5e79efcd7b96290001354d84");
        oo176.setColor("3dd4c4");
        mockTable.add(oo176);


        ObjectMapPo oo177 = new ObjectMapPo();
        oo177.setTenantId(683665);
        oo177.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo177.setAppId("CRM");
        oo177.setApiName("ProductObj");
        oo177.setKey("雪碧细长罐_可口可乐_330ml");
        oo177.setName("雪碧细长罐_可口可乐_330ml");
        oo177.setObjectId("5e79efcd7b96290001354d28");
        oo177.setColor("3f3f98");
        mockTable.add(oo177);


        ObjectMapPo oo178 = new ObjectMapPo();
        oo178.setTenantId(683665);
        oo178.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo178.setAppId("CRM");
        oo178.setApiName("ProductObj");
        oo178.setKey("冰红茶_康师傅_330ml");
        oo178.setName("冰红茶_康师傅_330ml");
        oo178.setObjectId("5e79efcd7b96290001354ccc");
        oo178.setColor("fce011");
        mockTable.add(oo178);


        ObjectMapPo oo179 = new ObjectMapPo();
        oo179.setTenantId(683665);
        oo179.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo179.setAppId("CRM");
        oo179.setApiName("ProductObj");
        oo179.setKey("七喜_百事_550ml");
        oo179.setName("七喜_百事_550ml");
        oo179.setObjectId("5e79efcc7b96290001354c70");
        oo179.setColor("ec377f");
        mockTable.add(oo179);


        ObjectMapPo oo180 = new ObjectMapPo();
        oo180.setTenantId(683665);
        oo180.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo180.setAppId("CRM");
        oo180.setApiName("ProductObj");
        oo180.setKey("农夫果园菠芒30_农夫山泉_500ml");
        oo180.setName("农夫果园菠芒30_农夫山泉_500ml");
        oo180.setObjectId("5e79efcc7b96290001354c14");
        oo180.setColor("679363");
        mockTable.add(oo180);


        ObjectMapPo oo181 = new ObjectMapPo();
        oo181.setTenantId(683665);
        oo181.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo181.setAppId("CRM");
        oo181.setApiName("ProductObj");
        oo181.setKey("美汁源果粒橙_可口可乐_450ml");
        oo181.setName("美汁源果粒橙_可口可乐_450ml");
        oo181.setObjectId("5e79efcc7b96290001354bb8");
        oo181.setColor("afdcd6");
        mockTable.add(oo181);


        ObjectMapPo oo182 = new ObjectMapPo();
        oo182.setTenantId(683665);
        oo182.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo182.setAppId("CRM");
        oo182.setApiName("ProductObj");
        oo182.setKey("可乐瓶装_可口可乐_330ml");
        oo182.setName("可乐瓶装_可口可乐_330ml");
        oo182.setObjectId("5e79efcc7b96290001354b5c");
        oo182.setColor("cc6044");
        mockTable.add(oo182);


        ObjectMapPo oo183 = new ObjectMapPo();
        oo183.setTenantId(683665);
        oo183.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo183.setAppId("CRM");
        oo183.setApiName("ProductObj");
        oo183.setKey("雪碧_可口可乐_1l");
        oo183.setName("雪碧_可口可乐_1l");
        oo183.setObjectId("5e79efcc7b96290001354b00");
        oo183.setColor("b75c25");
        mockTable.add(oo183);


        ObjectMapPo oo184 = new ObjectMapPo();
        oo184.setTenantId(683665);
        oo184.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo184.setAppId("CRM");
        oo184.setApiName("ProductObj");
        oo184.setKey("茶兀蜜桃乌龙茶_农夫山泉_500ml");
        oo184.setName("茶兀蜜桃乌龙茶_农夫山泉_500ml");
        oo184.setObjectId("5e79efcc7b96290001354aa4");
        oo184.setColor("e05829");
        mockTable.add(oo184);


        ObjectMapPo oo185 = new ObjectMapPo();
        oo185.setTenantId(683665);
        oo185.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo185.setAppId("CRM");
        oo185.setApiName("ProductObj");
        oo185.setKey("茉莉果茶_康师傅_500ml");
        oo185.setName("茉莉果茶_康师傅_500ml");
        oo185.setObjectId("5e79efcc7b96290001354a48");
        oo185.setColor("20da9a");
        mockTable.add(oo185);


        ObjectMapPo oo186 = new ObjectMapPo();
        oo186.setTenantId(683665);
        oo186.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo186.setAppId("CRM");
        oo186.setApiName("ProductObj");
        oo186.setKey("纯悦饮用水_可口可乐_1.5L");
        oo186.setName("纯悦饮用水_可口可乐_1.5L");
        oo186.setObjectId("5e79efcc7b962900013549ec");
        oo186.setColor("9bcdb5");
        mockTable.add(oo186);


        ObjectMapPo oo187 = new ObjectMapPo();
        oo187.setTenantId(683665);
        oo187.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo187.setAppId("CRM");
        oo187.setApiName("ProductObj");
        oo187.setKey("冰红茶柠檬口味_康师傅_500ml");
        oo187.setName("冰红茶柠檬口味_康师傅_500ml");
        oo187.setObjectId("5e79efcc7b96290001354990");
        oo187.setColor("063664");
        mockTable.add(oo187);


        ObjectMapPo oo188 = new ObjectMapPo();
        oo188.setTenantId(683665);
        oo188.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo188.setAppId("CRM");
        oo188.setApiName("ProductObj");
        oo188.setKey("爱夸饮用天然矿泉水_统一_570ml");
        oo188.setName("爱夸饮用天然矿泉水_统一_570ml");
        oo188.setObjectId("5e79efcc7b96290001354934");
        oo188.setColor("c5bc9c");
        mockTable.add(oo188);


        ObjectMapPo oo189 = new ObjectMapPo();
        oo189.setTenantId(683665);
        oo189.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo189.setAppId("CRM");
        oo189.setApiName("ProductObj");
        oo189.setKey("魔爪黑罐_可口可乐_330ml");
        oo189.setName("魔爪黑罐_可口可乐_330ml");
        oo189.setObjectId("5e79efcb7b962900013548d8");
        oo189.setColor("ce1654");
        mockTable.add(oo189);


        ObjectMapPo oo190 = new ObjectMapPo();
        oo190.setTenantId(683665);
        oo190.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo190.setAppId("CRM");
        oo190.setApiName("ProductObj");
        oo190.setKey("七喜_百事_6*330ml");
        oo190.setName("七喜_百事_6*330ml");
        oo190.setObjectId("5e79efcb7b9629000135487c");
        oo190.setColor("2470a0");
        mockTable.add(oo190);


        ObjectMapPo oo191 = new ObjectMapPo();
        oo191.setTenantId(683665);
        oo191.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo191.setAppId("CRM");
        oo191.setApiName("ProductObj");
        oo191.setKey("NFC橙汁100_农夫山泉_300ml");
        oo191.setName("NFC橙汁100_农夫山泉_300ml");
        oo191.setObjectId("5e79efcb7b96290001354820");
        oo191.setColor("ffa92e");
        mockTable.add(oo191);


        ObjectMapPo oo192 = new ObjectMapPo();
        oo192.setTenantId(683665);
        oo192.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo192.setAppId("CRM");
        oo192.setApiName("ProductObj");
        oo192.setKey("芬达橙_可口可乐_2.0L");
        oo192.setName("芬达橙_可口可乐_2.0L");
        oo192.setObjectId("5e79efcb7b962900013547c4");
        oo192.setColor("f96474");
        mockTable.add(oo192);


        ObjectMapPo oo193 = new ObjectMapPo();
        oo193.setTenantId(683665);
        oo193.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo193.setAppId("CRM");
        oo193.setApiName("ProductObj");
        oo193.setKey("维他命水热带水果_农夫山泉_500ml");
        oo193.setName("维他命水热带水果_农夫山泉_500ml");
        oo193.setObjectId("5e79efcb7b96290001354768");
        oo193.setColor("d9daf2");
        mockTable.add(oo193);


        ObjectMapPo oo194 = new ObjectMapPo();
        oo194.setTenantId(683665);
        oo194.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo194.setAppId("CRM");
        oo194.setApiName("ProductObj");
        oo194.setKey("美年达橙_百事_600ml");
        oo194.setName("美年达橙_百事_600ml");
        oo194.setObjectId("5e79efcb7b9629000135470c");
        oo194.setColor("715f5b");
        mockTable.add(oo194);


        ObjectMapPo oo195 = new ObjectMapPo();
        oo195.setTenantId(683665);
        oo195.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo195.setAppId("CRM");
        oo195.setApiName("ProductObj");
        oo195.setKey("美年达葡萄_百事_600ml");
        oo195.setName("美年达葡萄_百事_600ml");
        oo195.setObjectId("5e79efcb7b962900013546b0");
        oo195.setColor("5d1677");
        mockTable.add(oo195);


        ObjectMapPo oo196 = new ObjectMapPo();
        oo196.setTenantId(683665);
        oo196.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo196.setAppId("CRM");
        oo196.setApiName("ProductObj");
        oo196.setKey("东方树叶乌龙茶_农夫山泉_500ml");
        oo196.setName("东方树叶乌龙茶_农夫山泉_500ml");
        oo196.setObjectId("5e79efcb7b96290001354654");
        oo196.setColor("d25565");
        mockTable.add(oo196);


        ObjectMapPo oo197 = new ObjectMapPo();
        oo197.setTenantId(683665);
        oo197.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo197.setAppId("CRM");
        oo197.setApiName("ProductObj");
        oo197.setKey("酷儿蜜桃汁_可口可乐_450ml");
        oo197.setName("酷儿蜜桃汁_可口可乐_450ml");
        oo197.setObjectId("5e79efcb7b962900013545f8");
        oo197.setColor("c91f25");
        mockTable.add(oo197);


        ObjectMapPo oo198 = new ObjectMapPo();
        oo198.setTenantId(683665);
        oo198.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo198.setAppId("CRM");
        oo198.setApiName("ProductObj");
        oo198.setKey("怡泉+C柠檬_可口可乐_500ml");
        oo198.setName("怡泉+C柠檬_可口可乐_500ml");
        oo198.setObjectId("5e79efca7b962900013541cc");
        oo198.setColor("43d9fd");
        mockTable.add(oo198);


        ObjectMapPo oo199 = new ObjectMapPo();
        oo199.setTenantId(683665);
        oo199.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo199.setAppId("CRM");
        oo199.setApiName("ProductObj");
        oo199.setKey("农夫果园30番莓_农夫山泉_500ml");
        oo199.setName("农夫果园30番莓_农夫山泉_500ml");
        oo199.setObjectId("5e79efca7b96290001354170");
        oo199.setColor("59c2c4");
        mockTable.add(oo199);


        ObjectMapPo oo200 = new ObjectMapPo();
        oo200.setTenantId(683665);
        oo200.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo200.setAppId("CRM");
        oo200.setApiName("ProductObj");
        oo200.setKey("美年达西瓜_百事_600ml");
        oo200.setName("美年达西瓜_百事_600ml");
        oo200.setObjectId("5e79efca7b96290001354114");
        oo200.setColor("4f658a");
        mockTable.add(oo200);


        ObjectMapPo oo201 = new ObjectMapPo();
        oo201.setTenantId(683665);
        oo201.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo201.setAppId("CRM");
        oo201.setApiName("ProductObj");
        oo201.setKey("天然矿泉水_农夫山泉_535ml");
        oo201.setName("天然矿泉水_农夫山泉_535ml");
        oo201.setObjectId("5e79efca7b962900013540b8");
        oo201.setColor("98185e");
        mockTable.add(oo201);


        ObjectMapPo oo202 = new ObjectMapPo();
        oo202.setTenantId(683665);
        oo202.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo202.setAppId("CRM");
        oo202.setApiName("ProductObj");
        oo202.setKey("芬达西瓜_可口可乐_500ml");
        oo202.setName("芬达西瓜_可口可乐_500ml");
        oo202.setObjectId("5e79efca7b9629000135405c");
        oo202.setColor("18979f");
        mockTable.add(oo202);


        ObjectMapPo oo203 = new ObjectMapPo();
        oo203.setTenantId(683665);
        oo203.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo203.setAppId("CRM");
        oo203.setApiName("ProductObj");
        oo203.setKey("维他命水石榴蓝莓_农夫山泉_500ml");
        oo203.setName("维他命水石榴蓝莓_农夫山泉_500ml");
        oo203.setObjectId("5e79efca7b96290001354000");
        oo203.setColor("508fb3");
        mockTable.add(oo203);


        ObjectMapPo oo204 = new ObjectMapPo();
        oo204.setTenantId(683665);
        oo204.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo204.setAppId("CRM");
        oo204.setApiName("ProductObj");
        oo204.setKey("可乐_可口可乐_6*330ml");
        oo204.setName("可乐_可口可乐_6*330ml");
        oo204.setObjectId("5e79efca7b96290001353fa4");
        oo204.setColor("f92a82");
        mockTable.add(oo204);


        ObjectMapPo oo205 = new ObjectMapPo();
        oo205.setTenantId(683665);
        oo205.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo205.setAppId("CRM");
        oo205.setApiName("ProductObj");
        oo205.setKey("零度可乐_可口可乐_500ml");
        oo205.setName("零度可乐_可口可乐_500ml");
        oo205.setObjectId("5e79efca7b96290001353f48");
        oo205.setColor("edb67c");
        mockTable.add(oo205);


        ObjectMapPo oo206 = new ObjectMapPo();
        oo206.setTenantId(683665);
        oo206.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo206.setAppId("CRM");
        oo206.setApiName("ProductObj");
        oo206.setKey("可乐_可口可乐_2L");
        oo206.setName("可乐_可口可乐_2L");
        oo206.setObjectId("5e79efc97b96290001353eec");
        oo206.setColor("f8061d");
        mockTable.add(oo206);


        ObjectMapPo oo207 = new ObjectMapPo();
        oo207.setTenantId(683665);
        oo207.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo207.setAppId("CRM");
        oo207.setApiName("ProductObj");
        oo207.setKey("可乐_可口可乐_12*300ml");
        oo207.setName("可乐_可口可乐_12*300ml");
        oo207.setObjectId("5e79efc97b96290001353e90");
        oo207.setColor("fc8e1a");
        mockTable.add(oo207);


        ObjectMapPo oo208 = new ObjectMapPo();
        oo208.setTenantId(683665);
        oo208.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo208.setAppId("CRM");
        oo208.setApiName("ProductObj");
        oo208.setKey("绿茶蜂蜜茉莉_康师傅_1L");
        oo208.setName("绿茶蜂蜜茉莉_康师傅_1L");
        oo208.setObjectId("5e79efc97b96290001353e34");
        oo208.setColor("9385ec");
        mockTable.add(oo208);


        ObjectMapPo oo209 = new ObjectMapPo();
        oo209.setTenantId(683665);
        oo209.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo209.setAppId("CRM");
        oo209.setApiName("ProductObj");
        oo209.setKey("海之言海盐+柠檬_统一_500ml");
        oo209.setName("海之言海盐+柠檬_统一_500ml");
        oo209.setObjectId("5e79efc97b96290001353dd8");
        oo209.setColor("b9cf44");
        mockTable.add(oo209);


        ObjectMapPo oo210 = new ObjectMapPo();
        oo210.setTenantId(683665);
        oo210.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo210.setAppId("CRM");
        oo210.setApiName("ProductObj");
        oo210.setKey("维他命水柠檬_农夫山泉_500ml");
        oo210.setName("维他命水柠檬_农夫山泉_500ml");
        oo210.setObjectId("5e79efc97b96290001353d7c");
        oo210.setColor("244b67");
        mockTable.add(oo210);


        ObjectMapPo oo211 = new ObjectMapPo();
        oo211.setTenantId(683665);
        oo211.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo211.setAppId("CRM");
        oo211.setApiName("ProductObj");
        oo211.setKey("饮用水_农夫山泉_550ml");
        oo211.setName("饮用水_农夫山泉_550ml");
        oo211.setObjectId("5e79efc97b96290001353d20");
        oo211.setColor("90fee7");
        mockTable.add(oo211);


        ObjectMapPo oo212 = new ObjectMapPo();
        oo212.setTenantId(683665);
        oo212.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo212.setAppId("CRM");
        oo212.setApiName("ProductObj");
        oo212.setKey("尖叫多肽型西柚_农夫山泉_550ml");
        oo212.setName("尖叫多肽型西柚_农夫山泉_550ml");
        oo212.setObjectId("5e79efc97b96290001353cc4");
        oo212.setColor("f97576");
        mockTable.add(oo212);


        ObjectMapPo oo213 = new ObjectMapPo();
        oo213.setTenantId(683665);
        oo213.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo213.setAppId("CRM");
        oo213.setApiName("ProductObj");
        oo213.setKey("纯悦饮用水_可口可乐_800ml");
        oo213.setName("纯悦饮用水_可口可乐_800ml");
        oo213.setObjectId("5e79efc97b96290001353c68");
        oo213.setColor("99b8c1");
        mockTable.add(oo213);


        ObjectMapPo oo214 = new ObjectMapPo();
        oo214.setTenantId(683665);
        oo214.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo214.setAppId("CRM");
        oo214.setApiName("ProductObj");
        oo214.setKey("雪碧_可口可乐_500ml");
        oo214.setName("雪碧_可口可乐_500ml");
        oo214.setObjectId("5e79efc97b96290001353c0c");
        oo214.setColor("b17737");
        mockTable.add(oo214);


        ObjectMapPo oo215 = new ObjectMapPo();
        oo215.setTenantId(683665);
        oo215.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo215.setAppId("CRM");
        oo215.setApiName("ProductObj");
        oo215.setKey("鲜橙多_统一_450ml");
        oo215.setName("鲜橙多_统一_450ml");
        oo215.setObjectId("5e79efc97b96290001353bb0");
        oo215.setColor("f6acff");
        mockTable.add(oo215);


        ObjectMapPo oo216 = new ObjectMapPo();
        oo216.setTenantId(683665);
        oo216.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo216.setAppId("CRM");
        oo216.setApiName("ProductObj");
        oo216.setKey("零度可乐细长罐_可口可乐_330ml");
        oo216.setName("零度可乐细长罐_可口可乐_330ml");
        oo216.setObjectId("5e79efc97b96290001353b54");
        oo216.setColor("beded6");
        mockTable.add(oo216);


        ObjectMapPo oo217 = new ObjectMapPo();
        oo217.setTenantId(683665);
        oo217.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo217.setAppId("CRM");
        oo217.setApiName("ProductObj");
        oo217.setKey("冰糖雪梨_康师傅_500ml");
        oo217.setName("冰糖雪梨_康师傅_500ml");
        oo217.setObjectId("5e79efc87b96290001353af8");
        oo217.setColor("7fbfd9");
        mockTable.add(oo217);


        ObjectMapPo oo218 = new ObjectMapPo();
        oo218.setTenantId(683665);
        oo218.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo218.setAppId("CRM");
        oo218.setApiName("ProductObj");
        oo218.setKey("芬达苹果_可口可乐_300ml");
        oo218.setName("芬达苹果_可口可乐_300ml");
        oo218.setObjectId("5e79efc87b96290001353a9c");
        oo218.setColor("3a9367");
        mockTable.add(oo218);


        ObjectMapPo oo219 = new ObjectMapPo();
        oo219.setTenantId(683665);
        oo219.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo219.setAppId("CRM");
        oo219.setApiName("ProductObj");
        oo219.setKey("雪碧_可口可乐_300ml");
        oo219.setName("雪碧_可口可乐_300ml");
        oo219.setObjectId("5e79efc87b96290001353a40");
        oo219.setColor("033a95");
        mockTable.add(oo219);


        ObjectMapPo oo220 = new ObjectMapPo();
        oo220.setTenantId(683665);
        oo220.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo220.setAppId("CRM");
        oo220.setApiName("ProductObj");
        oo220.setKey("阿萨姆原味奶茶_统一_1.5L");
        oo220.setName("阿萨姆原味奶茶_统一_1.5L");
        oo220.setObjectId("5e79efc87b962900013539e4");
        oo220.setColor("9b39ec");
        mockTable.add(oo220);


        ObjectMapPo oo221 = new ObjectMapPo();
        oo221.setTenantId(683665);
        oo221.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo221.setAppId("CRM");
        oo221.setApiName("ProductObj");
        oo221.setKey("可乐_可口可乐_1.25L");
        oo221.setName("可乐_可口可乐_1.25L");
        oo221.setObjectId("5e79efc87b96290001353988");
        oo221.setColor("1f83ff");
        mockTable.add(oo221);


        ObjectMapPo oo222 = new ObjectMapPo();
        oo222.setTenantId(683665);
        oo222.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo222.setAppId("CRM");
        oo222.setApiName("ProductObj");
        oo222.setKey("茶兀玫瑰荔枝红茶_农夫山泉_500ml");
        oo222.setName("茶兀玫瑰荔枝红茶_农夫山泉_500ml");
        oo222.setObjectId("5e79efc87b9629000135392c");
        oo222.setColor("d2a6ce");
        mockTable.add(oo222);


        ObjectMapPo oo223 = new ObjectMapPo();
        oo223.setTenantId(683665);
        oo223.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo223.setAppId("CRM");
        oo223.setApiName("ProductObj");
        oo223.setKey("饮用水_康师傅_1.5L");
        oo223.setName("饮用水_康师傅_1.5L");
        oo223.setObjectId("5e79efc87b962900013538d0");
        oo223.setColor("d24c4f");
        mockTable.add(oo223);


        ObjectMapPo oo224 = new ObjectMapPo();
        oo224.setTenantId(683665);
        oo224.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo224.setAppId("CRM");
        oo224.setApiName("ProductObj");
        oo224.setKey("冰红茶_康师傅_1L");
        oo224.setName("冰红茶_康师傅_1L");
        oo224.setObjectId("5e79efc87b96290001353874");
        oo224.setColor("e0c29d");
        mockTable.add(oo224);


        ObjectMapPo oo225 = new ObjectMapPo();
        oo225.setTenantId(683665);
        oo225.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo225.setAppId("CRM");
        oo225.setApiName("ProductObj");
        oo225.setKey("绿茶_康师傅_500mL");
        oo225.setName("绿茶_康师傅_500mL");
        oo225.setObjectId("5e79efc87b96290001353818");
        oo225.setColor("259bc9");
        mockTable.add(oo225);


        ObjectMapPo oo226 = new ObjectMapPo();
        oo226.setTenantId(683665);
        oo226.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo226.setAppId("CRM");
        oo226.setApiName("ProductObj");
        oo226.setKey("可乐_百事_1L");
        oo226.setName("可乐_百事_1L");
        oo226.setObjectId("5e79efc87b962900013537bc");
        oo226.setColor("86c12d");
        mockTable.add(oo226);


        ObjectMapPo oo227 = new ObjectMapPo();
        oo227.setTenantId(683665);
        oo227.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo227.setAppId("CRM");
        oo227.setApiName("ProductObj");
        oo227.setKey("魔爪白罐_可口可乐_330ml");
        oo227.setName("魔爪白罐_可口可乐_330ml");
        oo227.setObjectId("5e79efc87b96290001353760");
        oo227.setColor("6da9ff");
        mockTable.add(oo227);


        ObjectMapPo oo228 = new ObjectMapPo();
        oo228.setTenantId(683665);
        oo228.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo228.setAppId("CRM");
        oo228.setApiName("ProductObj");
        oo228.setKey("美年达橙_百事_6*330ml");
        oo228.setName("美年达橙_百事_6*330ml");
        oo228.setObjectId("5e79efc77b96290001353704");
        oo228.setColor("5f54ce");
        mockTable.add(oo228);


        ObjectMapPo oo229 = new ObjectMapPo();
        oo229.setTenantId(683665);
        oo229.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo229.setAppId("CRM");
        oo229.setApiName("ProductObj");
        oo229.setKey("茉莉蜜茶_康师傅_250ml");
        oo229.setName("茉莉蜜茶_康师傅_250ml");
        oo229.setObjectId("5e79efc77b962900013536a8");
        oo229.setColor("bacd37");
        mockTable.add(oo229);


        ObjectMapPo oo230 = new ObjectMapPo();
        oo230.setTenantId(683665);
        oo230.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo230.setAppId("CRM");
        oo230.setApiName("ProductObj");
        oo230.setKey("东方树叶红茶_农夫山泉_500ml");
        oo230.setName("东方树叶红茶_农夫山泉_500ml");
        oo230.setObjectId("5e79efc77b9629000135364c");
        oo230.setColor("f8decb");
        mockTable.add(oo230);


        ObjectMapPo oo231 = new ObjectMapPo();
        oo231.setTenantId(683665);
        oo231.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo231.setAppId("CRM");
        oo231.setApiName("ProductObj");
        oo231.setKey("芝士奶茶_康师傅_500ml");
        oo231.setName("芝士奶茶_康师傅_500ml");
        oo231.setObjectId("5e79efc77b962900013535f0");
        oo231.setColor("70cefc");
        mockTable.add(oo231);


        ObjectMapPo oo232 = new ObjectMapPo();
        oo232.setTenantId(683665);
        oo232.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo232.setAppId("CRM");
        oo232.setApiName("ProductObj");
        oo232.setKey("芬达苹果_可口可乐_500ml");
        oo232.setName("芬达苹果_可口可乐_500ml");
        oo232.setObjectId("5e79efc77b96290001353594");
        oo232.setColor("f2b6c2");
        mockTable.add(oo232);


        ObjectMapPo oo233 = new ObjectMapPo();
        oo233.setTenantId(683665);
        oo233.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo233.setAppId("CRM");
        oo233.setApiName("ProductObj");
        oo233.setKey("维他命水蓝莓树莓_农夫山泉_500ml");
        oo233.setName("维他命水蓝莓树莓_农夫山泉_500ml");
        oo233.setObjectId("5e79efc77b96290001353538");
        oo233.setColor("ff74cc");
        mockTable.add(oo233);


        ObjectMapPo oo234 = new ObjectMapPo();
        oo234.setTenantId(683665);
        oo234.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo234.setAppId("CRM");
        oo234.setApiName("ProductObj");
        oo234.setKey("酷儿橙汁_可口可乐_450ml");
        oo234.setName("酷儿橙汁_可口可乐_450ml");
        oo234.setObjectId("5e79efc77b962900013534dc");
        oo234.setColor("f95961");
        mockTable.add(oo234);


        ObjectMapPo oo235 = new ObjectMapPo();
        oo235.setTenantId(683665);
        oo235.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo235.setAppId("CRM");
        oo235.setApiName("ProductObj");
        oo235.setKey("美汁源三重果粒_可口可乐_420ml");
        oo235.setName("美汁源三重果粒_可口可乐_420ml");
        oo235.setObjectId("5e79efc77b96290001353480");
        oo235.setColor("7fbc9c");
        mockTable.add(oo235);


        ObjectMapPo oo236 = new ObjectMapPo();
        oo236.setTenantId(683665);
        oo236.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo236.setAppId("CRM");
        oo236.setApiName("ProductObj");
        oo236.setKey("纯悦饮用水_可口可乐_12*550ml");
        oo236.setName("纯悦饮用水_可口可乐_12*550ml");
        oo236.setObjectId("5e79efc77b96290001353424");
        oo236.setColor("ff6331");
        mockTable.add(oo236);


        ObjectMapPo oo237 = new ObjectMapPo();
        oo237.setTenantId(683665);
        oo237.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo237.setAppId("CRM");
        oo237.setApiName("ProductObj");
        oo237.setKey("茶兀柠檬红茶_农夫山泉_500ml");
        oo237.setName("茶兀柠檬红茶_农夫山泉_500ml");
        oo237.setObjectId("5e79efc77b962900013533c8");
        oo237.setColor("578f8b");
        mockTable.add(oo237);


        ObjectMapPo oo238 = new ObjectMapPo();
        oo238.setTenantId(683665);
        oo238.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo238.setAppId("CRM");
        oo238.setApiName("ProductObj");
        oo238.setKey("芬达葡萄_可口可乐_500ml");
        oo238.setName("芬达葡萄_可口可乐_500ml");
        oo238.setObjectId("5e79efc67b9629000135336c");
        oo238.setColor("3dd4c4");
        mockTable.add(oo238);


        ObjectMapPo oo239 = new ObjectMapPo();
        oo239.setTenantId(683665);
        oo239.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo239.setAppId("CRM");
        oo239.setApiName("ProductObj");
        oo239.setKey("酸梅汤_康师傅_500ml");
        oo239.setName("酸梅汤_康师傅_500ml");
        oo239.setObjectId("5e79efc67b96290001353310");
        oo239.setColor("3f3f98");
        mockTable.add(oo239);


        ObjectMapPo oo240 = new ObjectMapPo();
        oo240.setTenantId(683665);
        oo240.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo240.setAppId("CRM");
        oo240.setApiName("ProductObj");
        oo240.setKey("冰红茶_统一_2L");
        oo240.setName("冰红茶_统一_2L");
        oo240.setObjectId("5e79efc67b962900013532b4");
        oo240.setColor("fce011");
        mockTable.add(oo240);


        ObjectMapPo oo241 = new ObjectMapPo();
        oo241.setTenantId(683665);
        oo241.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo241.setAppId("CRM");
        oo241.setApiName("ProductObj");
        oo241.setKey("饮用水_康师傅_550ml");
        oo241.setName("饮用水_康师傅_550ml");
        oo241.setObjectId("5e79efc67b96290001353258");
        oo241.setColor("ec377f");
        mockTable.add(oo241);


        ObjectMapPo oo242 = new ObjectMapPo();
        oo242.setTenantId(683665);
        oo242.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo242.setAppId("CRM");
        oo242.setApiName("ProductObj");
        oo242.setKey("芬达苹果细长罐_可口可乐_330ml");
        oo242.setName("芬达苹果细长罐_可口可乐_330ml");
        oo242.setObjectId("5e79efc67b962900013531fc");
        oo242.setColor("679363");
        mockTable.add(oo242);


        ObjectMapPo oo243 = new ObjectMapPo();
        oo243.setTenantId(683665);
        oo243.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo243.setAppId("CRM");
        oo243.setApiName("ProductObj");
        oo243.setKey("可乐细长罐_百事_330ml");
        oo243.setName("可乐细长罐_百事_330ml");
        oo243.setObjectId("5e79efc67b962900013531a0");
        oo243.setColor("afdcd6");
        mockTable.add(oo243);


        ObjectMapPo oo244 = new ObjectMapPo();
        oo244.setTenantId(683665);
        oo244.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo244.setAppId("CRM");
        oo244.setApiName("ProductObj");
        oo244.setKey("冰红茶_康师傅_250ml");
        oo244.setName("冰红茶_康师傅_250ml");
        oo244.setObjectId("5e79efc67b96290001353144");
        oo244.setColor("cc6044");
        mockTable.add(oo244);


        ObjectMapPo oo245 = new ObjectMapPo();
        oo245.setTenantId(683665);
        oo245.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo245.setAppId("CRM");
        oo245.setApiName("ProductObj");
        oo245.setKey("维他命水乳酸菌_农夫山泉_500ml");
        oo245.setName("维他命水乳酸菌_农夫山泉_500ml");
        oo245.setObjectId("5e79efc67b962900013530e8");
        oo245.setColor("b75c25");
        mockTable.add(oo245);


        ObjectMapPo oo246 = new ObjectMapPo();
        oo246.setTenantId(683665);
        oo246.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo246.setAppId("CRM");
        oo246.setApiName("ProductObj");
        oo246.setKey("饮用水_农夫山泉_1.5L");
        oo246.setName("饮用水_农夫山泉_1.5L");
        oo246.setObjectId("5e79efc67b9629000135308c");
        oo246.setColor("e05829");
        mockTable.add(oo246);


        ObjectMapPo oo247 = new ObjectMapPo();
        oo247.setTenantId(683665);
        oo247.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo247.setAppId("CRM");
        oo247.setApiName("ProductObj");
        oo247.setKey("经典奶茶_康师傅_500ml");
        oo247.setName("经典奶茶_康师傅_500ml");
        oo247.setObjectId("5e79efc67b96290001353030");
        oo247.setColor("20da9a");
        mockTable.add(oo247);


        ObjectMapPo oo248 = new ObjectMapPo();
        oo248.setTenantId(683665);
        oo248.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo248.setAppId("CRM");
        oo248.setApiName("ProductObj");
        oo248.setKey("茶兀柚子绿茶_农夫山泉_500ml");
        oo248.setName("茶兀柚子绿茶_农夫山泉_500ml");
        oo248.setObjectId("5e79efc57b96290001352750");
        oo248.setColor("9bcdb5");
        mockTable.add(oo248);


        ObjectMapPo oo249 = new ObjectMapPo();
        oo249.setTenantId(683665);
        oo249.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo249.setAppId("CRM");
        oo249.setApiName("ProductObj");
        oo249.setKey("雪碧6连装_可口可乐_6*330ml");
        oo249.setName("雪碧6连装_可口可乐_6*330ml");
        oo249.setObjectId("5e79efc57b962900013526f4");
        oo249.setColor("063664");
        mockTable.add(oo249);


        ObjectMapPo oo250 = new ObjectMapPo();
        oo250.setTenantId(683665);
        oo250.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo250.setAppId("CRM");
        oo250.setApiName("ProductObj");
        oo250.setKey("尖叫纤维型柠檬_农夫山泉_550ml");
        oo250.setName("尖叫纤维型柠檬_农夫山泉_550ml");
        oo250.setObjectId("5e79efc57b96290001352698");
        oo250.setColor("c5bc9c");
        mockTable.add(oo250);


        ObjectMapPo oo251 = new ObjectMapPo();
        oo251.setTenantId(683665);
        oo251.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo251.setAppId("CRM");
        oo251.setApiName("ProductObj");
        oo251.setKey("饮用水_农夫山泉_4L");
        oo251.setName("饮用水_农夫山泉_4L");
        oo251.setObjectId("5e79efc57b9629000135263c");
        oo251.setColor("ce1654");
        mockTable.add(oo251);


        ObjectMapPo oo252 = new ObjectMapPo();
        oo252.setTenantId(683665);
        oo252.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo252.setAppId("CRM");
        oo252.setApiName("ProductObj");
        oo252.setKey("七喜细长罐_百事_330ml");
        oo252.setName("七喜细长罐_百事_330ml");
        oo252.setObjectId("5e79efc57b962900013525e0");
        oo252.setColor("2470a0");
        mockTable.add(oo252);


        ObjectMapPo oo253 = new ObjectMapPo();
        oo253.setTenantId(683665);
        oo253.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo253.setAppId("CRM");
        oo253.setApiName("ProductObj");
        oo253.setKey("可乐_百事_330ml");
        oo253.setName("可乐_百事_330ml");
        oo253.setObjectId("5e79efc57b96290001352584");
        oo253.setColor("ffa92e");
        mockTable.add(oo253);


        ObjectMapPo oo254 = new ObjectMapPo();
        oo254.setTenantId(683665);
        oo254.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo254.setAppId("CRM");
        oo254.setApiName("ProductObj");
        oo254.setKey("芬达橙_可口可乐_330ml");
        oo254.setName("芬达橙_可口可乐_330ml");
        oo254.setObjectId("5e79efc47b96290001352528");
        oo254.setColor("f96474");
        mockTable.add(oo254);


        ObjectMapPo oo255 = new ObjectMapPo();
        oo255.setTenantId(683665);
        oo255.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo255.setAppId("CRM");
        oo255.setApiName("ProductObj");
        oo255.setKey("饮用水婴幼儿_农夫山泉_1L");
        oo255.setName("饮用水婴幼儿_农夫山泉_1L");
        oo255.setObjectId("5e79efc47b962900013524cc");
        oo255.setColor("d9daf2");
        mockTable.add(oo255);


        ObjectMapPo oo256 = new ObjectMapPo();
        oo256.setTenantId(683665);
        oo256.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo256.setAppId("CRM");
        oo256.setApiName("ProductObj");
        oo256.setKey("农夫果园胡橙30_农夫山泉_500ml");
        oo256.setName("农夫果园胡橙30_农夫山泉_500ml");
        oo256.setObjectId("5e79efc47b96290001352470");
        oo256.setColor("715f5b");
        mockTable.add(oo256);


        ObjectMapPo oo257 = new ObjectMapPo();
        oo257.setTenantId(683665);
        oo257.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo257.setAppId("CRM");
        oo257.setApiName("ProductObj");
        oo257.setKey("茉莉蜜茶_康师傅_2L");
        oo257.setName("茉莉蜜茶_康师傅_2L");
        oo257.setObjectId("5e79efc47b96290001352414");
        oo257.setColor("5d1677");
        mockTable.add(oo257);


        ObjectMapPo oo258 = new ObjectMapPo();
        oo258.setTenantId(683665);
        oo258.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo258.setAppId("CRM");
        oo258.setApiName("ProductObj");
        oo258.setKey("冰露饮用水_可口可乐_550ml");
        oo258.setName("冰露饮用水_可口可乐_550ml");
        oo258.setObjectId("5e79efc47b962900013523b8");
        oo258.setColor("d25565");
        mockTable.add(oo258);


        ObjectMapPo oo259 = new ObjectMapPo();
        oo259.setTenantId(683665);
        oo259.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo259.setAppId("CRM");
        oo259.setApiName("ProductObj");
        oo259.setKey("绿茶蜂蜜茉莉味_康师傅_2L");
        oo259.setName("绿茶蜂蜜茉莉味_康师傅_2L");
        oo259.setObjectId("5e79efc47b9629000135235c");
        oo259.setColor("c91f25");
        mockTable.add(oo259);


        ObjectMapPo oo260 = new ObjectMapPo();
        oo260.setTenantId(683665);
        oo260.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo260.setAppId("CRM");
        oo260.setApiName("ProductObj");
        oo260.setKey("美年达橙细长罐_百事_330ml");
        oo260.setName("美年达橙细长罐_百事_330ml");
        oo260.setObjectId("5e79efc47b96290001352300");
        oo260.setColor("43d9fd");
        mockTable.add(oo260);


        ObjectMapPo oo261 = new ObjectMapPo();
        oo261.setTenantId(683665);
        oo261.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo261.setAppId("CRM");
        oo261.setApiName("ProductObj");
        oo261.setKey("冰糖雪梨_康师傅_1L");
        oo261.setName("冰糖雪梨_康师傅_1L");
        oo261.setObjectId("5e79efc47b962900013522a4");
        oo261.setColor("59c2c4");
        mockTable.add(oo261);


        ObjectMapPo oo262 = new ObjectMapPo();
        oo262.setTenantId(683665);
        oo262.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo262.setAppId("CRM");
        oo262.setApiName("ProductObj");
        oo262.setKey("劲凉冰红茶_康师傅_500ml");
        oo262.setName("劲凉冰红茶_康师傅_500ml");
        oo262.setObjectId("5e79efc47b96290001352248");
        oo262.setColor("4f658a");
        mockTable.add(oo262);


        ObjectMapPo oo263 = new ObjectMapPo();
        oo263.setTenantId(683665);
        oo263.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo263.setAppId("CRM");
        oo263.setApiName("ProductObj");
        oo263.setKey("冰红茶_统一_1L");
        oo263.setName("冰红茶_统一_1L");
        oo263.setObjectId("5e79efc47b962900013521ec");
        oo263.setColor("98185e");
        mockTable.add(oo263);


        ObjectMapPo oo264 = new ObjectMapPo();
        oo264.setTenantId(683665);
        oo264.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo264.setAppId("CRM");
        oo264.setApiName("ProductObj");
        oo264.setKey("无糖可乐_百事_500ml");
        oo264.setName("无糖可乐_百事_500ml");
        oo264.setObjectId("5e79efc47b96290001352190");
        oo264.setColor("18979f");
        mockTable.add(oo264);


        ObjectMapPo oo265 = new ObjectMapPo();
        oo265.setTenantId(683665);
        oo265.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo265.setAppId("CRM");
        oo265.setApiName("ProductObj");
        oo265.setKey("雪碧零卡清爽柠檬味汽水_可口可乐_330ml");
        oo265.setName("雪碧零卡清爽柠檬味汽水_可口可乐_330ml");
        oo265.setObjectId("5e79efc47b96290001352134");
        oo265.setColor("508fb3");
        mockTable.add(oo265);


        ObjectMapPo oo266 = new ObjectMapPo();
        oo266.setTenantId(683665);
        oo266.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo266.setAppId("CRM");
        oo266.setApiName("ProductObj");
        oo266.setKey("无糖可乐_百事_330ml");
        oo266.setName("无糖可乐_百事_330ml");
        oo266.setObjectId("5e79efc37b962900013520d8");
        oo266.setColor("f92a82");
        mockTable.add(oo266);


        ObjectMapPo oo267 = new ObjectMapPo();
        oo267.setTenantId(683665);
        oo267.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo267.setAppId("CRM");
        oo267.setApiName("ProductObj");
        oo267.setKey("雪碧纤维_可口可乐_500ml");
        oo267.setName("雪碧纤维_可口可乐_500ml");
        oo267.setObjectId("5e79efc37b9629000135207c");
        oo267.setColor("edb67c");
        mockTable.add(oo267);


        ObjectMapPo oo268 = new ObjectMapPo();
        oo268.setTenantId(683665);
        oo268.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo268.setAppId("CRM");
        oo268.setApiName("ProductObj");
        oo268.setKey("水溶C100柠檬味_农夫山泉_445ml");
        oo268.setName("水溶C100柠檬味_农夫山泉_445ml");
        oo268.setObjectId("5e79efc37b96290001352020");
        oo268.setColor("f8061d");
        mockTable.add(oo268);


        ObjectMapPo oo269 = new ObjectMapPo();
        oo269.setTenantId(683665);
        oo269.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo269.setAppId("CRM");
        oo269.setApiName("ProductObj");
        oo269.setKey("海晶柠檬_康师傅_500ml");
        oo269.setName("海晶柠檬_康师傅_500ml");
        oo269.setObjectId("5e79efc37b96290001351fc4");
        oo269.setColor("fc8e1a");
        mockTable.add(oo269);


        ObjectMapPo oo270 = new ObjectMapPo();
        oo270.setTenantId(683665);
        oo270.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo270.setAppId("CRM");
        oo270.setApiName("ProductObj");
        oo270.setKey("饮用水_农夫山泉_380ml");
        oo270.setName("饮用水_农夫山泉_380ml");
        oo270.setObjectId("5e79efc37b96290001351f68");
        oo270.setColor("9385ec");
        mockTable.add(oo270);


        ObjectMapPo oo271 = new ObjectMapPo();
        oo271.setTenantId(683665);
        oo271.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo271.setAppId("CRM");
        oo271.setApiName("ProductObj");
        oo271.setKey("百事blue_百事_450ml");
        oo271.setName("百事blue_百事_450ml");
        oo271.setObjectId("5e79efc37b96290001351f0c");
        oo271.setColor("b9cf44");
        mockTable.add(oo271);


        ObjectMapPo oo272 = new ObjectMapPo();
        oo272.setTenantId(683665);
        oo272.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo272.setAppId("CRM");
        oo272.setApiName("ProductObj");
        oo272.setKey("芬达水蜜桃_可口可乐_500ml");
        oo272.setName("芬达水蜜桃_可口可乐_500ml");
        oo272.setObjectId("5e79efc37b96290001351eb0");
        oo272.setColor("244b67");
        mockTable.add(oo272);


        ObjectMapPo oo273 = new ObjectMapPo();
        oo273.setTenantId(683665);
        oo273.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo273.setAppId("CRM");
        oo273.setApiName("ProductObj");
        oo273.setKey("雅哈冰咖啡_统一_450ml");
        oo273.setName("雅哈冰咖啡_统一_450ml");
        oo273.setObjectId("5e79efc37b96290001351e54");
        oo273.setColor("90fee7");
        mockTable.add(oo273);


        ObjectMapPo oo274 = new ObjectMapPo();
        oo274.setTenantId(683665);
        oo274.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo274.setAppId("CRM");
        oo274.setApiName("ProductObj");
        oo274.setKey("茉莉蜜茶_康师傅_500ml");
        oo274.setName("茉莉蜜茶_康师傅_500ml");
        oo274.setObjectId("5e79efc37b96290001351df8");
        oo274.setColor("f97576");
        mockTable.add(oo274);


        ObjectMapPo oo275 = new ObjectMapPo();
        oo275.setTenantId(683665);
        oo275.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo275.setAppId("CRM");
        oo275.setApiName("ProductObj");
        oo275.setKey("芬达橙_可口可乐_500ml");
        oo275.setName("芬达橙_可口可乐_500ml");
        oo275.setObjectId("5e79efc37b96290001351d9c");
        oo275.setColor("99b8c1");
        mockTable.add(oo275);


        ObjectMapPo oo276 = new ObjectMapPo();
        oo276.setTenantId(683665);
        oo276.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo276.setAppId("CRM");
        oo276.setApiName("ProductObj");
        oo276.setKey("阿萨姆岩盐芝士奶茶_统一_450ml");
        oo276.setName("阿萨姆岩盐芝士奶茶_统一_450ml");
        oo276.setObjectId("5e79efc37b96290001351d40");
        oo276.setColor("b17737");
        mockTable.add(oo276);


        ObjectMapPo oo277 = new ObjectMapPo();
        oo277.setTenantId(683665);
        oo277.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo277.setAppId("CRM");
        oo277.setApiName("ProductObj");
        oo277.setKey("水溶C100青皮桔_农夫山泉_445ml");
        oo277.setName("水溶C100青皮桔_农夫山泉_445ml");
        oo277.setObjectId("5e79efc27b96290001351ce4");
        oo277.setColor("f6acff");
        mockTable.add(oo277);


        ObjectMapPo oo278 = new ObjectMapPo();
        oo278.setTenantId(683665);
        oo278.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo278.setAppId("CRM");
        oo278.setApiName("ProductObj");
        oo278.setKey("炼乳奶茶_康师傅_500ml");
        oo278.setName("炼乳奶茶_康师傅_500ml");
        oo278.setObjectId("5e79efc27b96290001351c88");
        oo278.setColor("beded6");
        mockTable.add(oo278);


        ObjectMapPo oo279 = new ObjectMapPo();
        oo279.setTenantId(683665);
        oo279.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo279.setAppId("CRM");
        oo279.setApiName("ProductObj");
        oo279.setKey("茉莉蜜茶_康师傅_1L");
        oo279.setName("茉莉蜜茶_康师傅_1L");
        oo279.setObjectId("5e79efc27b96290001351c2c");
        oo279.setColor("7fbfd9");
        mockTable.add(oo279);


        ObjectMapPo oo280 = new ObjectMapPo();
        oo280.setTenantId(683665);
        oo280.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo280.setAppId("CRM");
        oo280.setApiName("ProductObj");
        oo280.setKey("芬达橙_可口可乐_300ml");
        oo280.setName("芬达橙_可口可乐_300ml");
        oo280.setObjectId("5e79efc27b96290001351bd0");
        oo280.setColor("3a9367");
        mockTable.add(oo280);


        ObjectMapPo oo281 = new ObjectMapPo();
        oo281.setTenantId(683665);
        oo281.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo281.setAppId("CRM");
        oo281.setApiName("ProductObj");
        oo281.setKey("可乐纤维_可口可乐_500ml");
        oo281.setName("可乐纤维_可口可乐_500ml");
        oo281.setObjectId("5e79efc27b96290001351b74");
        oo281.setColor("033a95");
        mockTable.add(oo281);


        ObjectMapPo oo282 = new ObjectMapPo();
        oo282.setTenantId(683665);
        oo282.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo282.setAppId("CRM");
        oo282.setApiName("ProductObj");
        oo282.setKey("水溶C100西柚_农夫山泉_445ml");
        oo282.setName("水溶C100西柚_农夫山泉_445ml");
        oo282.setObjectId("5e79efc27b96290001351b18");
        oo282.setColor("9b39ec");
        mockTable.add(oo282);


        ObjectMapPo oo283 = new ObjectMapPo();
        oo283.setTenantId(683665);
        oo283.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo283.setAppId("CRM");
        oo283.setApiName("ProductObj");
        oo283.setKey("绿茶_统一_500ml");
        oo283.setName("绿茶_统一_500ml");
        oo283.setObjectId("5e79efc27b96290001351abc");
        oo283.setColor("1f83ff");
        mockTable.add(oo283);


        ObjectMapPo oo284 = new ObjectMapPo();
        oo284.setTenantId(683665);
        oo284.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo284.setAppId("CRM");
        oo284.setApiName("ProductObj");
        oo284.setKey("阿萨姆原味奶茶_统一_500ml");
        oo284.setName("阿萨姆原味奶茶_统一_500ml");
        oo284.setObjectId("5e79efc27b96290001351a60");
        oo284.setColor("d2a6ce");
        mockTable.add(oo284);


        ObjectMapPo oo285 = new ObjectMapPo();
        oo285.setTenantId(683665);
        oo285.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo285.setAppId("CRM");
        oo285.setApiName("ProductObj");
        oo285.setKey("芒果小酪_康师傅_3*500ml");
        oo285.setName("芒果小酪_康师傅_3*500ml");
        oo285.setObjectId("5e79efc27b96290001351a04");
        oo285.setColor("d24c4f");
        mockTable.add(oo285);


        ObjectMapPo oo286 = new ObjectMapPo();
        oo286.setTenantId(683665);
        oo286.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo286.setAppId("CRM");
        oo286.setApiName("ProductObj");
        oo286.setKey("冰红茶青苹果味_康师傅_500ml");
        oo286.setName("冰红茶青苹果味_康师傅_500ml");
        oo286.setObjectId("5e79efc27b962900013519a8");
        oo286.setColor("e0c29d");
        mockTable.add(oo286);


        ObjectMapPo oo287 = new ObjectMapPo();
        oo287.setTenantId(683665);
        oo287.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo287.setAppId("CRM");
        oo287.setApiName("ProductObj");
        oo287.setKey("绿茶_统一_1L");
        oo287.setName("绿茶_统一_1L");
        oo287.setObjectId("5e79efc27b9629000135194c");
        oo287.setColor("259bc9");
        mockTable.add(oo287);


        ObjectMapPo oo288 = new ObjectMapPo();
        oo288.setTenantId(683665);
        oo288.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo288.setAppId("CRM");
        oo288.setApiName("ProductObj");
        oo288.setKey("芬达橙细长罐_可口可乐_330ml");
        oo288.setName("芬达橙细长罐_可口可乐_330ml");
        oo288.setObjectId("5e79efc27b962900013518f0");
        oo288.setColor("86c12d");
        mockTable.add(oo288);


        ObjectMapPo oo289 = new ObjectMapPo();
        oo289.setTenantId(683665);
        oo289.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo289.setAppId("CRM");
        oo289.setApiName("ProductObj");
        oo289.setKey("阿萨姆小奶茶_统一_360ml");
        oo289.setName("阿萨姆小奶茶_统一_360ml");
        oo289.setObjectId("5e79efc17b96290001351894");
        oo289.setColor("6da9ff");
        mockTable.add(oo289);


        ObjectMapPo oo290 = new ObjectMapPo();
        oo290.setTenantId(683665);
        oo290.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo290.setAppId("CRM");
        oo290.setApiName("ProductObj");
        oo290.setKey("茉莉清茶_康师傅_1L");
        oo290.setName("茉莉清茶_康师傅_1L");
        oo290.setObjectId("5e79efc17b96290001351838");
        oo290.setColor("5f54ce");
        mockTable.add(oo290);


        ObjectMapPo oo291 = new ObjectMapPo();
        oo291.setTenantId(683665);
        oo291.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo291.setAppId("CRM");
        oo291.setApiName("ProductObj");
        oo291.setKey("午后奶茶_统一_500ml");
        oo291.setName("午后奶茶_统一_500ml");
        oo291.setObjectId("5e79efc17b962900013517dc");
        oo291.setColor("bacd37");
        mockTable.add(oo291);


        ObjectMapPo oo292 = new ObjectMapPo();
        oo292.setTenantId(683665);
        oo292.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo292.setAppId("CRM");
        oo292.setApiName("ProductObj");
        oo292.setKey("芬达橙_可口可乐_6*330ml");
        oo292.setName("芬达橙_可口可乐_6*330ml");
        oo292.setObjectId("5e79efc17b96290001351780");
        oo292.setColor("f8decb");
        mockTable.add(oo292);


        ObjectMapPo oo293 = new ObjectMapPo();
        oo293.setTenantId(683665);
        oo293.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo293.setAppId("CRM");
        oo293.setApiName("ProductObj");
        oo293.setKey("可乐_可口可乐_500ml");
        oo293.setName("可乐_可口可乐_500ml");
        oo293.setObjectId("5e79efc17b96290001351724");
        oo293.setColor("70cefc");
        mockTable.add(oo293);


        ObjectMapPo oo294 = new ObjectMapPo();
        oo294.setTenantId(683665);
        oo294.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo294.setAppId("CRM");
        oo294.setApiName("ProductObj");
        oo294.setKey("可乐罐装_百事_330ml");
        oo294.setName("可乐罐装_百事_330ml");
        oo294.setObjectId("5e79efc17b962900013516c8");
        oo294.setColor("f2b6c2");
        mockTable.add(oo294);


        ObjectMapPo oo295 = new ObjectMapPo();
        oo295.setTenantId(683665);
        oo295.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo295.setAppId("CRM");
        oo295.setApiName("ProductObj");
        oo295.setKey("冰红茶_统一_500ml");
        oo295.setName("冰红茶_统一_500ml");
        oo295.setObjectId("5e79ef4c7b9629000135012d");
        oo295.setColor("ff74cc");
        mockTable.add(oo295);


        ObjectMapPo oo296 = new ObjectMapPo();
        oo296.setTenantId(683665);
        oo296.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo296.setAppId("CRM");
        oo296.setApiName("ProductObj");
        oo296.setKey("绿茶茉莉味_统一_250ml");
        oo296.setName("绿茶茉莉味_统一_250ml");
        oo296.setObjectId("5e79ef4c7b962900013500d1");
        oo296.setColor("f95961");
        mockTable.add(oo296);


        ObjectMapPo oo297 = new ObjectMapPo();
        oo297.setTenantId(683665);
        oo297.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo297.setAppId("CRM");
        oo297.setApiName("ProductObj");
        oo297.setKey("芬达青柠_可口可乐_500ml");
        oo297.setName("芬达青柠_可口可乐_500ml");
        oo297.setObjectId("5e79ef4c7b96290001350071");
        oo297.setColor("7fbc9c");
        mockTable.add(oo297);


        ObjectMapPo oo298 = new ObjectMapPo();
        oo298.setTenantId(683665);
        oo298.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
        oo298.setAppId("CRM");
        oo298.setApiName("ProductObj");
        oo298.setKey("产品（示例）");
        oo298.setName("产品（示例）");
        oo298.setObjectId("5df26aae2848bdd5b475f55c");
        oo298.setColor("ff6331");
        mockTable.add(oo298);

//b85f432aa2e24e85b26664a566bca5a7    2e6222d660b54e08afbf13d85c6c4e2c
        ObjectMapPo oo299 = new ObjectMapPo();
        oo299.setTenantId(472252);
        oo299.setModelId("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
        oo299.setAppId("CRM");
        oo299.setApiName("ProductObj");
        oo299.setKey("山楂树下箱子_冠芳_350ml");
        oo299.setName("山楂树下350ML*15");
        oo299.setObjectId("b85f432aa2e24e85b26664a566bca5a7");
        oo299.setColor("ff6331");
        mockTable.add(oo299);

        ObjectMapPo oo300 = new ObjectMapPo();
        oo300.setTenantId(472252);
        oo300.setModelId("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
        oo300.setAppId("CRM");
        oo300.setApiName("ProductObj");
        oo300.setKey("山楂树下箱子_冠芳_1.25L");
        oo300.setName("山楂树下1.25L*6");
        oo300.setObjectId("2e6222d660b54e08afbf13d85c6c4e2c");
        oo300.setColor("7fbc9c");
        mockTable.add(oo300);


    }

    public List<ObjectMapPo> query(Integer tenantId, String modelId) {
        return mockTable.stream().filter(f -> f.getTenantId().equals(tenantId) && f.getModelId().equals(modelId)).collect(Collectors.toList());
    }
}
