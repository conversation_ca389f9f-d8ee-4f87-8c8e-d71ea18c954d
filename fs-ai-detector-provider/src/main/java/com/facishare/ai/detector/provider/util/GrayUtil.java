package com.facishare.ai.detector.provider.util;

import com.alibaba.fastjson.JSON;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/12/15 下午3:26
 */
public class GrayUtil {

    private static final String CONFIG_NAME = "gray-rel-fmcg";

    private static final Map<String,Object> GRAY_MAP= new HashMap<>();

    private static final List<String> MAP_KEY = Lists.newArrayList("AI_FACE_RECAPTURE","COIN_IMPORT_SPECIAL_USER","AI_FACE_RECAPTURE_MAP");

    static {
        ConfigFactory.getConfig(CONFIG_NAME, iConfig -> {
           // iConfig.get
            Set<String> set = new HashSet<>(GRAY_MAP.keySet());
            iConfig.getAll().forEach((k,v)->{
                if(MAP_KEY.contains(k))
                    GRAY_MAP.put(k,JSON.parseObject(v,HashMap.class));
                else
                    GRAY_MAP.put(k,v);
            });
            set.removeAll(iConfig.getAll().keySet());
            set.forEach(GRAY_MAP::remove);
        });
    }

    public static <T,R> Map<T,R> getMap(String key){
        if(GRAY_MAP.containsKey(key))
             return (Map<T,R>)(GRAY_MAP.get(key));
        return null;
    }

    public static String get(String key){
        if(GRAY_MAP.containsKey(key))
            return (String)(GRAY_MAP.get(key));
        return null;
    }
}
