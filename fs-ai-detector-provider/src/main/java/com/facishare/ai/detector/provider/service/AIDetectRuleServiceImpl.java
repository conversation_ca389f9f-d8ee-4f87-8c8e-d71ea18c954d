package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.arg.DeleteAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.GetAIDetectRuleListArg;
import com.facishare.ai.detector.api.dto.arg.QueryRulesByIdsArg;
import com.facishare.ai.detector.api.dto.arg.SaveAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.arg.UpdateAIDetectRuleArg;
import com.facishare.ai.detector.api.dto.result.DeleteAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleListResult;
import com.facishare.ai.detector.api.dto.result.GetAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.QueryRulesByIdsResult;
import com.facishare.ai.detector.api.dto.result.SaveAIDetectRuleResult;
import com.facishare.ai.detector.api.dto.result.UpdateAIDetectRuleResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.AIDetectRuleService;
import com.facishare.ai.detector.provider.dao.abstraction.AIDetectRuleDAO;
import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;
import com.facishare.ai.detector.provider.util.ConvertUtil;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service(value = "aiDetectRuleService")
public class AIDetectRuleServiceImpl implements AIDetectRuleService {

    private static final Logger log = LoggerFactory.getLogger(AIDetectRuleServiceImpl.class);

    @Resource
    private AIDetectRuleDAO aiDetectRuleDao;

    @Override
    public SaveAIDetectRuleResult saveRule(SaveAIDetectRuleArg arg) {
        log.info("start saveRule.arg:{}", arg);
        SaveAIDetectRuleResult result = new SaveAIDetectRuleResult();
        try {
            AIDetectRulePO po = ConvertUtil.convertToPo(arg.getRule());
            String id = aiDetectRuleDao.save(arg.getTenantId(), po);
            AIDetectRuleDto detectRuleDto = ConvertUtil.convertToDto(po);
            result.setRule(detectRuleDto);
            result.setSuccess(true);
        }catch (AiProviderException e){
           throw e;
        } catch (Exception e) {
            log.error("saveRule error:", e);
            result.setSuccess(false);
        }
        log.info("finish saveRule.result:{}", result);
        return result;
    }

    @Override
    public UpdateAIDetectRuleResult updateRule(UpdateAIDetectRuleArg arg) {
        log.info("start updateRule.arg:{}", arg);
        UpdateAIDetectRuleResult result = new UpdateAIDetectRuleResult();
        try {
            AIDetectRulePO dbPo = aiDetectRuleDao.getRuleById(arg.getTenantId(), arg.getRule().getId());
            AIDetectRulePO po = ConvertUtil.convertToPo(arg.getRule());
            po.setId(dbPo.getOriginalId());
            aiDetectRuleDao.update(arg.getTenantId(), arg.getUserId(), po);
            result.setRule(ConvertUtil.convertToDto(po));
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("updateRule error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        log.info("finish updateRule.result:{}", result);
        return result;
    }

    @Override
    public DeleteAIDetectRuleResult deleteRule(DeleteAIDetectRuleArg arg) {
        log.info("start deleteRule.arg:{}", arg);
        DeleteAIDetectRuleResult result = new DeleteAIDetectRuleResult();
        try {
            aiDetectRuleDao.delete(arg.getTenantId(), arg.getUserId(), arg.getRuleId());
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("deleteRule error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        log.info("finish deleteRule.result:{}", result);
        return result;
    }

    @Override
    public GetAIDetectRuleResult getRuleById(GetAIDetectRuleArg arg) {
        log.info("start getRuleById.arg:{}", arg);
        GetAIDetectRuleResult result = new GetAIDetectRuleResult();
        try {
            AIDetectRulePO po = aiDetectRuleDao.getRuleById(arg.getTenantId(), arg.getRuleId());
            if (po != null) {
                result.setRule(ConvertUtil.convertToDto(po));
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("getRuleById error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        log.info("finish getRuleById.result:{}", result);
        return result;
    }

    @Override
    public GetAIDetectRuleListResult getRuleList(GetAIDetectRuleListArg arg) {
        log.info("start getRuleList.arg:{}", arg);
        GetAIDetectRuleListResult result = new GetAIDetectRuleListResult();
        try {
            List<AIDetectRulePO> pos = aiDetectRuleDao.query(arg.getTenantId(), arg.getModelId());
            if (CollectionUtils.isNotEmpty(pos)) {
                result.setRuleList(pos.stream().map(ConvertUtil::convertToDto).collect(Collectors.toList()));
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("getRuleList error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        log.info("finish getRuleList.result:{}", result);
        return result;
    }

    @Override
    public QueryRulesByIdsResult queryRulesByIds(QueryRulesByIdsArg arg) {
        log.info("start queryRulesByIds.arg:{}", arg);
        QueryRulesByIdsResult result = new QueryRulesByIdsResult();
        try {
            if (CollectionUtils.isEmpty(arg.getRuleIds())) {
                result.setSuccess(true);
                return result;
            }
            
            List<AIDetectRulePO> pos = aiDetectRuleDao.queryRuleByIds(arg.getTenantId(), arg.getRuleIds());
            if (CollectionUtils.isNotEmpty(pos)) {
                result.setRules(pos.stream()
                        .map(ConvertUtil::convertToDto)
                        .collect(Collectors.toList()));
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("queryRulesByIds error:", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        log.info("finish queryRulesByIds.result:{}", result);
        return result;
    }
}
