package com.facishare.ai.detector.provider.util;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.SpringContextHolder;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-1  下午4:45
 */
public class FSHttpUtil {

    private static Logger log = LoggerFactory.getLogger(FSHttpUtil.class);

    private static final MediaType JSON_MEDIA = MediaType.parse("application/json;charset=utf-8");

    private static final MediaType JPG_MEDIA = MediaType.parse("image/jpg");

    private static final MediaType FORM_MEDIA = MediaType.parse("application/x-www-form-urlencoded");

    private static OkHttpSupport client ;

    private static final String CONFIG_NAME = "fs-fmcg-adapter-apis";
    private static final String ENTERPRISE_ID_HEADER = "x-fs-ei";
    private static final String EMPLOYEE_ID_HEADER = "x-fs-userInfo";

    static {
        client = SpringContextHolder.getBean(OkHttpSupport.class);
    }

    public static <T> Object post(String url, Map<String, String> headers, T data) throws AiProviderException {
        Request.Builder builder = new Request.Builder().url(url);
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        builder.addHeader("Content-Type", "application/json");
        RequestBody requestBody = RequestBody.create(JSON_MEDIA, JSON.toJSONString(data));
        Request request = builder.post(requestBody).build();
        return request(request);
    }


    public static Object get(String url, Map<String, String> headers) throws AiProviderException {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        headers.forEach(builder::addHeader);
        Request request = builder.build();
        return request(request);
    }

    private static Object request(Request request){
        return request(request,null);
    }

    private static Object  request(Request request,Class clazz){
        return client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws Exception {
                if (response.code() < 200 || response.code() > 299) {
                    String errorBuilder = "framework-http invoke error." +
                            "url:" +
                            request.url().toString() +
                            "." +
                            "method:" +
                            request.method() +
                            "." +
                            "headers:" +
                            JSON.toJSONString(request.headers().toMultimap().toString()) +
                            "." +
                            "code:" +
                            response.code() +
                            "." +
                            "message:" +
                            response.message() +
                            ".";
                    log.info(errorBuilder);
                    throw new AiProviderException("100500",errorBuilder);
                }
                if(response.body() == null){
                    log.info("response is empty.");
                    return null;
                }
                String body = response.body().string();

                if(clazz == null)
                    return JSON.parse(body);
                else
                    return JSON.parseObject(body, clazz);
            }
        });
    }

    public static Object form_post(String url, Map<String, String> headers, String data) throws AiProviderException {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        builder.addHeader("Content-type", "application/x-www-form-urlencoded");
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        RequestBody body = RequestBody.create(FORM_MEDIA, data);
        builder.post(body);
        Request request = builder.build();
        return request(request);
    }

    public static <T> Headers head(String url, Map<String, String> headers, T data) {
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        builder.addHeader("Content-Type", "application/json");
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(builder::addHeader);
        }
        RequestBody requestBody = RequestBody.create(JSON_MEDIA, JSON.toJSONString(data));
        Request request = builder.post(requestBody).build();
        return (Headers) client.syncExecute(request, new SyncCallback() {
            @Override
            public Object response(Response response) throws Exception {
                if (response.code() < 200 || response.code() > 299) {
                    String errorBuilder = "framework-http invoke error." +
                            "url:" +
                            request.url().toString() +
                            "." +
                            "method:" +
                            request.method() +
                            "." +
                            "headers:" +
                            JSON.toJSONString(request.headers().toMultimap().toString()) +
                            "." +
                            "code:" +
                            response.code() +
                            "." +
                            "message:" +
                            response.message() +
                            ".";
                    throw new AiProviderException("100500",errorBuilder);
                }
                if(response.body() == null){
                    log.info("response is empty.");
                    return null;
                }
                String body = response.body().string();
                return response.headers();
            }
        });
    }


    public static Map<String, Object> initMetadataHeader(Integer tenantId, Integer userId) {
        Map<String, Object> header = new HashMap<>(2);
        header.put(ENTERPRISE_ID_HEADER, tenantId.toString());
        header.put(EMPLOYEE_ID_HEADER, userId.toString());
        if (!Strings.isNullOrEmpty(System.getProperty("app.name"))) {
            header.put("x-fs-peer-name", System.getProperty("app.name"));
            header.put("x-peer-name", System.getProperty("app.name"));
        }
        return header;
    }

    public static Map<String, Object> initMetadataHeader(Integer tenantId) {
        Map<String, Object> header = new HashMap<>(2);
        header.put(ENTERPRISE_ID_HEADER, tenantId.toString());
        header.put(EMPLOYEE_ID_HEADER, "-10000");
        if (!Strings.isNullOrEmpty(System.getProperty("app.name"))) {
            header.put("x-fs-peer-name", System.getProperty("app.name"));
            header.put("x-peer-name", System.getProperty("app.name"));
        }
        return header;
    }

    public static String initUrl(String key) {
        return ConfigFactory.getConfig(CONFIG_NAME).get(key);
    }

    public static String initUrl(String key, Object... parameters) {
        String url = ConfigFactory.getConfig(CONFIG_NAME).get(key);
        return url != null ? String.format(url, parameters) : null;
    }

}
