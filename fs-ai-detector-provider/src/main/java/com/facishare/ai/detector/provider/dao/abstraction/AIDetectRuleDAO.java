package com.facishare.ai.detector.provider.dao.abstraction;

import java.util.List;

import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;

public interface AIDetectRuleDAO {

    List<AIDetectRulePO> query(Integer tenantId, String modelId);

    AIDetectRulePO update(Integer tenantId, Integer userId, AIDetectRulePO po);

    void delete(Integer tenantId, Integer userId, String id);

    String save(Integer tenantId, AIDetectRulePO po);

    AIDetectRulePO getRuleById(Integer tenantId, String ruleId);

    List<AIDetectRulePO> queryRuleByIds(Integer tenantId,List<String> ruleIds);
} 
