package com.facishare.ai.detector.provider.adapter.service.ocr.model;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/15 下午4:14
 */
public class FacadeDetectAO {

    private List<FacadeInfo> facades;

    @Data
    @ToString
    static class FacadeInfo {
        private Double score;
        private String name;
        private String brief;
    }
}
