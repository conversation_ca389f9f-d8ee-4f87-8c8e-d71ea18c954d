package com.facishare.ai.detector.provider.adapter.service.abstraction;

import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.face.model.FaceImage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-8  上午11:34
 */
public interface FaceDetectDetector {

    FaceComparisionResult faceComparison(String groupId, String userId, List<FaceImage> faces) throws AiProviderException;

    AddFaceResult addFace(String groupId,String userId,FaceImage face) throws AiProviderException;

    UpdateFaceResult updateFace(String groupId,String userId,FaceImage face) throws AiProviderException;

    ListFaceResult listFace(String groupId,String userId) throws AiProviderException;

    DeleteFaceResult deleteFace(String groupId,String userId,String faceId) throws AiProviderException;

    FaceDetectResult faceDetect(String groupId,String userId,FaceImage face) throws AiProviderException;
}
