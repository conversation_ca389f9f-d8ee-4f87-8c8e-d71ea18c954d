package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.ClassifyRecordPo;

import java.util.List;


/**
 * <AUTHOR>
 * @date 19-9-23  下午3:45
 */
public interface ClassifyRecordDao {

    ClassifyRecordPo get(String id);

    String save(ClassifyRecordPo po);

    ClassifyRecordPo get(Integer tenantId,String modelId,String srcImg);

    List<ClassifyRecordPo> query(Integer tenantId,String modelId,List<String>srcImg);
}
