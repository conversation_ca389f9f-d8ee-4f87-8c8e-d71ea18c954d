package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午2:08
 */
@Entity(value = "ai_account",noClassnameStored = true)
@Data
@ToString
public class AccountPO extends MongoPOBase {
    public static final String F_BALANCE="B";
    public static final String F_TYPE="T";
    public static final String F_CREATE_TIME="CT";
    public static final String F_UPDATE_TIME="UT";
    public static final String F_CREATOR="CTR";
    public static final String F_UPDATER="UD";
    public static final String F_IS_DELETED="ID";



    @Property(F_BALANCE)
    private Double balance;

    @Property(F_TYPE)
    private String type;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_UPDATE_TIME)
    private Long updateTime;

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_UPDATER)
    private Integer updater;

    @Property(F_IS_DELETED)
    private Boolean isDeleted;
}
