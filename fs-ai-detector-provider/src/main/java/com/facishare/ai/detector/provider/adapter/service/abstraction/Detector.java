package com.facishare.ai.detector.provider.adapter.service.abstraction;

import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.provider.dao.po.ModelPo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface Detector {

    /**
     * detect image
     *
     * @param arg
     * @param model
     * @param imageCache
     * @return
     * @throws AiProviderException
     */
    List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException;


    List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException;
}
