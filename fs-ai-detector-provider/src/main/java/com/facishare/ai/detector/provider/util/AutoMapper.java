package com.facishare.ai.detector.provider.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.ObjectDto;
import com.facishare.ai.detector.api.dto.PositionDto;
import com.facishare.ai.detector.api.dto.api.SaveDetectRecord;
import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.result.DetectByBase64Result;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordResult;
import com.facishare.ai.detector.provider.dao.po.DetectRecordPo;
import com.facishare.ai.detector.provider.dao.po.ObjectEntity;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.dao.po.PositionEntity;
import com.fs.fmcg.sdk.ai.adapter.contract.CommonDetect;
import com.fs.fmcg.sdk.ai.adapter.contract.GetModel;
import com.fs.fmcg.sdk.ai.contract.ObjectDTO;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AutoMapper {

    private static List<ObjectDto> convertToObjectDtoList(List<ObjectEntity> objects) {
        return objects == null ? Lists.newArrayList() : objects.stream().map(AutoMapper::convertToObjectDto).collect(Collectors.toList());
    }

    private static ObjectEntity toObjectEntity(String appId, Map<String, ObjectMapPo> objectMap, BoxDto box) {
        ObjectMapPo bizObject = objectMap.get(box.getName());
        PositionEntity position = new PositionEntity();
        if (box.getBox() != null && box.getBox().length >= 4) {
            position.setY(box.getBox()[0]);
            position.setX(box.getBox()[1]);
            position.setW(box.getBox()[3] - box.getBox()[1]);
            position.setH(box.getBox()[2] - box.getBox()[0]);
        }
        ObjectEntity obj = new ObjectEntity();
        obj.setAppId(appId);
        obj.setObjectType(bizObject.getApiName());
        obj.setObjectName(bizObject.getName());
        obj.setObjectId(bizObject.getObjectId());
        obj.setPosition(position);
        obj.setScore(box.getScore());
        obj.setColor(bizObject.getColor());
        obj.setUnit(bizObject.getUnit());
        obj.setComponents(box.getComponents());
        obj.setType(box.getType());
        obj.setKey(box.getName());
        obj.setIsRotated(box.getIsRotated());
        obj.setComponentEntities(translateObjectEntity(appId, box.getComponentEntities(), objectMap));
        obj.setIsFront(box.getIsFront());
        obj.setScene(box.getScene());
        obj.setShelf(box.getShelf());
        obj.setLayer(box.getLayer());
        obj.setSkuSn(box.getSkuSn());
        return obj;
    }

    private static List<ObjectEntity> translateObjectEntity(String appId, List<BoxDto> entities, Map<String, ObjectMapPo> objectMap) {

        if (entities == null)
            return null;
        List<ObjectEntity> results = Lists.newArrayList();
        entities.forEach(box -> results.add(toObjectEntity(appId, objectMap, box)));
        return results;
    }

    private static ObjectDto convertToObjectDto(ObjectEntity object) {
        ObjectDto dto = new ObjectDto();
        dto.setAppId(object.getAppId());
        dto.setObjectType(object.getObjectType());
        dto.setObjectName(object.getObjectName());
        dto.setObjectId(object.getObjectId());
        dto.setPosition(convertToPositionDto(object.getPosition()));
        dto.setScore(object.getScore());
        dto.setColor(object.getColor());
        dto.setUnit(object.getUnit());
        dto.setComponents(object.getComponents());
        dto.setIsFront(object.getIsFront());
        dto.setIsRotated(object.getIsRotated());
        dto.setComponentEntities(translateObjectDto(object.getComponentEntities()));
        dto.setType(object.getType());
        dto.setKey(object.getKey());
        dto.setScene(object.getScene());
        dto.setShelf(object.getShelf());
        dto.setLayer(object.getLayer());
        dto.setSkuSn(object.getSkuSn());
        dto.setExtraData(object.getExtraData());
        dto.setSubSkuCount(object.getSubSkuCount());
        return dto;
    }

    private static List<ObjectDto> translateObjectDto(List<ObjectEntity> entities) {
        return entities == null ? null : JSON.parseObject(JSON.toJSONString(entities), new TypeReference<List<ObjectDto>>() {
        });
    }

    public static List<ObjectEntity> toObjectEntityList(String appId, List<BoxDto> boxes, Map<String, ObjectMapPo> objectMap) {
        return boxes == null ?
                Lists.newArrayList() :
                boxes.stream()
                        .filter(box -> objectMap.containsKey(box.getName()))
                        .map(box -> AutoMapper.toObjectEntity(appId, objectMap, box))
                        .collect(Collectors.toList());
    }

    public static DetectArg convertDetectArg(BatchDetectArg arg, String path) {
        DetectArg detectArg = new DetectArg();
        detectArg.setTenantId(arg.getTenantId());
        detectArg.setUserId(arg.getUserId());
        detectArg.setTenantAccount(arg.getTenantAccount());
        detectArg.setAppId(arg.getAppId());
        detectArg.setModelId(arg.getModelId());
        detectArg.setPath(path);
        detectArg.setCreateProcessedImage(arg.getCreateProcessedImage());
        return detectArg;
    }


    private static PositionDto convertToPositionDto(PositionEntity position) {
        if (position == null) {
            return null;
        }
        PositionDto dto = new PositionDto();
        dto.setX(position.getX());
        dto.setY(position.getY());
        dto.setW(position.getW());
        dto.setH(position.getH());
        return dto;
    }

    public static DetectResult toDetectResult(DetectRecordPo record) {
        DetectResult dto = new DetectResult();

        dto.setOriginalPath(record.getOriginalPath());
        dto.setPath(record.getProcessedPath());
        dto.setProcessedPath(record.getProcessedPath());
        dto.setIsCache(false);
        dto.setObjects(AutoMapper.convertToObjectDtoList(record.getObjects()));
        return dto;
    }

    public static DetectResult toDetectResult(DetectRecordPo record, boolean isCache) {
        DetectResult dto = new DetectResult();
        dto.setOriginalPath(record.getOriginalPath());
        dto.setPath(record.getProcessedPath());
        dto.setProcessedPath(record.getProcessedPath());
        dto.setIsCache(isCache);
        dto.setObjects(AutoMapper.convertToObjectDtoList(record.getObjects()));
        return dto;
    }

    public static DetectByBase64Result toDetectByBase64Result(DetectRecordPo record) {
        DetectByBase64Result dto = new DetectByBase64Result();
        dto.setPath(record.getPath());
        dto.setOriginalPath(record.getOriginalPath());
        dto.setObjects(AutoMapper.convertToObjectDtoList(record.getObjects()));
        return dto;
    }

    public static QueryDetectRecordResult.Record toQueryDetectRecordResult(DetectRecordPo record) {
        QueryDetectRecordResult.Record dto = new QueryDetectRecordResult.Record();
        dto.setPath(record.getPath());
        dto.setOriginalPath(record.getOriginalPath());
        dto.setProcessedPath(record.getProcessedPath());
        dto.setObjects(AutoMapper.convertToObjectDtoList(record.getObjects()));
        dto.setExtraData(record.getExtraData());
        return dto;
    }

    public static BoxDto toBoxDto(CommonDetect.BoxDTO boxDTO) {
        BoxDto newBoxDto = new BoxDto();
        newBoxDto.setComponents(boxDTO.getComponents());
        newBoxDto.setScore(String.valueOf(boxDTO.getScore()));
        newBoxDto.setName(boxDTO.getName());
        newBoxDto.setIsFront(boxDTO.getIsFront());
        newBoxDto.setIsRotated(boxDTO.getIsRotated());
        newBoxDto.setComponentEntities(translateBoxDto(boxDTO.getComponentEntities()));
        newBoxDto.setType(boxDTO.getType());
        newBoxDto.setScene(boxDTO.getScene());
        newBoxDto.setShelf(boxDTO.getShelf());
        newBoxDto.setLayer(boxDTO.getLayer());
        newBoxDto.setSkuSn(boxDTO.getSkuSn());
        Double[] box = new Double[4];
        newBoxDto.setBox(box);
        box[1] = boxDTO.getLocation().getLeft();
        box[0] = boxDTO.getLocation().getTop();
        box[3] = boxDTO.getLocation().getWidth() + box[1];
        box[2] = boxDTO.getLocation().getHeight() + box[0];
        return newBoxDto;
    }

    private static List<BoxDto> translateBoxDto(List<CommonDetect.BoxDTO> entities) {

        if (entities == null)
            return null;
        List<BoxDto> results = Lists.newArrayList();
        entities.forEach(entity -> results.add(toBoxDto(entity)));
        return results;
    }

    public static CommonDetect.BoxDTO toCommonDetectBoxDTO(BoxDto boxDTO) {
        CommonDetect.BoxDTO newBoxDto = new CommonDetect.BoxDTO();
        newBoxDto.setComponents(boxDTO.getComponents());
        newBoxDto.setComponentEntities(translateCommonBoxDto(boxDTO.getComponentEntities()));
        newBoxDto.setType(boxDTO.getType());
        newBoxDto.setScore(Double.parseDouble(boxDTO.getScore()));
        newBoxDto.setName(boxDTO.getName());
        newBoxDto.setIsFront(boxDTO.getIsFront());
        newBoxDto.setIsRotated(boxDTO.getIsRotated());
        newBoxDto.setScene(boxDTO.getScene());
        newBoxDto.setShelf(boxDTO.getShelf());
        newBoxDto.setLayer(boxDTO.getLayer());
        newBoxDto.setSkuSn(boxDTO.getSkuSn());
        CommonDetect.LocationDTO locationDTO = new CommonDetect.LocationDTO();
        locationDTO.setLeft(boxDTO.getBox()[1]);
        locationDTO.setTop(boxDTO.getBox()[0]);
        locationDTO.setWidth(boxDTO.getBox()[3] - boxDTO.getBox()[1]);
        locationDTO.setHeight(boxDTO.getBox()[2] - boxDTO.getBox()[0]);
        newBoxDto.setLocation(locationDTO);
        return newBoxDto;
    }


    private static List<CommonDetect.BoxDTO> translateCommonBoxDto(List<BoxDto> entities) {

        if (entities == null)
            return null;
        List<CommonDetect.BoxDTO> results = Lists.newArrayList();
        entities.forEach(boxDTO -> results.add(toCommonDetectBoxDTO(boxDTO)));
        return results;
    }


    public static List<BoxDto> toBoxDtoList(List<CommonDetect.BoxDTO> boxDTOs) {
        List<BoxDto> boxes = Lists.newArrayList();
        boxDTOs.forEach(v -> boxes.add(toBoxDto(v)));
        return boxes;
    }

    public static List<CommonDetect.BoxDTO> toCommonDetectBoxDTOList(List<BoxDto> boxDTOs) {
        List<CommonDetect.BoxDTO> boxes = Lists.newArrayList();
        boxDTOs.forEach(v -> boxes.add(toCommonDetectBoxDTO(v)));
        return boxes;
    }
}
