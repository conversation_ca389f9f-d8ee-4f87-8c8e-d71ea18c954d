package com.facishare.ai.detector.provider.adapter.service.detect;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PinLanDetectorImpl implements Detector {

    private static final Logger logger = LoggerFactory.getLogger(PinLanDetectorImpl.class);

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private TokenFactory tokenFactory;

    @Override
    public List<BoxDto> detect(DetectArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {

        String imageUrl = fileAdapter.createShareFile(arg.getTenantAccount(), 1000, arg.getPath().toLowerCase().contains(".jpg")?arg.getPath():arg.getPath()+".jpg");
        List<BoxDto> boxes = new ArrayList<>();
        Map<String,Object> data = new HashMap<>();
        data.put("image_url",imageUrl);
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json");
        headers.put("Authorization",tokenFactory.getToken(AppEnum.PINLAN.value()));
        JSONObject response = (JSONObject) HttpUtil.post(
                String.format(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.PINLAN_RETAIL_CUSTOMIZED),model.getKey()),
                headers,data);
        if(!response.getInteger("code").equals(200)){
            logger.info("PinLang object detect fail.imageUrl:{}",imageUrl);
            if(response.getJSONObject("data")!=null)
                throw new AiProviderException("500",response.getJSONObject("data").getString("error_description"));
            else
                  throw new AiProviderException("500","PinLang AI Detect Fail.");
        }else{
            JSONObject result = response.getJSONObject("data").getJSONObject("response_result");
            if(result!=null){
                if(result.containsKey("all_sku")){
                    result.getJSONArray("all_sku").forEach(sku->{
                        JSONObject skuObj = (JSONObject) sku;
                        if("others".equals(skuObj.getString("name"))||(model.getConfidence()!=null && skuObj.getDoubleValue("score")<model.getConfidence()))
                            return;
                        BoxDto detectItem = new BoxDto();
                        detectItem.setScore(skuObj.getString("score"));
                        detectItem.setName(skuObj.getString("name"));
                        List<Double> location = skuObj.getJSONArray("coordinate").toJavaList(Double.class);
                        Double[] size = new Double[4];
                        size[1] = location.get(0);
                        size[0] = location.get(1);
                        size[3] = location.get(2) ;
                        size[2] = location.get(3) ;
                        detectItem.setBox(size);
                        boxes.add(detectItem);
                    });
                }
            }
        }
        return boxes;
    }

    @Override
    public List<BoxDto> detectByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        return null;
    }
}
