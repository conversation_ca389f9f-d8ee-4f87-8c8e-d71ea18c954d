package com.facishare.ai.detector.provider.adapter.service.classfy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.codec.binary.Base64;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 19-8-1  下午4:36
 */
public class BaiduLocalClassifierImpl  implements Classifier {



    @Override
    public List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        JSONObject classifyArg = new JSONObject();
        classifyArg.put("img", Base64.encodeBase64String(imageCache));
        //"http://************:1234/classify"
        String url = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.LOCAL_CLASSIFICATION);
        JSONArray array = (JSONArray) HttpUtil.post(String.format(url,model.getKey()), null, classifyArg);
        List<ClassDto> classes = new ArrayList<>();
        if (array != null) {
            for (Object o : array) {
                JSONObject obj = (JSONObject) o;
                ClassDto dto = new ClassDto();
                dto.setClassName(obj.getString("label"));
                dto.setConfidence(obj.getDouble("confidence"));
                classes.add(dto);
            }
        }else {
            throw  new AiProviderException("AI","call local ai service fail.");
        }
        return classes;
    }
}
