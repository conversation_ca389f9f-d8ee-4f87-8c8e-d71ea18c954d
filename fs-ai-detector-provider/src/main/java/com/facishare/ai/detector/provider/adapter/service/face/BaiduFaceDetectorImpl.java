package com.facishare.ai.detector.provider.adapter.service.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.FaceAttributeDto;
import com.facishare.ai.detector.api.dto.PositionDto;
import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.abstraction.FaceDetectDetector;
import com.facishare.ai.detector.provider.adapter.service.face.model.FaceImage;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 19-11-8  下午3:45
 */
@SuppressWarnings("Duplicates")
public class BaiduFaceDetectorImpl implements FaceDetectDetector {


    private static Logger logger = LoggerFactory.getLogger(BaiduFaceDetectorImpl.class);

    @Resource
    private TokenFactory tokenFactory;

    private static Integer SPACE = 60 * 5;

    private static String REDIS_KEY = "AI_FACE_RETRY_TIMES_%s_%s";

    private static Integer MAX_TIME = 5;

    private static Map<String, String> tipsMap = new HashMap<>();

    static {
        tipsMap.put("223113", "请勿遮挡面部!");
        tipsMap.put("223114", "照片模糊,请勿晃动手机!");
        tipsMap.put("223115", "请到光线适宜的地方拍摄!");
        tipsMap.put("223116", "请勿遮挡面部!");
        tipsMap.put("223120", "请勿用照片或画纸来替代人脸!");
        tipsMap.put("223121", "请勿遮挡左眼!");
        tipsMap.put("223122", "请勿遮挡右眼!");
        tipsMap.put("223123", "请勿遮挡左脸颊!");
        tipsMap.put("223124", "请勿遮挡右脸颊!");
        tipsMap.put("223125", "请勿遮挡下巴!");
        tipsMap.put("223126", "请勿遮挡鼻子!");
        tipsMap.put("223127", "请勿遮挡嘴巴!");
        tipsMap.put("222202", "未能识别出人脸照");//图片中没有找到人脸，请重新识别!
        tipsMap.put("222203", "无法解析人脸，请请重新拍摄!");
    }


    public JSONObject faceDatabaseManage(FaceImage faceImage, String groupId, String userId, String action, String url, Map<String, ? extends Object> options) throws AiProviderException {
        JSONObject req = new JSONObject();
        try {

            req.put("group_id", groupId);
            req.put("user_id", userId);
            if (faceImage != null) {
                req.put("image", faceImage.getImage());
                req.put("image_type", faceImage.getType());
                if (faceImage.getQualityControl() != null) {
                    req.put("quality_control", faceImage.getQualityControl());
                }
                if (faceImage.getLivenessControl() != null) {
                    req.put("liveness_control", faceImage.getLivenessControl());
                }
            }
            req.put("action_type", action);
            if (options != null && !options.isEmpty())
                options.forEach(req::put);
            return (JSONObject) HttpUtil.post(url, null, req);
        } catch (Exception e) {
            logger.error("faceDatabaseManage:{}", e);
            throw e;
        }
    }

    @Override
    public FaceComparisionResult faceComparison(String groupId, String userId, List<FaceImage> faces) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/match?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        JSONObject face1 = new JSONObject();
        FaceImage faceImage1 = faces.get(0);
        face1.put("image", faceImage1.getImage());
        face1.put("image_type", faceImage1.getType());
        if (faceImage1.getFaceType() != null) {
            face1.put("face_type", faceImage1.getFaceType());
        }
        if (faceImage1.getQualityControl() != null) {
            face1.put("quality_control", faceImage1.getQualityControl());
        }
        if (faceImage1.getLivenessControl() != null) {
            face1.put("liveness_control", faceImage1.getLivenessControl());
        }
        JSONObject face2 = new JSONObject();
        FaceImage faceImage2 = faces.get(1);
        face2.put("image", faceImage2.getImage());
        face2.put("image_type", faceImage2.getType());
        if (faceImage2.getFaceType() != null) {
            face2.put("face_type", faceImage2.getFaceType());
        }
        if (faceImage2.getQualityControl() != null) {
            face2.put("quality_control", faceImage2.getQualityControl());
        }
        if (faceImage2.getLivenessControl() != null) {
            face2.put("liveness_control", faceImage2.getLivenessControl());
        }
        List<JSONObject> list = Lists.newArrayList(face1, face2);
        FaceComparisionResult result = new FaceComparisionResult();

        JSONObject res = (JSONObject) HttpUtil.post(url, null, list);
        if (userId.equals("-10000")) {
            try (FileOutputStream o = new FileOutputStream(new File("faceCompare.json"))) {
                o.write(JSON.toJSONBytes(list));
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        logger.info("faceComparison:resFromBaiDu:{},userId:{},groupId:{}", res, userId, groupId);
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("faceComparison  fail.groupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            if (tipsMap.containsKey(res.getString("error_code"))) {
                result.setMsg(tipsMap.get(res.getString("error_code")));
            } else {
                result.setMsg(res.getString("error_msg"));
                logger.info("groupId:{},userId:{},arg:{},msg:{}", groupId, userId, faces, res.getString("error_msg"));
            }
            return result;
        }
        result.setScore(res.getJSONObject("result").getDouble("score"));
        result.setFaceIds(res.getJSONObject("result").getJSONArray("face_list").stream().map(v -> ((JSONObject) v).getString("face_token")).collect(Collectors.toList()));
        return result;
    }

    @Override
    public AddFaceResult addFace(String groupId, String userId, FaceImage face) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/add?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        JSONObject res = faceDatabaseManage(face, groupId, userId, "REPLACE", url, null);
        logger.info("addFace:resFromBaiDu:{},userId:{},groupId:{}", res, userId, groupId);
        AddFaceResult result = new AddFaceResult();
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("app faceId to group fail.groupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            if (tipsMap.containsKey(res.getString("error_code"))) {
                result.setMsg(tipsMap.get(res.getString("error_code")));
            } else {
                result.setMsg(res.getString("error_msg"));
                logger.info("groupId:{},userId:{},arg:{},msg:{}", groupId, userId, face, res.getString("error_msg"));
            }
            return result;
        }
        result.setFaceId(res.getJSONObject("result").getString("face_token"));
        JSONObject location = res.getJSONObject("result").getJSONObject("location");
        double[] lo = new double[5];
        lo[0] = location.getDouble("left");
        lo[1] = location.getDouble("top");
        lo[2] = location.getDouble("width") + lo[0];
        lo[3] = location.getDouble("height") + lo[1];
        if(location.containsKey("rotation")){
            lo[4] = location.getDouble("rotation");
        }
        result.setLocation(lo);
        return result;
    }

    @Override
    public UpdateFaceResult updateFace(String groupId, String userId, FaceImage face) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/user/update?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        JSONObject res = faceDatabaseManage(face, groupId, userId, "REPLACE", url, null);
        UpdateFaceResult result = new UpdateFaceResult();
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("update faceId to group fail.groupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            result.setMsg(res.getString("error_msg"));
            return result;
        }
        result.setFaceId(res.getJSONObject("result").getString("face_token"));
        JSONObject location = res.getJSONObject("result").getJSONObject("location");
        double[] lo = new double[5];
        lo[0] = location.getDouble("left");
        lo[1] = location.getDouble("top");
        lo[2] = location.getDouble("width") + lo[0];
        lo[3] = location.getDouble("height") + lo[1];
        lo[4] = location.getDouble("rotation");
        result.setLocation(lo);
        return result;
    }

    @Override
    public ListFaceResult listFace(String groupId, String userId) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/face/getlist?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        JSONObject res = faceDatabaseManage(null, groupId, userId, "XJBLX", url, null);
        ListFaceResult result = new ListFaceResult();
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("getlist  from group failgroupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            result.setMsg(res.getString("error_msg"));
            return result;
        }
        result.setFaceIds(new ArrayList<>());
        if (res.containsKey("result")) {
            res = res.getJSONObject("result");
            res.getJSONArray("face_list").forEach(v -> result.getFaceIds().add(((JSONObject) v).getString("face_token")));
        }
        return result;
    }

    @Override
    public DeleteFaceResult deleteFace(String groupId, String userId, String faceId) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/faceset/face/delete?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        Map<String, String> op = new HashMap<>(2);
        op.put("face_token", faceId);
        JSONObject res = faceDatabaseManage(null, groupId, userId, "XJBLX", url, op);
        DeleteFaceResult result = new DeleteFaceResult();
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("delete  from group fail.groupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            result.setMsg(res.getString("error_msg"));
            return result;
        }
        return result;
    }

    @Override
    public FaceDetectResult faceDetect(String groupId, String userId, FaceImage face) throws AiProviderException {
        String url = "https://aip.baidubce.com/rest/2.0/face/v3/detect?access_token=" + tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_FACE.value());
        Map<String, Object> data = new HashMap<>(8);
        data.put("image", face.getImage());
        data.put("image_type", face.getType());
        data.put("face_field", "age,beauty,expression,face_shape,gender,glasses,landmark,landmark150,race,quality,eye_status,emotion,face_type,mask,spoofing");
        data.put("max_face_num", 5);
        if (!Strings.isNullOrEmpty(face.getFaceType())) {
            data.put("face_type", face.getFaceType());
        } else {
            data.put("face_type", "LIVE");
        }
        data.put("liveness_control", "NONE");
        JSONObject res = (JSONObject) HttpUtil.post(url, null, data);
        FaceDetectResult result = new FaceDetectResult();
        if (res.containsKey("error_code") && res.getInteger("error_code") != 0) {
            logger.info("delete  from group fail.groupId:{},userId:{},err:{}", groupId, userId, res.getString("error_msg"));
            result.setErrorCode(res.getInteger("error_code").toString());
            if (tipsMap.containsKey(res.getString("error_code"))) {
                result.setMsg(tipsMap.get(res.getString("error_code")));
            } else {
                result.setMsg(res.getString("error_msg"));
            }
            return result;
        }
        result.setFaceCnt(res.getJSONObject("result").getInteger("face_num"));
        result.setFaces(new ArrayList<>());
        res.getJSONObject("result").getJSONArray("face_list").forEach(faceTmp -> {
            JSONObject bdFace = (JSONObject) faceTmp;
            FaceAttributeDto faceAttributeDto = new FaceAttributeDto();
            result.getFaces().add(faceAttributeDto);
            faceAttributeDto.setFaceToken(bdFace.getString("face_token"));
            PositionDto positionDto = new PositionDto();
            positionDto.setRotation(bdFace.getJSONObject("location").getInteger("rotation"));
            positionDto.setX(bdFace.getJSONObject("location").getDouble("left"));
            positionDto.setY(bdFace.getJSONObject("location").getDouble("top"));
            positionDto.setW(bdFace.getJSONObject("location").getDouble("width"));
            positionDto.setH(bdFace.getJSONObject("location").getDouble("height"));
            faceAttributeDto.setPosition(positionDto);
            faceAttributeDto.setConfidence(bdFace.getDouble("face_probability"));
            if (bdFace.getJSONObject("angle") != null) {
                JSONObject bdAngle = bdFace.getJSONObject("angle");
                FaceAttributeDto.Angle angle = new FaceAttributeDto.Angle();
                angle.setYaw(bdAngle.getDouble("yaw"));
                angle.setPitch(bdAngle.getDouble("pitch"));
                angle.setPitch(bdAngle.getDouble("roll"));
                faceAttributeDto.setAngle(angle);
            }
            faceAttributeDto.setAge(bdFace.getInteger("age"));
            faceAttributeDto.setBeauty(bdFace.getInteger("beauty"));
            if (bdFace.getJSONObject("expression") != null) {
                // JSONObject bdExpression
                switch (bdFace.getJSONObject("expression").getString("type")) {
                    case "smile":
                        faceAttributeDto.setExpression(FaceAttributeDto.Expression.SMILE.value());
                        break;
                    case "laugh":
                        faceAttributeDto.setExpression(FaceAttributeDto.Expression.LAUGH.value());
                        break;
                    default:
                        faceAttributeDto.setExpression(FaceAttributeDto.Expression.NONE.value());
                }
            }
            if (bdFace.getJSONObject("face_shape") != null) {
                // JSONObject bdExpression
                switch (bdFace.getJSONObject("face_shape").getString("type")) {
                    case "square":
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.SQUARE.value());
                        break;
                    case "triangle":
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.TRIANGLE.value());
                        break;
                    case "oval":
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.OVAL.value());
                        break;
                    case "heart":
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.HEART.value());
                        break;
                    case "round":
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.ROUND.value());
                        break;
                    default:
                        faceAttributeDto.setFaceShape(FaceAttributeDto.Shape.NONE.value());
                }
            }
            if (bdFace.getJSONObject("gender") != null) {
                // JSONObject bdExpression
                faceAttributeDto.setGender(bdFace.getJSONObject("gender").getString("type").equals(FaceAttributeDto.Gender.MALE.value()) ?
                        FaceAttributeDto.Gender.MALE.value() : FaceAttributeDto.Gender.FEMALE.value());
            }
            if (bdFace.getJSONObject("glasses") != null) {
                // JSONObject bdExpression
                switch (bdFace.getJSONObject("glasses").getString("type")) {
                    case "sun":
                        faceAttributeDto.setGlasses(FaceAttributeDto.Glasses.SUN.value());
                        break;
                    case "common":
                        faceAttributeDto.setGlasses(FaceAttributeDto.Glasses.COMMON.value());
                        break;
                    default:
                        faceAttributeDto.setGlasses(FaceAttributeDto.Glasses.NONE.value());
                }
            }
            if (bdFace.getJSONObject("eye_status") != null) {
                // JSONObject bdExpression
                faceAttributeDto.setLeftEyeStatus(bdFace.getJSONObject("eye_status").getDouble("left_eye"));
                faceAttributeDto.setRightEyeStatus(bdFace.getJSONObject("eye_status").getDouble("right_eye"));
            }
            if (bdFace.getJSONObject("emotion") != null) {
                // JSONObject bdExpression
                switch (bdFace.getJSONObject("emotion").getString("type")) {
                    case "angry":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.ANGRY.value());
                        break;
                    case "disgust":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.DISGUST.value());
                        break;
                    case "fear":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.FEAR.value());
                        break;
                    case "happy":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.HAPPY.value());
                        break;
                    case "sad":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.SAD.value());
                        break;
                    case "surprise":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.SURPRISE.value());
                        break;
                    case "neutral":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.NEUTRAL.value());
                        break;
                    case "pouty":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.POUTY.value());
                        break;
                    case "grimace":
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.GRIMACE.value());
                        break;
                    default:
                        faceAttributeDto.setEmotion(FaceAttributeDto.Emotion.NONE.value());
                }
            }
            if (bdFace.getJSONObject("face_type") != null) {
                faceAttributeDto.setFaceType(bdFace.getJSONObject("face_type").getString("type"));
            }
            if (bdFace.getJSONObject("mask") != null) {
                faceAttributeDto.setMask(bdFace.getJSONObject("mask").getInteger("type"));
            }
            if (bdFace.getDouble("spoofing") != null) {
                faceAttributeDto.setSpoofing(bdFace.getDoubleValue("spoofing") > 0.00109);
            }
            if (bdFace.getJSONObject("quality") != null) {
                JSONObject quality = bdFace.getJSONObject("quality");
                FaceAttributeDto.Quality faceQuality = new FaceAttributeDto.Quality();
                faceQuality.setCompleteness(quality.getInteger("completeness"));
                faceQuality.setIllumination(quality.getDouble("illumination"));
                faceQuality.setBlur(quality.getDouble("blur"));
                if (quality.getJSONObject("occlusion") != null) {
                    JSONObject occlusion = quality.getJSONObject("occlusion");
                    faceQuality.setChin(occlusion.getDouble("chin_contour"));
                    faceQuality.setLeftEye(occlusion.getDouble("left_eye"));
                    faceQuality.setRightEye(occlusion.getDouble("right_eye"));
                    faceQuality.setNose(occlusion.getDouble("nose"));
                    faceQuality.setMouth(occlusion.getDouble("mouth"));
                    faceQuality.setLeftCheek(occlusion.getDouble("left_cheek"));
                    faceQuality.setRightCheek(occlusion.getDouble("right_cheek"));
                }
                faceAttributeDto.setQuality(faceQuality);
            }
        });
        return result;
    }
}
