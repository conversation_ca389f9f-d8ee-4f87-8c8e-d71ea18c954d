package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.AccountPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午2:42
 */
public interface AccountDAO {

    String insert(AccountPO po);

    AccountPO get(String id);

    List<AccountPO> query(int tenantId);

    AccountPO query(int tenantId,String type);

    void riseBalance(String id,Double amount);
}
