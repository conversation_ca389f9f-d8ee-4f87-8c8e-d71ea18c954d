package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.dao.abstraction.AIDetectRuleDAO;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class AIDetectRuleDaoImpl extends DaoBase<AIDetectRulePO> implements AIDetectRuleDAO {

    @Override
    public List<AIDetectRulePO> query(Integer tenantId, String modelId) {
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(AIDetectRulePO.F_TENANT_ID).equal(tenantId);
        query.field(AIDetectRulePO.F_MODEL_ID).equal(modelId);
        query.field(AIDetectRulePO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    @Override
    public AIDetectRulePO update(Integer tenantId, Integer userId, AIDetectRulePO po) {
        checkNameExist(tenantId, po.getModelId(), po.getName(), po.getUniqueId());
        checkApiNameExist(tenantId, po.getModelId(), po.getApiName(), po.getUniqueId());
        po.setLastModifier(userId);
        po.setLastModifyTime(System.currentTimeMillis());
        dbContext.save(po);
        return po;
    }

    @Override
    public void delete(Integer tenantId, Integer userId, String id) {
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(AIDetectRulePO.F_TENANT_ID).equal(tenantId);
        query.or(query.criteria("_id").equal(new ObjectId(id)),
                query.criteria(AIDetectRulePO.F_UNIQUE_ID).equal(id));

        UpdateOperations<AIDetectRulePO> updateOperations = dbContext.createUpdateOperations(AIDetectRulePO.class);
        updateOperations.set(AIDetectRulePO.F_LAST_MODIFIER, userId);
        updateOperations.set(AIDetectRulePO.F_LAST_MODIFY_TIME, System.currentTimeMillis());
        updateOperations.set(AIDetectRulePO.F_IS_DELETED, true);
        dbContext.update(query, updateOperations);
    }

    @Override
    public String save(Integer tenantId, AIDetectRulePO po) {
        checkNameExist(tenantId, po.getModelId(), po.getName(), po.getUniqueId());
        checkApiNameExist(tenantId, po.getModelId(), po.getApiName(), po.getUniqueId());
        po.setTenantId(tenantId);
        return save(po);
    }

    private void checkApiNameExist(Integer tenantId, String modelId, String apiName, String id) {
        if (Strings.isNullOrEmpty(apiName)) {
            return;
        }
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(AIDetectRulePO.F_TENANT_ID).equal(tenantId);
        query.field(AIDetectRulePO.F_API_NAME).equal(apiName);
        query.field(AIDetectRulePO.F_MODEL_ID).equal(modelId);
        query.field(AIDetectRulePO.F_IS_DELETED).equal(false);
        if(!Strings.isNullOrEmpty(id)){
            query.field(MongoPOBase.F_UNIQUE_ID).notEqual(id);
        }
        if (query.get() != null) {
            throw new AiProviderException("7124", "存在重复的规则。");
        }
    }

    private void checkNameExist(Integer tenantId, String modelId, String name, String id) {
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(AIDetectRulePO.F_TENANT_ID).equal(tenantId);
        query.field(AIDetectRulePO.F_NAME).equal(name);
        query.field(AIDetectRulePO.F_MODEL_ID).equal(modelId);
        query.field(AIDetectRulePO.F_IS_DELETED).equal(false);
        if(!Strings.isNullOrEmpty(id)){
            query.field(MongoPOBase.F_UNIQUE_ID).notEqual(id);
        }
        if (query.get() != null) {
            throw new AiProviderException("7123", "规则名称已存在。");
        }
    }

    @Override
    public AIDetectRulePO getRuleById(Integer tenantId, String ruleId) {
        Query<AIDetectRulePO> query = buildUniqueIdQuery(ruleId, AIDetectRulePO.class);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        query.field(AIDetectRulePO.F_IS_DELETED).equal(false);
        return query.get();
    }

    @Override
    public List<AIDetectRulePO> queryRuleByIds(Integer tenantId, List<String> ruleIds) {
        Query<AIDetectRulePO> query = dbContext.createQuery(AIDetectRulePO.class);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        query.field(MongoPOBase.F_IS_DELETED).equal(false);
        query.or(query.criteria("_id").in(ruleIds.stream().map(ObjectId::new).collect(Collectors.toList())), query.criteria(AIDetectRulePO.F_UNIQUE_ID).in(ruleIds));
        return query.asList();
    }


} 