package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.abstraction.FaceInfoDao;
import com.facishare.ai.detector.provider.dao.po.FaceInfoPo;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-11-11  下午5:28
 */
public class FaceInfoDaoImpl extends DaoBase<FaceInfoPo> implements FaceInfoDao {

    @Override
    public String save(FaceInfoPo po) {
        return dbContext.save(po).getId().toString();
    }

    @Override
    public void update(FaceInfoPo po) {
        Query<FaceInfoPo> query = dbContext.createQuery(FaceInfoPo.class);
        query.field(FaceInfoPo.F_GROUP_ID).equal(po.getGroupId());
        query.field((FaceInfoPo.F_USER_ID)).equal(po.getUserId());
        query.field(FaceInfoPo.F_PATH).equal(po.getPath());
        UpdateOperations<FaceInfoPo> updateOperations = dbContext.createUpdateOperations(FaceInfoPo.class);
        updateOperations.set(FaceInfoPo.F_FACE_ID,po.getFaceId());
        updateOperations.set(FaceInfoPo.F_SCENE,po.getScene());
        dbContext.update(query,updateOperations);
    }

    @Override
    public FaceInfoPo query(String groupId, String userId) {
        Query<FaceInfoPo> query = dbContext.createQuery(FaceInfoPo.class);
        query.field(FaceInfoPo.F_GROUP_ID).equal(groupId);
        query.field((FaceInfoPo.F_USER_ID)).equal(userId);
        return query.get();
    }

    @Override
    public FaceInfoPo query(String groupId, String userId, String path) {
        Query<FaceInfoPo> query = dbContext.createQuery(FaceInfoPo.class);
        query.field(FaceInfoPo.F_GROUP_ID).equal(groupId);
        query.field((FaceInfoPo.F_USER_ID)).equal(userId);
        query.field(FaceInfoPo.F_PATH).equal(path);
        return query.get();
    }

    @Override
    public void delete(String groupId, String userId) {
        Query<FaceInfoPo> query = dbContext.createQuery(FaceInfoPo.class);
        query.field(FaceInfoPo.F_GROUP_ID).equal(groupId);
        query.field(FaceInfoPo.F_USER_ID).equal(userId);
        dbContext.delete(query);
    }



}
