package com.facishare.ai.detector.provider.dao.abstraction;


import com.facishare.ai.detector.provider.dao.po.AccountDetailPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午2:42
 */
public interface AccountDetailDAO {

    String insert(AccountDetailPO po);

    AccountDetailPO get(String id);

    List<AccountDetailPO> query(int tenantId);

    List<AccountDetailPO>  query(int tenantId,String type);
}
