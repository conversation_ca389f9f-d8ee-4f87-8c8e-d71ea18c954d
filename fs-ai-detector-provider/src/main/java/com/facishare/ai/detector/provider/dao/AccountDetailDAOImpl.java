package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.AccountDetailDAO;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.po.AccountDetailPO;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午3:13
 */
@Repository
public class AccountDetailDAOImpl extends DaoBase<AccountDetailPO> implements AccountDetailDAO {

    @Override
    public String insert(AccountDetailPO po) {
        po.setCreateTime(System.currentTimeMillis());
        po.setCreator(po.getCreator()==null?-10000:po.getCreator());
        po.setIsDeleted(false);
        po.setAmount(po.getAmount()==null?0D:po.getAmount());
        return dbContext.save(po).getId().toString();
    }

    @Override
    public AccountDetailPO get(String id) {
        return buildUniqueIdQuery(id, AccountDetailPO.class).get();
    }

    @Override
    public List<AccountDetailPO> query(int tenantId) {
        Query<AccountDetailPO> query = dbContext.createQuery(AccountDetailPO.class);
        query.field(AccountDetailPO.F_TENANT_ID).equal(tenantId);
        query.field(AccountDetailPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    @Override
    public List<AccountDetailPO> query(int tenantId, String type) {
        Query<AccountDetailPO> query = dbContext.createQuery(AccountDetailPO.class);
        query.field(AccountDetailPO.F_TENANT_ID).equal(tenantId);
        query.field(AccountDetailPO.F_TYPE).equal(type);
        query.field(AccountDetailPO.F_IS_DELETED).equal(false);
        return query.asList();
    }
}
