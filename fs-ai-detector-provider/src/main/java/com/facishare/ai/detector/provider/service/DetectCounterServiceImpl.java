package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.arg.QueryCountByTimeArg;
import com.facishare.ai.detector.api.dto.result.QueryCountByTimeResult;
import com.facishare.ai.detector.api.dto.vo.DetectCounterDto;
import com.facishare.ai.detector.api.service.DetectCounterService;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.po.DetectCounterPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/3/24 下午5:25
 */
public class DetectCounterServiceImpl implements DetectCounterService {

    @Resource
    private DetectCounterDao detectCounterDao;

    private static final Logger log = LoggerFactory.getLogger(DetectCounterServiceImpl.class);

    @Override
    public QueryCountByTimeResult queryCountByTime(QueryCountByTimeArg arg) {
        QueryCountByTimeResult result = new QueryCountByTimeResult();
        try {
            DetectCounterPo po =detectCounterDao.sum(arg.getTenantId(),arg.getService(),arg.getStart(),arg.getEnd());
            if(po!=null){
                DetectCounterDto dto = new DetectCounterDto();
                BeanUtils.copyProperties(po,dto);
                result.setRecord(dto);
            }
        }catch (Exception e){
            result.setErrorMsg("1001");
            result.setErrorMsg(e.getMessage());
            log.error("query count fail . arg:{},e:{}",arg,e);
        }
        return result;
    }
}
