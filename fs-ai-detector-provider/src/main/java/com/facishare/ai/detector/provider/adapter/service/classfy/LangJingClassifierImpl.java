package com.facishare.ai.detector.provider.adapter.service.classfy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Classifier;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.facishare.fsc.api.model.CreateFileShareId;
import com.facishare.fsc.api.service.SharedFileService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 19-8-16  下午7:37
 */
@SuppressWarnings("Duplicates")
public class LangJingClassifierImpl implements Classifier {



    @Resource
    private FileAdapter fileAdapter;

    private static final Logger log = LoggerFactory.getLogger(LangJingClassifierImpl.class);



    private JSONObject submit(String url,JSONObject req) throws AiProviderException {

        return (JSONObject) HttpUtil.post(url, null,req);
    }

    public JSONObject query(String url,JSONObject req) throws AiProviderException {
        Map<String, String> header = new HashMap<>();
        return (JSONObject) HttpUtil.post(url, header,req);
    }

    private boolean isRequestSuccess(JSONObject object) {
        return object.get("retStatus") != null && object.getJSONObject("retStatus").getString("retCode").equals("0");
    }


    @Override
    public List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException {
        List<ClassDto> classDtos = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("companyId","fxiaoke");
        String id = System.currentTimeMillis()+"."+arg.getTenantId()+"."+arg.getUserId();
        request.put("businessDataParamList", Lists.newArrayList("recapture",id));
        List<JSONObject> groupList = new ArrayList<>();
        request.put("pictureGroupList",groupList);
        JSONObject picGroup = new JSONObject();
        groupList.add(picGroup);
        picGroup.put("groupId",id);
        List<JSONObject> picList = new ArrayList<>();
        picGroup.put("groupPictureList",picList);
        JSONObject pic = new JSONObject();
        pic.put("id",arg.getPath());
        String path = fileAdapter.createShareFile(arg.getTenantAccount(), Integer.valueOf(arg.getUserId()), arg.getPath());
        pic.put("url",path);
        picList.add(pic);
        int interval = 1000;

        JSONObject submitRst = submit(ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.LANGJING_IMAGE_SUBMIT), request);
        if (isRequestSuccess(submitRst)) {
            JSONObject response = submitRst.getJSONObject("retData");
            String responseId;
            if (response != null) {
                responseId = response.getString("responseId");
                String queryUrl = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.LANGJING_IMAGE_QUERY);
                JSONObject req = new JSONObject();
                JSONObject queryData = new JSONObject();
                queryData.put("companyId","fxiaoke");
                queryData.put("responseId",responseId);
                req.put("reqobj",queryData);
                int seed = 20;
                while (seed > 0) {
                    JSONObject rst = query(queryUrl, req);
                    if (isRequestSuccess(rst)&&!rst.getJSONObject("retData").getJSONArray("aiResult").isEmpty()) {
                        JSONObject obj = rst.getJSONObject("retData");

                        log.info("query finished : {} ", obj);

                        if(obj.getJSONArray("aiResult")!=null){
                            JSONArray array = obj.getJSONArray("aiResult");
                            JSONObject imageRst =  (JSONObject)array.get(0);
                            ClassDto dto = new ClassDto();
                            dto.setClassName("recapture");
                            dto.setConfidence(imageRst.getDouble("remakeScore"));
                            classDtos.add(dto);
                            return  classDtos;
                        }else {
                            log.info("query fail requestId:{}",responseId);
                        }
                    } else {

                        log.info("query fail. rst : {}", rst);
                    }

                    seed = seed - 1;

                    try {
                        Thread.sleep(interval);
                    } catch (InterruptedException e) {
                        log.error("time sleep err in query langjing interface.");
                        throw new AiProviderException("AI","time sleep err in query langjing interface.");
                    }
                    if(seed == 0){
                        throw new AiProviderException("AI","query fail!");
                    }
                }
            }
        } else {
            log.error("summit task fail. rst : {}", submitRst);
            throw new AiProviderException("AI","summit task fail.");
        }
        return classDtos;
    }
}
