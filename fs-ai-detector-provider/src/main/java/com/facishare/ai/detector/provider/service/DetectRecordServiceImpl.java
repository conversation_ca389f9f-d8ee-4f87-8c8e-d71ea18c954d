package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.arg.DeleteDetectRecordByTenantIdArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordArg;
import com.facishare.ai.detector.api.dto.arg.QueryDetectRecordCountArg;
import com.facishare.ai.detector.api.dto.result.DeleteDetectedRecordByTenantIdResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordCountResult;
import com.facishare.ai.detector.api.dto.result.QueryDetectRecordResult;
import com.facishare.ai.detector.api.service.DetectRecordService;
import com.facishare.ai.detector.provider.dao.abstraction.DetectRecordDao;
import com.facishare.ai.detector.provider.dao.po.DetectRecordPo;
import com.facishare.ai.detector.provider.util.AutoMapper;
import com.facishare.ai.detector.provider.util.CommonUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.base.Strings;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/31 下午5:58
 */
@Slf4j
@Component
public class DetectRecordServiceImpl implements DetectRecordService {

    @Resource
    private DetectRecordDao detectRecordDao;

    @Override
    public QueryDetectRecordResult queryDetectRecord(QueryDetectRecordArg arg) {
        Set<String> relatedPaths = CommonUtil.queryRelatedPath(arg.getPath());
        DetectRecordPo recordPo = null;
        if (Strings.isNullOrEmpty(arg.getModelId())) {
            List<DetectRecordPo> pos = detectRecordDao.queryByPath(arg.getTenantId(), relatedPaths);
            if (!pos.isEmpty()) {
                recordPo = pos.get(0);
            }
        } else {
            recordPo = arg.getPathType() == 0 ? detectRecordDao.get(arg.getTenantId(), arg.getModelId(), relatedPaths) : detectRecordDao.queryByProcessPath(arg.getTenantId(), arg.getModelId(), relatedPaths);
        }
        QueryDetectRecordResult result = new QueryDetectRecordResult();
        if (recordPo != null) {
            result.setRecord(AutoMapper.toQueryDetectRecordResult(recordPo));
        }
        return result;
    }

    @Override
    public QueryDetectRecordCountResult queryDetectRecordCount(QueryDetectRecordCountArg arg) {
        QueryDetectRecordCountResult result = new QueryDetectRecordCountResult();
        result.setCount(detectRecordDao.count(arg.getTenantId(), arg.getModelId(), arg.getStart(), arg.getEnd()));
        return result;
    }

    @Override
    public DeleteDetectedRecordByTenantIdResult deleteDetectedRecordByTenantId(DeleteDetectRecordByTenantIdArg arg) {
        List<DetectRecordPo> records;
        DeleteDetectedRecordByTenantIdResult result = new DeleteDetectedRecordByTenantIdResult();
        result.setDeleteCount(0);
        RateLimiter rateLimiter = RateLimiter.create(10);
        while (!(records = detectRecordDao.queryByTenantId(arg.getTenantId(), arg.getMaxCreateTime())).isEmpty()) {
            if (GrayRelease.isAllow("fmcg", "disallow_delete_detected_record", arg.getTenantId())) {
                log.info("forbid delete detect record. tenantId:{}", arg.getTenantId());
                break;
            }
            if (arg.getMaxExecuteTime() != null && System.currentTimeMillis() > arg.getMaxExecuteTime()) {
                log.info("execute time limit");
                break;
            }
            rateLimiter.acquire();
            log.info("delete size:{}. first po:{}", records.size(), records.get(0));
            detectRecordDao.deleteByIds(arg.getTenantId(), records.stream().map(v -> v.getOriginalId().toString()).collect(Collectors.toList()));
            result.setDeleteCount(result.getDeleteCount() + records.size());
        }
        return result;
    }
}
