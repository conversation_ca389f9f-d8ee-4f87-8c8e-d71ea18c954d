package com.facishare.ai.detector.provider.dao.po;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 * @date 19-11-11  下午3:33
 */
@Data
@ToString
@Entity(value = "ai_face_info", noClassnameStored = true)
public class FaceInfoPo extends MongoPOBase {

    public static final String F_USER_ID = "UI";
    public static final String F_GROUP_ID = "GI";
    public static final String F_ID_CARD_NUMBER = "ICN";
    public static final String F_FACE_ID = "FI";
    public static final String F_SCENE = "S";
    public static final String F_PATH = "P";
    public static final String F_PLATE = "PT";

    @Property(F_USER_ID)
    private String userId;

    @Property(F_GROUP_ID)
    private String groupId;

    @Property(F_ID_CARD_NUMBER)
    private String idCardNumber;

    @Property(F_FACE_ID)
    private String faceId;

    @Property(F_PATH)
    private String path;

    /**
     * LIVE:表示生活照 ,IDCARD：表示身份证芯片照,WATERMARK：表示带水印证件照：,CERT：表示证件照片 默认生活照
     */
    @Property(F_SCENE)
    private String scene;

    @Property(F_PLATE)
    private String plate;
}
