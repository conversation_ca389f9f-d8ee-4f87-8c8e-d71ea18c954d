package com.facishare.ai.detector.provider.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.arg.DeleteDetectRecordByTenantIdArg;
import com.facishare.ai.detector.api.dto.result.DeleteDetectedRecordByTenantIdResult;
import com.facishare.ai.detector.api.service.DetectRecordService;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/8/29 14:56
 */
@Slf4j
@Component
@EnableScheduling
public class DeleteDetectRecordSchedule {

    @Resource
    private DetectRecordService detectRecordService;

    @Resource
    private MergeJedisCmd redisCmd;

    public static final ExecutorService POOL;

    static {
        AtomicInteger count = new AtomicInteger(0);
        POOL = new ThreadPoolExecutor(2, 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10), r -> new Thread(r, "thread-DeleteDetectRecordSchedule-" + count.getAndIncrement()));
    }

    @Scheduled(cron = "1 0 0 * * ? ")
    public void run() {
        log.info("start delete detected record schedule");
        String grayList = ConfigFactory.getConfig("fs-fmcg-sdk-apis").get("delete_detected_record_ei_map");
        if (!Strings.isNullOrEmpty(grayList)) {
            JSONObject ei2DayMap = JSON.parseObject(grayList);
            for (String tenantIdStr : ei2DayMap.keySet()) {
                POOL.execute(() -> {
                    Integer tenantId = Integer.valueOf(tenantIdStr);
                    Integer day = (Integer) ei2DayMap.getOrDefault(tenantIdStr,365);
                    String lockKey = "fmcg_ai_delete_detect_record:" + tenantId;
                    String value = UUID.randomUUID().toString();
                    LocalDateTime maxExecuteTime = LocalDateTime.now().withHour(6).withMinute(0).withSecond(0).withNano(0);
                    try {
                        if (!"OK".equalsIgnoreCase(redisCmd.set(lockKey, value, SetParams.setParams().nx().ex(60L)))) {
                            log.info("lock fail");
                            return;
                        }
                        TraceContext.get().setTraceId("DeleteDetectRecordSchedule-" + UUID.randomUUID());
                        DeleteDetectRecordByTenantIdArg arg = new DeleteDetectRecordByTenantIdArg();
                        arg.setTenantId(tenantId);
                        arg.setMaxCreateTime(System.currentTimeMillis() - 3600_000L * 24 * day);
                        arg.setMaxExecuteTime(maxExecuteTime.toEpochSecond(ZoneOffset.of("+8")) * 1000);
                        log.info("arg:{}", arg);
                        DeleteDetectedRecordByTenantIdResult result = detectRecordService.deleteDetectedRecordByTenantId(arg);
                        log.info("delete result:{}", result);
                    } catch (Exception e) {
                        log.info("", e);
                    } finally {
                        redisCmd.eval("if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end", Lists.newArrayList(lockKey), Lists.newArrayList(value));
                    }
                });
            }
        }
    }
}
