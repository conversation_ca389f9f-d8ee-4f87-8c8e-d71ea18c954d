package com.facishare.ai.detector.provider.service;

import com.facishare.ai.detector.api.dto.AIDetectRuleDto;
import com.facishare.ai.detector.api.dto.ModelDto;
import com.facishare.ai.detector.api.dto.ObjectMapDto;
import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.service.ModelService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.dao.OldModelDaoImpl;
import com.facishare.ai.detector.provider.dao.abstraction.AIDetectRuleDAO;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.AIDetectRulePO;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.util.ConvertUtil;
import com.facishare.ai.detector.provider.util.FmcgBeanUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 19-9-26  上午10:49
 */
public class ModelServiceImpl implements ModelService {

    private static final Logger log = LoggerFactory.getLogger(ModelServiceImpl.class);

    @Resource
    private ModelDao modelDao;

    @Resource
    private OldModelDaoImpl oldModelDao;

    @Resource
    private ObjectMapDao objectMapDao;

    @Resource
    private AIDetectRuleDAO aidetectRuleDao;

    @Override
    public BatchAddModelResult batchAddModel(BatchAddModelArg arg) {
        log.info("start batchAddModel.arg:{}", arg);
        BatchAddModelResult rst = new BatchAddModelResult();
        rst.setStatus(ConstantUtil.SUCCESS);
        try {
            List<ModelPo> list = new ArrayList<>();
            arg.getModels().forEach(v -> list.add(ConvertUtil.convertToPo(v)));
            modelDao.saveAll(list);
        } catch (Exception e) {
            log.error("err:{}", e);
            rst.setStatus(ConstantUtil.FAILURE);
            rst.setMsg(e.toString());
        }
        log.info("finish batchAddModel.arg:{}", arg);
        return rst;
    }

    @Override
    public GetModelsByTenantIdResult getModelsByTenantId(GetModelsByTenantIdArg arg) {
        GetModelsByTenantIdResult result = new GetModelsByTenantIdResult();
        List<ModelPo> pos = modelDao.query(arg.getTenantId());
        List<ModelDto> dtos = new ArrayList<>();
        result.setModels(dtos);
        if (!pos.isEmpty()) {
            pos.forEach(v -> dtos.add(ConvertUtil.convertToDto(v)));
        }
        return result;
    }

    @Override
    public SaveModelResult saveModel(SaveModelArg arg) {
        SaveModelResult result = new SaveModelResult();
        if(nameCheck(arg.getTenantId(), arg.getModel().getName(), arg.getModel().getUniqueId())) {
            result.setStatus("fmcg.ai.model.name.repeat");
            result.setMsg("模型的名字已存在。");
            return result;
        }
        ModelPo po = ConvertUtil.convertToPo(arg.getModel());
        result.setId(modelDao.save(po));
        return result;
    }

    private boolean nameCheck(Integer tenantId, String name, String id) {
        ModelPo po = modelDao.queryModelByName(tenantId, name, id);
        return po != null;
    }

    @Override
    public UpdateModelResult updateModel(UpdateModelArg arg) {
        modelDao.update(arg.getTenantId(), arg.getId(), arg.getUpdateFields());
        ModelPo po = modelDao.query(arg.getId(), arg.getTenantId());
        ModelDto modelDto = ConvertUtil.convertToDto(po);
        UpdateModelResult updateModelResult = new UpdateModelResult();
        updateModelResult.setModelDto(modelDto);
        return updateModelResult;
    }

    @Override
    public GetModelByIdResult getModelById(GetModelByIdArg arg) {
        ModelPo modelPo = null;
        if ((modelPo = oldModelDao.get(arg.getTenantId(), arg.getModelId())) == null) {
            if (!Strings.isNullOrEmpty(arg.getModelId()) && isMongoIdString(arg.getModelId())) {
                modelPo = modelDao.query(arg.getModelId(), arg.getTenantId());
            }
        }
        if (modelPo != null) {
            ModelDto modelDto = new ModelDto();
            FmcgBeanUtil.copyProperties(modelPo, modelDto);
            modelDto.setId(modelPo.getUniqueId());
            List<ObjectMapDto> objectMapDtos = getObjectMapDtosByModelId(arg.getModelId(), arg.getTenantId());
            List<AIDetectRuleDto> aiDetectRuleDtos = getAIDetectRuleDtosByModelId(arg.getModelId(), arg.getTenantId());
            return new GetModelByIdResult(modelDto, objectMapDtos, aiDetectRuleDtos);
        } else {
            return new GetModelByIdResult();
        }

    }

    private List<AIDetectRuleDto> getAIDetectRuleDtosByModelId(String modelId, Integer tenantId) {
        List<AIDetectRulePO> objectMapPos = aidetectRuleDao.query(tenantId, modelId);
        return objectMapPos.stream().map(v -> ConvertUtil.convertToDto(v)).collect(Collectors.toList());
    }

    private List<ObjectMapDto> getObjectMapDtosByModelId(String modelId, Integer tenantId) {
        List<ObjectMapPo> objectMapPos = objectMapDao.query(tenantId, modelId);
        return objectMapPos.stream().map(v -> ConvertUtil.convertToDto(v)).collect(Collectors.toList());
    }

    @Override
    public GetModelListResult getModelList(GetModelListArg arg) {
        List<ModelPo> pos = modelDao.query(arg.getTenantId(), arg.getScene());
        List<ModelDto> modelDtos = new ArrayList<>();
        pos.forEach(po -> {
            ModelDto modelDto = new ModelDto();
            FmcgBeanUtil.copyProperties(po, modelDto);
            modelDtos.add(modelDto);
        });
        return new GetModelListResult(modelDtos);
    }

    @Override
    public CopyModelAndMappingResult copyModelAndMapping(CopyModelAndMappingArg arg) {
        List<ModelPo> models = Strings.isNullOrEmpty(arg.getModelId())
                ? modelDao.query(arg.getFromTenantId())
                : Lists.newArrayList(modelDao.query(arg.getModelId(), arg.getFromTenantId()));

        List<ModelPo> newModels = models.stream()
                .filter(modelPo -> modelDao.query(modelPo.getUniqueId(), arg.getToTenantId()) == null)
                .map(modelPo -> {
                    ModelPo newModel = new ModelPo();
                    FmcgBeanUtil.copyProperties(modelPo, newModel);
                    newModel.setId(null);
                    newModel.setTenantId(arg.getToTenantId());
                    return newModel;
                })
                .collect(Collectors.toList());

        modelDao.saveAll(newModels);
        List<ObjectMapPo> objectMapPos = new ArrayList<>();
        models.forEach(model -> {
            List<ObjectMapPo> all = objectMapDao.query(arg.getFromTenantId(), model.getUniqueId());
            Lists.partition(all, 50).forEach(parts -> {
                Set<String> containIds = objectMapDao.checkContainsIds(arg.getToTenantId(), parts.stream().map(MongoPOBase::getUniqueId).filter(Objects::nonNull).collect(Collectors.toList()));
                parts.stream().filter(v -> !containIds.contains(v.getUniqueId())).forEach(mapping -> {
                    ObjectMapPo newPo = new ObjectMapPo();
                    FmcgBeanUtil.copyProperties(mapping, newPo);
                    newPo.setId(null);
                    newPo.setTenantId(arg.getToTenantId());
                    objectMapPos.add(newPo);
                });
            });
        });
        objectMapDao.batchAdd(objectMapPos);
        CopyModelAndMappingResult result = new CopyModelAndMappingResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public ModelSwitchResult modelSwitch(ModelSwitchArg arg) {
        ModelPo po = modelDao.query(arg.getId(), arg.getTenantId());
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(ModelPo.F_STATUS, arg.getSwitchStatus());
        modelDao.update(arg.getTenantId(), po.getOriginalId().toString(), updateMap);
        ModelSwitchResult result = new ModelSwitchResult();
        result.setSuccess(true);
        return result;
    }


    private boolean isMongoIdString(String id) {
        if (id == null || id.length() > 24)
            return false;
        for (int i = 0; i < id.length(); i++) {
            char c = id.charAt(i);
            boolean t = (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
            if (!t)
                return false;
        }
        return true;
    }

    @Override
    public OverlayUpdateModelResult overlayUpdate(OverlayUpdateModelArg arg) {
        OverlayUpdateModelResult result = new OverlayUpdateModelResult();
        ModelPo dbPo = modelDao.query(arg.getModel().getId(), arg.getTenantId());
        ModelPo modelPo = ConvertUtil.convertToPo(arg.getModel());
        //为空则不更新TK
        if (Strings.isNullOrEmpty(modelPo.getTokenKey())) {
            modelPo.setTokenKey(dbPo.getTokenKey());
        }
        if (Strings.isNullOrEmpty(modelPo.getPlatform())) {
            modelPo.setPlatform(dbPo.getPlatform());
        }
        modelPo.setId(dbPo.getOriginalId());
        if(nameCheck(arg.getTenantId(), arg.getModel().getName(), dbPo.getUniqueId())) {
            result.setCode("fmcg.ai.model.name.repeat");
            result.setMessage("模型的名字已存在。");
            return result;
        }
        modelDao.save(modelPo);
        ModelDto modelDto = ConvertUtil.convertToDto(modelPo);
        result.setModel(modelDto);
        return result;
    }

}
