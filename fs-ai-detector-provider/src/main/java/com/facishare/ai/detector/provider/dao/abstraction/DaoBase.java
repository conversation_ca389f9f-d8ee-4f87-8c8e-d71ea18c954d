package com.facishare.ai.detector.provider.dao.abstraction;

import com.fxiaoke.api.IdGenerator;
import com.github.mongo.support.DatastoreExt;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-9-24  下午6:13
 */
@SuppressWarnings("unchecked")
public abstract class DaoBase<R extends MongoPOBase> {

    @Autowired
    protected DatastoreExt dbContext;

    private Class<R> clazz;

    public DaoBase() {
        clazz = (Class<R>) ((java.lang.reflect.ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    protected <T> Query<T> buildIdQuery(String id, Class<T> clazz) {
        Query<T> query = dbContext.createQuery(clazz);
        query.field("_id").equal(new ObjectId(id));
        return query;
    }

    protected <T> Query<T> buildUniqueIdQuery(String uniqueId, Class<T> clazz) {
        Query<T> query = dbContext.createQuery(clazz);
        query.or(query.criteria(MongoPOBase.F_UNIQUE_ID).equal(uniqueId), query.criteria("_id").equal(new ObjectId(uniqueId)));
        return query;
    }


    public String save(R po) {
        if (Strings.isNullOrEmpty(po.uniqueId)) {
            String id = po.getId() == null ? IdGenerator.get() : po.getId().toString();
            po.setId(new ObjectId(id));
            po.setUniqueId(id);
        }
        setSystemInfo(po);
        return dbContext.save(po).getId().toString();
    }

    public void saveAll(List<R> models) {
        for (R model : models) {
            if (Strings.isNullOrEmpty(model.uniqueId)) {
                String id = model.getId() == null ? IdGenerator.get() : model.getId().toString();
                model.setId(new ObjectId(id));
                model.setUniqueId(id);
            }
            setSystemInfo(model);
        }
        dbContext.save(models);
    }

    public void setSystemInfo(MongoPOBase po) {
        if (po.getCreateTime() == null) {
            po.setCreateTime(System.currentTimeMillis());
        }
        if (po.getLastModifyTime() == null) {
            po.setLastModifyTime(System.currentTimeMillis());
        }
        if (po.getCreator() == null) {
            po.setCreator(-10000);
        }
        if (po.getLastModifier() == null) {
            po.setLastModifier(-10000);
        }
    }

    public R query(String id, Integer tenantId) {
        Query<R> query = dbContext.createQuery(clazz);
        query.or(query.criteria(MongoPOBase.F_UNIQUE_ID).equal(id), query.criteria("_id").equal(new ObjectId(id)));
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        return query.get();
    }

    public List<R> queryNullUniqueIdPo(int limit) {
        Query<R> query = dbContext.createQuery(clazz);
        query.field(MongoPOBase.F_UNIQUE_ID).doesNotExist();
        query.limit(limit);
        return query.asList();
    }

    public void fillUniqueId(MongoPOBase mongoPO) {
        if (mongoPO.getId() == null) {
            String id = IdGenerator.get();
            mongoPO.setId(new ObjectId(id));
            if (Strings.isNullOrEmpty(mongoPO.getUniqueId())) {
                mongoPO.setUniqueId(id);
            }
        }
    }
}
