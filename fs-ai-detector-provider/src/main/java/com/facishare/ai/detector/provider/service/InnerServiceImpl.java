package com.facishare.ai.detector.provider.service;

import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.enumeration.AICategoryEnum;
import com.facishare.ai.detector.api.enumeration.ModelSceneEnum;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.service.abstraction.InnerService;
import com.facishare.converter.EIEAConverter;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/22 下午2:25
 */
@Component
public class InnerServiceImpl implements InnerService {
    private static final Logger log = LoggerFactory.getLogger(InnerServiceImpl.class);
    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private ModelDao modelDao;

    @Resource
    private ObjectMapDao objectMapDao;

    private static final String LOG_DIR = "/opt/tomcat/logs/";

    @Override
    public String downloadFile(Integer ei, String npath, String saveName) {
        try {
            if (Strings.isNullOrEmpty(saveName))
                saveName = npath + ".jpg";
            byte[] bytes = fileAdapter.downloadByPath(eieaConverter.enterpriseIdToAccount(ei), npath);
            try (FileOutputStream fileOutputStream = new FileOutputStream(new File(LOG_DIR + saveName))) {
                fileOutputStream.write(bytes);
            }
        } catch (AiProviderException | IOException e) {
            return e.toString();
        }
        return "success";
    }

    @Override
    public String updateUniqueId() {
        List<? extends MongoPOBase> pos;
        MongoPOBase last = null;
        while (!(pos = modelDao.queryNullUniqueIdPo(100)).isEmpty()) {
            if (last != null && last.getId().equals(pos.get(0).getId())) {
                break;
            }
            pos.forEach(po -> po.setUniqueId(po.getId().toString()));
            modelDao.saveAll((List<ModelPo>) pos);
        }

        while (!(pos = objectMapDao.queryNullUniqueIdPo(100)).isEmpty()) {
            if (last != null && last.getId().equals(pos.get(0).getId())) {
                break;
            }
            pos.forEach(po -> po.setUniqueId(po.getId().toString()));
            objectMapDao.batchAdd((List<ObjectMapPo>) pos);
        }
        return "success";
    }

    @Override
    public String updateModelAddtionalField950() {
        List<ModelPo> modelsWithoutStatus;
        while (!(modelsWithoutStatus = modelDao.queryEmptyStatusPo(100)).isEmpty()) {
            for (ModelPo model : modelsWithoutStatus) {
                try{
                    fillModel(model);
                }catch (Exception e){
                    log.info("error",e);
                }
            }
            modelDao.saveAll(modelsWithoutStatus);
        }
        return "success";
    }

    @Override
    public String updateModelParentType() {
        return "";
    }


    private static void fillModel(ModelPo model) {
        model.setStatus(1);
        if (AICategoryEnum.OBJECT_RECOGNITION.value().equals(model.getType())) {
            model.setScene(ModelSceneEnum.DISPLAY.getValue());
        } else if (AICategoryEnum.IMAGE_CLASSIFICATION.value().equals(model.getType())) {
            model.setScene(ModelSceneEnum.RECAPTURE.getValue());
        } else if("CLASSIFY".equals(model.getType())) {
            model.setScene(ModelSceneEnum.RECAPTURE.getValue());
            model.setType(AICategoryEnum.IMAGE_CLASSIFICATION.value());
        }else{
            System.out.println(JSON.toJSONString(model));
            throw new AiProviderException("123", "error type");
        }
        switch (model.getPlatform()) {
            case "baidu":
            case "baidu_retail":
            case "baidu_customized":
            case "baidu_similar_search":
            case "baidu_same_search":
                model.setModelManufacturer("baidu");
                break;
            case "huawei":
            case "huawei_sku_poc":
            case "huawei_model_art_obj_detect":
                model.setModelManufacturer("huawei");
                break;
            case "yqsl":
            case "yqsl_rest":
            case "yqsl_rest_v2":
            case "yqsl_materials":
            case "yqsl_materials_v2":
                model.setModelManufacturer("yqsl");
                break;
            case "rio":
                model.setModelManufacturer("rio");
                break;
            case "mengniu":
            case "mengniu_v2":
                model.setModelManufacturer("meng_niu");
                break;
            case "tujiang":
                model.setModelManufacturer("tu_jiang");
                break;
            case "sense_time":
                model.setModelManufacturer("sense_time");
                break;
            case "alibaba":
                model.setModelManufacturer("alibaba");
                break;
            case "pinlan":
                model.setModelManufacturer("pinlan");
                break;
            default:
                model.setModelManufacturer("unKnown");
        }
    }
}
