package com.facishare.ai.detector.provider.facade;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.arg.*;
import com.facishare.ai.detector.api.dto.result.*;
import com.facishare.ai.detector.api.service.AICostService;
import com.facishare.ai.detector.api.service.DetectCounterService;
import com.facishare.ai.detector.api.service.DetectRecordService;
import com.facishare.ai.detector.provider.dao.abstraction.DetectRecordDao;
import com.facishare.ai.detector.provider.service.abstraction.InnerService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/3/24 下午5:02
 */
@RestController
@RequestMapping("v1/inner")
public class InnerController {
    @Resource
    public DetectCounterService detectCounterService;

    @Resource
    private AICostService aiCostService;

    @Resource
    private InnerService innerService;

    @Resource
    private DetectRecordDao detectRecordDao;

    @Resource
    private DetectRecordService detectRecordService;

    @RequestMapping("/query_count")
    public QueryCountByTimeResult queryCount(@RequestBody QueryCountByTimeArg arg) {
        return detectCounterService.queryCountByTime(arg);
    }

    @RequestMapping("/create_account")
    public CreateAccountResult createAccount(@RequestBody CreateAccountArg arg) {
        return aiCostService.createAccount(arg);
    }

    @RequestMapping("/create_account_detail")
    public CreateAccountDetailResult createAccountDetail(@RequestBody CreateAccountDetailArg arg) {
        return aiCostService.createAccountDetail(arg);
    }

    @RequestMapping("/create_price")
    public CreatePriceResult createPrice(@RequestBody CreatePriceArg arg) {
        return aiCostService.createPrice(arg);
    }

    @RequestMapping("/query_balance_of_object_detect")
    public QueryBalanceOfObjectDetectResult que(@RequestBody QueryBalanceOfObjectDetectArg arg) {
        return aiCostService.queryBalanceOfObjectDetect(arg);
    }

    @RequestMapping("/saveFile")
    public String saveFile(@RequestParam Integer tenantId, @RequestParam String npath, @RequestParam String saveName) {
        return innerService.downloadFile(tenantId, npath, saveName);
    }

    @RequestMapping("/updateUniqueId")
    public String updateUniqueId(@RequestBody Object a) {
        return innerService.updateUniqueId();
    }

    @RequestMapping("/updateModelAddtionalField950")
    public String updateModelAddtionalField950(@RequestBody Object arg) {
        return innerService.updateModelAddtionalField950();
    }

    @RequestMapping("/deleteAllDetectRecord")
    public JSONObject deleteAllByNpathList(@RequestBody JSONObject arg) {
        detectRecordDao.deleteAll(arg.getInteger("tenant_id"), arg.getString("model_id"), arg.getJSONArray("npath_list").toJavaList(String.class));
        return new JSONObject();
    }

    @RequestMapping("deleteDetectedRecordByTenantId")
    public DeleteDetectedRecordByTenantIdResult deleteDetectedRecordByTenantId(@RequestBody DeleteDetectRecordByTenantIdArg arg) {
        return detectRecordService.deleteDetectedRecordByTenantId(arg);
    }

}
