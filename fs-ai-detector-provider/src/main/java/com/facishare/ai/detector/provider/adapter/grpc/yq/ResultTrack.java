// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: XDService.proto

package com.facishare.ai.detector.provider.adapter.grpc.yq;

/**
 * Protobuf type {@code XDService.ResultTrack}
 */
public final class ResultTrack extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:XDService.ResultTrack)
        ResultTrackOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ResultTrack.newBuilder() to construct.
  private ResultTrack(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ResultTrack() {
    imgId_ = "";
    code_ = "";
    result_ = "";
  }

  @Override
  @SuppressWarnings({"unused"})
  protected Object newInstance(
      UnusedPrivateParameter unused) {
    return new ResultTrack();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ResultTrack(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            String s = input.readStringRequireUtf8();

            imgId_ = s;
            break;
          }
          case 18: {
            String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 26: {
            String s = input.readStringRequireUtf8();

            result_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_ResultTrack_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_ResultTrack_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.class, com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.Builder.class);
  }

  public static final int IMGID_FIELD_NUMBER = 1;
  private volatile Object imgId_;
  /**
   * <code>string imgId = 1;</code>
   * @return The imgId.
   */
  @Override
  public String getImgId() {
    Object ref = imgId_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs =
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      imgId_ = s;
      return s;
    }
  }
  /**
   * <code>string imgId = 1;</code>
   * @return The bytes for imgId.
   */
  @Override
  public com.google.protobuf.ByteString
      getImgIdBytes() {
    Object ref = imgId_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b =
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      imgId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 2;
  private volatile Object code_;
  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  @Override
  public String getCode() {
    Object ref = code_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs =
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  @Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    Object ref = code_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b =
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESULT_FIELD_NUMBER = 3;
  private volatile Object result_;
  /**
   * <code>string result = 3;</code>
   * @return The result.
   */
  @Override
  public String getResult() {
    Object ref = result_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs =
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      result_ = s;
      return s;
    }
  }
  /**
   * <code>string result = 3;</code>
   * @return The bytes for result.
   */
  @Override
  public com.google.protobuf.ByteString
      getResultBytes() {
    Object ref = result_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b =
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      result_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getImgIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, imgId_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, code_);
    }
    if (!getResultBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, result_);
    }
    unknownFields.writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getImgIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, imgId_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, code_);
    }
    if (!getResultBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, result_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack)) {
      return super.equals(obj);
    }
    com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack other = (com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack) obj;

    if (!getImgId()
        .equals(other.getImgId())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getResult()
        .equals(other.getResult())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + IMGID_FIELD_NUMBER;
    hash = (53 * hash) + getImgId().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + RESULT_FIELD_NUMBER;
    hash = (53 * hash) + getResult().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code XDService.ResultTrack}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:XDService.ResultTrack)
      com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrackOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_ResultTrack_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_ResultTrack_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.class, com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.Builder.class);
    }

    // Construct using com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @Override
    public Builder clear() {
      super.clear();
      imgId_ = "";

      code_ = "";

      result_ = "";

      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.XDServiceProto.internal_static_XDService_ResultTrack_descriptor;
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack getDefaultInstanceForType() {
      return com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.getDefaultInstance();
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack build() {
      com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack buildPartial() {
      com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack result = new com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack(this);
      result.imgId_ = imgId_;
      result.code_ = code_;
      result.result_ = result_;
      onBuilt();
      return result;
    }

    @Override
    public Builder clone() {
      return super.clone();
    }
    @Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.setField(field, value);
    }
    @Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.addRepeatedField(field, value);
    }
    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack) {
        return mergeFrom((com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack other) {
      if (other == com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack.getDefaultInstance()) return this;
      if (!other.getImgId().isEmpty()) {
        imgId_ = other.imgId_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (!other.getResult().isEmpty()) {
        result_ = other.result_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private Object imgId_ = "";
    /**
     * <code>string imgId = 1;</code>
     * @return The imgId.
     */
    public String getImgId() {
      Object ref = imgId_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        imgId_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string imgId = 1;</code>
     * @return The bytes for imgId.
     */
    public com.google.protobuf.ByteString
        getImgIdBytes() {
      Object ref = imgId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        imgId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string imgId = 1;</code>
     * @param value The imgId to set.
     * @return This builder for chaining.
     */
    public Builder setImgId(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }

      imgId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string imgId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearImgId() {

      imgId_ = getDefaultInstance().getImgId();
      onChanged();
      return this;
    }
    /**
     * <code>string imgId = 1;</code>
     * @param value The bytes for imgId to set.
     * @return This builder for chaining.
     */
    public Builder setImgIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

      imgId_ = value;
      onChanged();
      return this;
    }

    private Object code_ = "";
    /**
     * <code>string code = 2;</code>
     * @return The code.
     */
    public String getCode() {
      Object ref = code_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }

      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {

      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

      code_ = value;
      onChanged();
      return this;
    }

    private Object result_ = "";
    /**
     * <code>string result = 3;</code>
     * @return The result.
     */
    public String getResult() {
      Object ref = result_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        result_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string result = 3;</code>
     * @return The bytes for result.
     */
    public com.google.protobuf.ByteString
        getResultBytes() {
      Object ref = result_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string result = 3;</code>
     * @param value The result to set.
     * @return This builder for chaining.
     */
    public Builder setResult(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }

      result_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string result = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearResult() {

      result_ = getDefaultInstance().getResult();
      onChanged();
      return this;
    }
    /**
     * <code>string result = 3;</code>
     * @param value The bytes for result to set.
     * @return This builder for chaining.
     */
    public Builder setResultBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

      result_ = value;
      onChanged();
      return this;
    }
    @Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:XDService.ResultTrack)
  }

  // @@protoc_insertion_point(class_scope:XDService.ResultTrack)
  private static final com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack();
  }

  public static com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ResultTrack>
      PARSER = new com.google.protobuf.AbstractParser<ResultTrack>() {
    @Override
    public ResultTrack parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ResultTrack(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ResultTrack> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<ResultTrack> getParserForType() {
    return PARSER;
  }

  @Override
  public com.facishare.ai.detector.provider.adapter.grpc.yq.ResultTrack getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

