package com.facishare.ai.detector.provider.dao.po;

import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午2:14
 */
@Entity(value = "ai_account_detail",noClassnameStored = true)
@Data
@ToString
public class AccountDetailPO extends MongoPOBase {

    public static final String F_ACCOUNT_ID="AI";
    public static final String F_TYPE="T";
    public static final String F_REMARK="R";
    public static final String F_AMOUNT="A";
    public static final String F_CREATE_TIME="CT";
    public static final String F_UPDATE_TIME="UT";
    public static final String F_CREATOR="CTR";
    public static final String F_UPDATER="UD";
    public static final String F_IS_DELETED="ID";


    @Property(F_ACCOUNT_ID)
    private String accountId;

    @Property(F_TYPE)
    private String type;

    @Property(F_REMARK)
    private String remark;

    @Property(F_AMOUNT)
    private Double amount;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_UPDATE_TIME)
    private Long updateTime;

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_UPDATER)
    private Integer updater;

    @Property(F_IS_DELETED)
    private Boolean isDeleted;
}
