package com.facishare.ai.detector.provider.inner;

import com.facishare.ai.detector.api.dto.ObjectDto;
import com.facishare.ai.detector.api.dto.api.InnerDetect;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.qixin.converter.QXEIEAConverterImpl;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/API/inner/v1/detector")
public class DetectorInnerController {

    @Resource
    private DetectorService detectorService;

    @Resource
    private QXEIEAConverterImpl qXEIEAConverter;

    @PostMapping(value = "/detect")
    public InnerDetect.Result detect(
            @RequestHeader("x-fs-ei") Integer tenantId,
            @RequestHeader("x-fs-userInfo") Integer currentEmployeeId,
            @RequestBody InnerDetect.Arg arg) {
        try {
            DetectArg apiArg = new DetectArg();
            apiArg.setTenantId(String.valueOf(tenantId));
            apiArg.setUserId(String.valueOf(currentEmployeeId));
            apiArg.setTenantAccount(qXEIEAConverter.enterpriseIdToEa(tenantId));
            apiArg.setAppId(arg.getAppId());
            apiArg.setModelId(arg.getModelId());
            apiArg.setPath(arg.getPath());
            apiArg.setCreateProcessedImage(false);
            DetectResult apiResult = detectorService.detect(apiArg);
            InnerDetect.Result result = new InnerDetect.Result();
            result.setCode(0);
            result.setMessage("success");
            result.setSuccess(true);
            InnerDetect.ResultData data = new InnerDetect.ResultData();
            data.setPath(arg.getPath());
            data.setObjectList(Lists.newArrayList());
            for (ObjectDto object : apiResult.getObjects()) {
                InnerDetect.ObjectDTO dto = new InnerDetect.ObjectDTO();
                dto.setApiName(object.getObjectType());
                dto.setDataId(object.getObjectId());
                InnerDetect.PositionDTO positionDTO = new InnerDetect.PositionDTO();
                positionDTO.setX(object.getPosition().getX());
                positionDTO.setY(object.getPosition().getY());
                positionDTO.setW(object.getPosition().getW());
                positionDTO.setH(object.getPosition().getH());
                dto.setPosition(positionDTO);
                dto.setScore(object.getScore());
                dto.setColor(object.getColor());
                dto.setUnit(object.getUnit());
                dto.setComponents(object.getComponents());
                dto.setIsFront(object.getIsFront());
                dto.setIsRotated(object.getIsRotated());
                data.getObjectList().add(dto);
            }
            result.setData(data);
            return result;
        } catch (AiProviderException ex) {
            InnerDetect.Result result = new InnerDetect.Result();
            result.setCode(501);
            result.setMessage(ex.getMessage());
            result.setSuccess(false);
            return result;
        } catch (Exception ex) {
            InnerDetect.Result result = new InnerDetect.Result();
            result.setCode(500);
            result.setMessage(ex.getMessage());
            result.setSuccess(false);
            return result;
        }
    }
}
