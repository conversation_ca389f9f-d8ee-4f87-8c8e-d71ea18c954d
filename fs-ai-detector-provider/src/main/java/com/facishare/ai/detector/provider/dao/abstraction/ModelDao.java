package com.facishare.ai.detector.provider.dao.abstraction;

import com.facishare.ai.detector.provider.dao.po.ModelPo;

import java.util.List;
import java.util.Map;

public interface ModelDao {

    String save(ModelPo po);

    ModelPo query(String id, Integer tenantId);

    void saveAll(List<ModelPo> models);

    List<ModelPo> query(Integer tenantId);

    List<ModelPo> query(Integer tenantId, String scene);

    void update(Integer tenantId, String id, Map<String, Object> updateFields);

    List<ModelPo> queryNullUniqueIdPo(int limit);

    List<ModelPo> queryEmptyStatusPo(int limit);

    ModelPo queryModelByName(Integer tenantId, String name, String id);

}
