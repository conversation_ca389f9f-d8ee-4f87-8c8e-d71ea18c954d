package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.ClassifyRecordDao;
import com.facishare.ai.detector.provider.dao.abstraction.DaoBase;
import com.facishare.ai.detector.provider.dao.po.ClassifyRecordPo;
import org.mongodb.morphia.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-9-24  下午6:37
 */
public class ClassifyRecordDaoImpl extends DaoBase<ClassifyRecordPo> implements ClassifyRecordDao {

    @Override
    public ClassifyRecordPo get(String id) {
        return buildIdQuery(id, ClassifyRecordPo.class).get();
    }


    @Override
    public ClassifyRecordPo get(Integer tenantId, String modelId, String srcImg) {
        Query<ClassifyRecordPo> query = dbContext.createQuery(ClassifyRecordPo.class);
        query.field(ClassifyRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(ClassifyRecordPo.F_MODEL_ID).equal(modelId);
        query.field(ClassifyRecordPo.F_SRC_IMG_PATH).equal(srcImg);
        List<ClassifyRecordPo> list = query.asList();
        return list.isEmpty() ? null : list.get(0);
    }

    @Override
    public List<ClassifyRecordPo> query(Integer tenantId, String modelId, List<String> srcImg) {
        Query<ClassifyRecordPo> query = dbContext.createQuery(ClassifyRecordPo.class);
        query.field(ClassifyRecordPo.F_TENANT_ID).equal(tenantId);
        query.field(ClassifyRecordPo.F_MODEL_ID).equal(modelId);
        query.field(ClassifyRecordPo.F_SRC_IMG_PATH).in(srcImg);
        return query.asList();
    }
}
