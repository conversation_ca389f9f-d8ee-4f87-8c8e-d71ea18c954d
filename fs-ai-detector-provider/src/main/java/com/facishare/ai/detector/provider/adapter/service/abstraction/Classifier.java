package com.facishare.ai.detector.provider.adapter.service.abstraction;

import com.facishare.ai.detector.api.dto.ClassDto;
import com.facishare.ai.detector.api.dto.arg.ClassifyArg;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.provider.dao.po.ModelPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-8-1  下午4:11
 */
public interface Classifier {

    /**
     * 场景识别
     *
     * @param arg
     * @param model
     * @param imageCache
     * @return
     */
    List<ClassDto> classify(ClassifyArg arg, ModelPo model, byte[] imageCache) throws AiProviderException;
}
