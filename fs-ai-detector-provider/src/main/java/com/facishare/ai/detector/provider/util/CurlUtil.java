package com.facishare.ai.detector.provider.util;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/8 下午2:29
 */
public class CurlUtil {

    private static final String HEADER_TEMPLATE = "\t-H \"%s:%s\" ";
    private static final String DATA_TEMPLATE = "\t-d '%s' ";
    private static final String TYPE_TEMPLATE = "\t-X %s ";

    public static String curl(String url, Map<String, ? extends Object> headers, String type, Object data) {
        StringBuilder sb = new StringBuilder("curl");
        if (headers != null && !headers.isEmpty()) {
            headers.forEach((k, v) -> sb.append(String.format(HEADER_TEMPLATE, k, v)));
        }
        type = Strings.isNullOrEmpty(type) ? "GET" : type;
        sb.append(String.format(TYPE_TEMPLATE, type.toUpperCase()));
        if (data != null && !(data instanceof String))
            sb.append(String.format(DATA_TEMPLATE, JSON.toJSONString(data)));
        else if (data != null)
            sb.append(String.format(DATA_TEMPLATE, data));
        else
            sb.append(String.format(DATA_TEMPLATE, ""));
        sb.append(url);
        return sb.toString();
    }

    public static String curl(Request request) throws IOException {
        String data = "";
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-type", "text/plain");
        if(request.body()!=null&&request.body().contentLength()>=0){
            Buffer bf = new Buffer();
            request.body().writeTo(bf);
            data = bf.readUtf8();
            headers.put("Content-type", request.body().contentType());
        }
        request.headers().toMultimap().forEach((key, value) -> {
            if (!value.isEmpty()) {
                StringBuilder temp = new StringBuilder(value.get(0));
                for (int i = 1; i < value.size(); i++) {
                    temp.append(";").append(value.get(i));
                }
                headers.put(key, temp);
            }
        });
        return curl(request.url().url().toString(), headers, request.method(), data);
    }

    public static void main(String[] args) throws IOException {
        Request.Builder builder = new Request.Builder().url("http://www.baidu.com");
        builder.post(RequestBody.create(MediaType.parse("application/json;charset=utf-8"), JSON.toJSONString(new Object())));
        System.out.println(curl(builder.build()));
    }
}
