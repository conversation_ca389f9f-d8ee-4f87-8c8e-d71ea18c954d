package com.facishare.ai.detector.provider.dao.po;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 19-10-15  下午7:53
 */
@Data
@ToString
public class ObjectEntity {

    /**
     * api name or custom object flag
     */
    private String objectType;

    /**
     * display name
     */
    private String objectName;

    /**
     * object identity id
     */
    private String objectId;

    /**
     * score
     */
    private String score;

    /**
     * app id
     */
    private String appId;

    /**
     * box color
     */
    private String color;

    private String unit;

    /**
     * box position
     */
    private PositionEntity position;

    /*
       number of sku in one
     */
    private Integer components;

    /**
     * box face direction
     */
    private Boolean isFront;

    /**
     * box rotation
     */
    private Boolean isRotated;

    private List<ObjectEntity> componentEntities;

    private String type;

    private String key;

    private String scene;

    private Integer shelf;

    private Integer layer;

    private String skuSn;

    private Integer subSkuCount;

    private JSONObject extraData;
}
