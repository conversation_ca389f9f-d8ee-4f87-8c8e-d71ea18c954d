package com.facishare.ai.detector.provider.adapter.service.ocr;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.abstraction.OcrDetector;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.FacadeDetectAO;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.IdCardDetectAO;
import com.facishare.ai.detector.provider.adapter.service.ocr.model.VATInvoiceDetectAO;
import com.facishare.ai.detector.provider.util.HttpUtil;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/28 下午3:52
 */
public class BaiduOcrDetectorImpl implements OcrDetector {

    @Resource
    private TokenFactory tokenFactory;

    private Logger log = LoggerFactory.getLogger(BaiduOcrDetectorImpl.class);


    @Override
    public IdCardDetectAO idCardDetect(byte[] image, String cardSide, String detectDirection, String detectRisk, String detectPhoto, String detectRectify) throws AiProviderException, IOException {
        IdCardDetectAO idCardDetectAO = new IdCardDetectAO();
        String url = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.BAIDU_OCR);
        url = String.format(url, "idcard", tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_OCR.value()));
        String data = "id_card_side=" + cardSide;
        if (StringUtils.isNotEmpty(detectDirection)) {
            data += "&detect_direction=" + detectDirection;
        }
        if (StringUtils.isNotEmpty(detectRisk)) {
            data += "&detect_risk=" + detectRisk;
        }
        if (StringUtils.isNotEmpty(detectPhoto)) {
            data += "&detect_photo=" + detectPhoto;
        }
        if (StringUtils.isNotEmpty(detectRectify)) {
            data += "&detect_rectify=" + detectRectify;
        }
        data += "&image=" + URLEncoder.encode(Base64.encodeBase64String(image), "UTF-8");
        JSONObject baiduRst = (JSONObject) HttpUtil.form_post(url, null, data);
        if (baiduRst != null && baiduRst.getInteger("error_code") == null) {
            idCardDetectAO = baiduRst.toJavaObject(IdCardDetectAO.class);
            JSONObject rstMap = baiduRst.getJSONObject("words_result");
            if (rstMap != null && !rstMap.isEmpty()) {
                for (String k : rstMap.keySet()) {
                    JSONObject item = rstMap.getJSONObject(k);
                    switch (k) {
                        case "姓名":
                            idCardDetectAO.setName(item.getString("words"));
                            break;
                        case "民族":
                            idCardDetectAO.setNationality(item.getString("words"));
                            break;
                        case "住址":
                            idCardDetectAO.setAddress(item.getString("words"));
                            break;
                        case "公民身份号码":
                            idCardDetectAO.setIdNum(item.getString("words"));
                            break;
                        case "出生":
                            idCardDetectAO.setBirth(item.getString("words"));
                            break;
                        case "性别":
                            idCardDetectAO.setSex(item.getString("words"));
                            break;
                        default:
                            break;
                    }
                }
            }
        } else {
            idCardDetectAO.setErrorCode(baiduRst.getInteger("error_code"));
            idCardDetectAO.setErrorMsg(baiduRst.getString("error_msg"));
        }
        return idCardDetectAO;
    }

    @Override
    public VATInvoiceDetectAO VATInvoiceDetect(byte[] image, String accuracy, String type) throws AiProviderException, IOException {
        VATInvoiceDetectAO vatInvoiceDetectAO = new VATInvoiceDetectAO();
        String url = ConfigFactory.getConfig(ConstantUtil.AI_URL_TEMPLATE_CONFIG).get(ConstantUtil.BAIDU_OCR);
        url = String.format(url, "vat_invoice", tokenFactory.getToken(AppEnum.BAIDU_FXIAOKERD_OCR.value()));
        String data = "author=jiege";
        if (StringUtils.isNotEmpty(accuracy)) {
            data += "&accuracy=" + accuracy;
        }
        if (StringUtils.isNotEmpty(type)) {
            data += "&type=" + type;
        }
        data += "&image=" + URLEncoder.encode(Base64.encodeBase64String(image), "UTF-8");
        JSONObject baiduRst = (JSONObject) HttpUtil.form_post(url, null, data);
        if (baiduRst != null && baiduRst.getInteger("error_code") == null) {
            JSONObject wordRst = baiduRst.getJSONObject("words_result");
            if (wordRst != null) {
                vatInvoiceDetectAO = wordRst.toJavaObject(VATInvoiceDetectAO.class);
                Map<String, VATInvoiceDetectAO.Item> products = new HashMap<>(2);
                JSONArray name = wordRst.getJSONArray("CommodityName");
                if (name != null && !name.isEmpty()) {
                    name.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityName(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityType = wordRst.getJSONArray("CommodityType");
                if (commodityType != null && !commodityType.isEmpty()) {
                    commodityType.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityType(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray CommodityUnit = wordRst.getJSONArray("CommodityUnit");
                if (CommodityUnit != null && !CommodityUnit.isEmpty()) {
                    CommodityUnit.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityUnit(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityNum = wordRst.getJSONArray("CommodityNum");
                if (commodityNum != null && !commodityNum.isEmpty()) {
                    commodityNum.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityNum(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityPrice = wordRst.getJSONArray("CommodityPrice");
                if (commodityPrice != null && !commodityPrice.isEmpty()) {
                    commodityPrice.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityPrice(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityAmount = wordRst.getJSONArray("CommodityAmount");
                if (commodityAmount != null && !commodityAmount.isEmpty()) {
                    commodityAmount.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityAmount(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityTaxRate = wordRst.getJSONArray("CommodityTaxRate");
                if (commodityTaxRate != null && !commodityTaxRate.isEmpty()) {
                    commodityTaxRate.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityTaxRate(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }

                JSONArray commodityTax = wordRst.getJSONArray("CommodityTax");
                if (commodityTax != null && !commodityTax.isEmpty()) {
                    commodityTax.forEach(v -> {
                        JSONObject row = (JSONObject) v;
                        VATInvoiceDetectAO.Item item = products.getOrDefault(row.getString("row"), new VATInvoiceDetectAO.Item());
                        item.setCommodityTax(row.getString("word"));
                        products.put(row.getString("row"), item);
                    });
                }
                if (!products.isEmpty()) {
                    vatInvoiceDetectAO.setProducts(new ArrayList<>());
                    for (VATInvoiceDetectAO.Item v : products.values()) {
                        vatInvoiceDetectAO.getProducts().add(v);
                    }
                }
            }
        } else {
            vatInvoiceDetectAO.setErrorCode(baiduRst.getInteger("error_code"));
            vatInvoiceDetectAO.setErrorMsg(baiduRst.getString("error_msg"));
        }
        return vatInvoiceDetectAO;
    }

    @Override
    public FacadeDetectAO facadeDetect(byte[] image) {
        return null;
    }


}
