package com.facishare.ai.detector.provider.dao.po;

import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;

import lombok.Data;
import lombok.ToString;

import static com.facishare.ai.detector.provider.dao.po.ObjectMapPo.F_API_NAME;


/**
 * <AUTHOR>
 */
@Data
@ToString
@Entity(value = "ai_model", noClassnameStored = true)
public class ModelPo extends MongoPOBase  {

    public static final String F_KEY = "K";
    public static final String F_PLAT_FROM = "PF";
    public static final String F_NAME = "N";
    public static final String F_TYPE = "T";
    public static final String F_CONFIDENCE = "C";
    public static final String F_MODEL_CODE = "MC";
    public static final String F_IDENTITY="I";
    public static final String F_TOKEN_KEY="TK";
    public static final String F_PARAMS="PS";
    public static final String F_SCENE="SNE";
    public static final String F_STATUS="STS";
    public static final String F_MODEL_MANUFACTURER="MM";
    public static final String F_DESCRIPTION="DSP";
    private static final String F_PARENT_TYPE = "PT";
    private static final String F_API_NAME = "AN";
    private static final String F_IS_DEFAULT = "IDF";


    @Property(F_NAME)
    private String name;

    @Property(F_PLAT_FROM)
    private String platform;

    @Property(F_API_NAME)
    private String apiName;

    /**
     * 实际平台的modelid
     */
    @Property(F_MODEL_CODE)
    private String modelCode;

    /**
     * 映射介入平台模型的键值，一般用以拼接url 或则
     */
    @Property(F_KEY)
    private String key;

    @Property(F_CONFIDENCE)
    private Double confidence;

    /**
     * 模型类别
     */
    @Property(F_TYPE)
    private String type;

    @Property(F_PARENT_TYPE)
    private String parentType;


    /**
     * 对应哪个token
     */
    @Property(F_TOKEN_KEY)
    private String tokenKey;


    /**
     * 临时字段 兼容老的model 用于存储自定义的modelID ,后期调整删除
     */
    @Property(F_IDENTITY)
    private String identity;


    /**
     * display 陈列模型
     */
    @Property(F_SCENE)
    private String scene;

    @Property(F_STATUS)
    private int status;

    @Property(F_MODEL_MANUFACTURER)
    private String modelManufacturer;

    @Property(F_DESCRIPTION)
    private String description;

    @Embedded(F_PARAMS)
    private JSONObject params;

    @Property(F_IS_DEFAULT)
    private boolean isDefault;
}
