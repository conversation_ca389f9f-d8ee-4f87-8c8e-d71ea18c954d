package com.facishare.ai.detector.provider.dao;

import com.facishare.ai.detector.provider.dao.abstraction.MongoPOBase;
import com.github.mongo.support.DatastoreExt;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author: linmj
 * Date: 2024/7/10 17:43
 */
@Slf4j
@Component
public class GeneralDAO {

    @Autowired
    protected DatastoreExt dbContext;

    public static final Map<String, Class> CLAZZ_MAP = new HashMap<>();


    private Class getClassByPath(String path) {
        if (CLAZZ_MAP.containsKey(path)) {
            return CLAZZ_MAP.get(path);
        }
        Class clazz = null;
        try {
            clazz = Class.forName(path);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        CLAZZ_MAP.put(path, clazz);
        return clazz;
    }

    public int update(Integer tenantId, String classPath, Map<String, Object> conditionMap, Map<String, Object> updateMap) {
        Class clazz = getClassByPath(classPath);
        Query query = dbContext.createQuery(clazz);
        query.field(MongoPOBase.F_TENANT_ID).equal(tenantId);
        if (MapUtils.isEmpty(conditionMap)) {
            log.info("条件为空，直接回退");
            return 0;
        }
        conditionMap.forEach((key, value) -> {
            if (key.equals("_id")) {
                query.field(key).equal(new ObjectId(value.toString()));
            } else {
                query.field(key).equal(value);
            }
        });

        UpdateOperations<MongoPOBase> updateOperations = dbContext.createUpdateOperations(clazz);
        updateMap.forEach(updateOperations::set);
        return dbContext.update(query, updateOperations).getUpdatedCount();
    }
}
