package com.facishare.ai.detector.provider.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.dto.BoxDto;
import com.facishare.ai.detector.api.dto.arg.BatchDetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectArg;
import com.facishare.ai.detector.api.dto.arg.DetectByBase64Arg;
import com.facishare.ai.detector.api.dto.result.BatchDetectResult;
import com.facishare.ai.detector.api.dto.result.DetectByBase64Result;
import com.facishare.ai.detector.api.dto.result.DetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.DetectorService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.service.DetectorFactory;
import com.facishare.ai.detector.provider.adapter.service.abstraction.Detector;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.cache.ProductCache;
import com.facishare.ai.detector.provider.dao.OldModelDaoImpl;
import com.facishare.ai.detector.provider.dao.OldObjectMapDaoImpl;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.abstraction.DetectRecordDao;
import com.facishare.ai.detector.provider.dao.abstraction.ModelDao;
import com.facishare.ai.detector.provider.dao.abstraction.ObjectMapDao;
import com.facishare.ai.detector.provider.dao.po.DetectRecordPo;
import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.facishare.ai.detector.provider.dao.po.ObjectEntity;
import com.facishare.ai.detector.provider.dao.po.ObjectMapPo;
import com.facishare.ai.detector.provider.util.AutoMapper;
import com.facishare.ai.detector.provider.util.CommonUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataGet;
import com.fs.fmcg.sdk.ai.adapter.contract.CommonDetect;
import com.fs.fmcg.sdk.ai.business.abstration.RowFaceBusiness;
import com.fs.fmcg.sdk.ai.common.SpringContextHolder;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
public class DetectorServiceImpl implements DetectorService {

    private static final Logger logger = LoggerFactory.getLogger(DetectorServiceImpl.class);

    private static final String DOT_JPG = ".jpg";

    private static final String JPG = "jpg";

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private ModelDao modelDao;

    @Resource
    private ObjectMapDao objectMapDao;

    @Autowired
    private OldModelDaoImpl oldModelDao;

    @Autowired
    private OldObjectMapDaoImpl oldObjectMapDao;

    @Resource
    private DetectRecordDao detectRecordDao;

    @Resource
    private DetectCounterDao detectCounterDao;

    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private ProductCache productCache;

    protected static Map<String, String> ROW_UTIL_MAP = new HashMap<>();

    static {
        ROW_UTIL_MAP.put("jml_v1", RowFaceBusiness.JML_ROW_FACE);
        ROW_UTIL_MAP.put("niupi", RowFaceBusiness.HLY_ROW_FACE);
        ConfigFactory.getConfig("fs-fmcg-sdk-apis", config -> {
            String json = config.get("ROW_UTIL_MAP");
            if (Strings.isNullOrEmpty(json))
                return;
            Map<String, String> map = (Map<String, String>) JSON.parse(json);
            ROW_UTIL_MAP = map;
        });
    }

    private static final Color[] COLORS = new Color[]{
            new Color(249, 42, 130),
            new Color(67, 217, 253),
            new Color(249, 100, 116),
            new Color(32, 218, 154),
            new Color(252, 224, 17),
            new Color(255, 116, 204),
            new Color(109, 169, 255),
            new Color(31, 131, 255),
            new Color(246, 172, 255),
            new Color(185, 207, 68),
            new Color(80, 143, 179),
            new Color(201, 31, 37),
            new Color(255, 169, 46),
            new Color(224, 88, 41),
            new Color(63, 63, 152),
            new Color(242, 182, 194),
            new Color(134, 193, 45),
            new Color(155, 57, 236),
            new Color(177, 119, 55),
            new Color(147, 133, 236),
            new Color(24, 151, 159),
            new Color(210, 85, 101),
            new Color(36, 112, 160),
            new Color(183, 92, 37),
            new Color(61, 212, 196)};

    @Override
    public DetectResult detect(DetectArg arg) throws AiProviderException {
        if (arg.getCreateProcessedImage() == null) {
            arg.setCreateProcessedImage(true);
        }

        logger.info("detect arg : {}", JSON.toJSONString(arg));

        long start = System.currentTimeMillis();
        ModelPo model = insureModel(arg.getModelId(), arg.getTenantId());
        DetectResult result = detectSingleImage(arg, model);

        logger.info("detect time total cost {}. arg : {}", System.currentTimeMillis() - start, arg);
        return result;
    }

    @Override
    public BatchDetectResult batchDetect(BatchDetectArg arg) throws AiProviderException {
        if (arg.getCreateProcessedImage() == null) {
            arg.setCreateProcessedImage(true);
        }
        TraceContext.get().setTraceId(UUID.randomUUID().toString());
        logger.info("batch detect arg : {}", arg);

        ModelPo model = insureModel(arg.getModelId(), arg.getTenantId());
        BatchDetectResult result = new BatchDetectResult();
        result.setResults(new ArrayList<>(arg.getPaths().size()));
        for (String path : arg.getPaths()) {
            DetectArg detectArg = AutoMapper.convertDetectArg(arg, path);
            result.getResults().add(detectSingleImage(detectArg, model));
        }
        logger.info("batch finish detect ");
        TraceContext.remove();
        return result;
    }

    @Override
    public DetectByBase64Result detectByBase64(DetectByBase64Arg arg) throws AiProviderException {
        logger.info("detect arg : {}", JSON.toJSONString(arg));

        long start = System.currentTimeMillis();
        ModelPo model = insureModel(arg.getModelId(), arg.getTenantId());
        DetectByBase64Result result = detectSingleImageByBase64(arg, model);

        logger.info("detect time total cost {}. arg : {}", System.currentTimeMillis() - start, arg);
        return result;
    }

    private DetectByBase64Result detectSingleImageByBase64(DetectByBase64Arg arg, ModelPo model) throws AiProviderException {
        int tenantId = Integer.parseInt(arg.getTenantId());
        Set<String> relatedPaths = getAllNPathForSameFile(arg.getTenantAccount(), arg.getUserId(), arg.getPath());
        String appId = arg.getAppId();
        DetectRecordPo record = detectRecordDao.get(tenantId, arg.getModelId(), relatedPaths);
        if (record == null) {
            List<BoxDto> boxes;
            try {
                Detector detector = DetectorFactory.getDetector(model.getPlatform());
                logger.info("start detect, arg : {}", arg);
                boxes = detector.detectByBase64(arg, model);
                //排面统计
                boxes = AutoMapper.toBoxDtoList(rebuildBox(model.getKey(), AutoMapper.toCommonDetectBoxDTOList(boxes)));

                logger.info("finish detect, result : {}", boxes);
                detectCounterDao.success(tenantId, String.format(ConstantUtil.OBJECT_DETECT, model.getId() == null ? model.getIdentity() : model.getUniqueId()));
            } catch (AiProviderException ex) {

                logger.error("detect error ", ex);
                detectCounterDao.fail(tenantId, String.format(ConstantUtil.OBJECT_DETECT, model.getId() == null ? model.getIdentity() : model.getUniqueId()));
                throw ex;
            }
            record = insertDetectRecordWithNPath(tenantId, appId, arg.getPath(), arg.getModelId(), boxes);
            logger.info("finish draw");
        }
        record.setOriginalPath(arg.getPath());
        return AutoMapper.toDetectByBase64Result(record);
    }

    private DetectResult detectSingleImage(DetectArg arg, ModelPo model) throws AiProviderException {
        int tenantId = Integer.parseInt(arg.getTenantId());
        Set<String> relatedPaths = getAllNPathForSameFile(arg.getTenantAccount(), arg.getUserId(), arg.getPath());
        String tenantAccount = arg.getTenantAccount();
        Integer userId = Integer.parseInt(arg.getUserId());
        String appId = arg.getAppId();
        logger.info("start detect, arg : {}", arg);
        DetectRecordPo record = detectRecordDao.get(tenantId, arg.getModelId(), relatedPaths);
        boolean isCache = false;
        if (record == null) {
            byte[] image = fileAdapter.downloadWithoutSockets(tenantId, tenantAccount, arg.getPath());
            List<BoxDto> boxes;
            try {
                Detector detector = DetectorFactory.getDetector(model.getPlatform());

                boxes = detector.detect(arg, model, image);
                //排面统计
                boxes = AutoMapper.toBoxDtoList(rebuildBox(model.getKey(), AutoMapper.toCommonDetectBoxDTOList(boxes)));

                logger.info("finish detect, result : {}", boxes);
                detectCounterDao.success(tenantId, String.format(ConstantUtil.OBJECT_DETECT, model.getId() == null ? model.getIdentity() : model.getUniqueId()));
            } catch (Exception ex) {
                logger.error("detect error : ", ex);
                detectCounterDao.fail(tenantId, String.format(ConstantUtil.OBJECT_DETECT, model.getId() == null ? model.getIdentity() : model.getUniqueId()));
                throw ex;
            }
            if (Boolean.TRUE.equals(arg.getCreateProcessedImage())) {
                record = insertDetectRecord(tenantId, tenantAccount, userId, appId, arg.getPath(), arg.getModelId(), boxes, image);
            } else {
                record = insertDetectRecordWithOutCreateProcessedImage(tenantId, tenantAccount, userId, appId, arg.getPath(), arg.getModelId(), boxes);
            }
        } else {
            isCache = true;
        }
        record.setOriginalPath(arg.getPath());
        if (Boolean.TRUE.equals(arg.getCreateProcessedImage()) && record.getPath().equals(record.getProcessedPath())) {
            if (record.getObjects() != null) {
                byte[] image = fileAdapter.downloadWithoutSockets(tenantId, tenantAccount, arg.getPath());
                Map<String, String> productMap = getProductMap(tenantId, record.getObjects().stream().map(ObjectEntity::getObjectId).collect(Collectors.toList()));
                record.getObjects().forEach(v -> {
                    if (Strings.isNullOrEmpty(v.getObjectName())) {
                        v.setObjectName(productMap.get(v.getObjectId()));
                    }
                });
                String processedPath = createProcessedPath(tenantAccount, userId, image, record.getObjects() == null ? new ArrayList<>() : record.getObjects());
                record.setProcessedPath(processedPath);
                detectRecordDao.save(record);
            }
        }
        return AutoMapper.toDetectResult(record, isCache);
    }

    private List<CommonDetect.BoxDTO> rebuildBox(String key, List<CommonDetect.BoxDTO> boxes) {
        String util = ROW_UTIL_MAP.get(key);
        if (!Strings.isNullOrEmpty(util)) {
            RowFaceBusiness rowFaceBusiness = SpringContextHolder.getBean(util);
            if (rowFaceBusiness != null)
                return rowFaceBusiness.countRowFace(boxes);
        } else {
            logger.info("can`t map any row face util:{}", key);
        }
        return boxes;
    }

    private ModelPo insureModel(String modelId, String tenantId) throws AiProviderException {
        ModelPo model = oldModelDao.get(Integer.valueOf(tenantId), modelId);
        if (model == null && isMongoIdString(modelId)) {
            model = modelDao.query(modelId, Integer.valueOf(tenantId));
        }
        if (model == null) {
            throw new AiProviderException("600404", String.format("tenant:%s cant map any ai model.", tenantId));
        }
        return model;
    }

    private boolean isMongoIdString(String id) {
        if (id == null || id.length() > 24)
            return false;
        for (int i = 0; i < id.length(); i++) {
            char c = id.charAt(i);
            boolean t = (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F');
            if (!t)
                return false;
        }
        return true;
    }

    private Set<String> getAllNPathForSameFile(String ea, String userId, String path) throws AiProviderException {
        /*Set<String> relatedPaths = new HashSet<>();
        List<String> sameFiles = fileAdapter.getFileNPaths(ea,Integer.parseInt(userId), path);
        if(!CollectionUtils.isEmpty(sameFiles)){
            for(String filePah: sameFiles){
                relatedPaths.addAll(CommonUtil.queryRelatedPath(filePah));
            }
        }*/
        return CommonUtil.queryRelatedPath(path);
    }

    /**
     * 创建标注图片
     */
    private String createProcessedPath(String ea, Integer employeeId, byte[] imageStream, List<BoxDto> boxes, Map<String, ObjectMapPo> cache) throws AiProviderException {
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageStream));
            Graphics2D graphics = (Graphics2D) image.getGraphics();
            for (BoxDto box : boxes) {
                ObjectMapPo objectRelation = cache.get(box.getName());
                if (objectRelation == null) {
                    continue;
                }
                graphics.setColor(new Color(Integer.parseInt(objectRelation.getColor(), 16)));
                graphics.setStroke(new BasicStroke(10.0f));
                int top = box.getBox()[0].intValue();
                int left = box.getBox()[1].intValue();
                int bottom = box.getBox()[2].intValue();
                int right = box.getBox()[3].intValue();
                graphics.drawRoundRect(left, top, right - left, bottom - top, 50, 50);
                Font font = new Font("微软雅黑", Font.BOLD, (right - left) / 8);
                graphics.setFont(font);
                graphics.drawString(objectRelation.getName(), left, top - 10);
            }
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, CommonUtil.JPG, os);
            byte[] bytes = os.toByteArray();
            return fileAdapter.uploadImage(ea, employeeId, bytes);
        } catch (Exception ex) {
            logger.error("Draw picture box error.", ex);

            throw new AiProviderException("600514", ex.getMessage());
        }
    }

    public String getProductName(int ei, String key, Map<String, String> map) {
        if (!map.containsKey(key)) {
            PaasDataGet.Result result = paasDataProxy.get(ei, -10000, "ProductObj", key);
            if (result.getCode() == 0) {
                map.put(key, result.getData().getObjectData().getString("name"));
            } else
                return "";
        }
        return map.get(key);
    }

    public Map<String, String> getProductMap(int ei, List<String> productIds) throws AiProviderException {
        Map<String, String> nameMap = new HashMap<>();
        List<Map<String, Object>> products = productCache.querySku(ei, productIds);
        products.forEach(product -> nameMap.put((String) product.get("_id"), (String) product.get("name")));
        productIds.forEach(id -> nameMap.putIfAbsent(id, ""));
        return nameMap;
    }

    private String createProcessedPath(String ea, Integer employeeId, byte[] imageStream, List<ObjectEntity> boxes) throws AiProviderException {
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageStream));
            Graphics2D graphics = (Graphics2D) image.getGraphics();
            for (ObjectEntity box : boxes) {
                graphics.setColor(new Color(Integer.parseInt(box.getColor(), 16)));
                graphics.setStroke(new BasicStroke(10.0f));
                int top = box.getPosition().getY().intValue();
                int left = box.getPosition().getX().intValue();
                int bottom = top + box.getPosition().getH().intValue();
                int right = left + box.getPosition().getW().intValue();
                graphics.drawRoundRect(left, top, right - left, bottom - top, 50, 50);
                Font font = new Font("微软雅黑", Font.BOLD, (right - left) / 8);
                graphics.setFont(font);
                graphics.drawString(box.getObjectName(), left, top - 10);
            }
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(image, CommonUtil.JPG, os);
            byte[] bytes = os.toByteArray();
            return fileAdapter.uploadImage(ea, employeeId, bytes);
        } catch (Exception ex) {
            logger.error("Draw picture box error.", ex);

            throw new AiProviderException("600514", ex.getMessage());
        }
    }

    /**
     * 插入识别记录
     */
    private DetectRecordPo insertDetectRecord(Integer tenantId, String tenantAccount, Integer userId, String appId, String originalPath, String modelId, List<BoxDto> boxes, byte[] image) throws AiProviderException {
        Map<String, ObjectMapPo> objectMap = queryObjectMap(tenantId, modelId);

        String processedPath = createProcessedPath(tenantAccount, userId, image, boxes, objectMap);
        String path = fileAdapter.saveTempFile(tenantAccount, userId, originalPath);

        DetectRecordPo record = new DetectRecordPo(tenantId, modelId, path, originalPath, processedPath, AutoMapper.toObjectEntityList(appId, boxes, objectMap));
        detectRecordDao.save(record);
        return record;
    }

    private DetectRecordPo insertDetectRecordWithOutCreateProcessedImage(Integer tenantId, String tenantAccount, Integer userId, String appId, String originalPath, String modelId, List<BoxDto> boxes) {
        Map<String, ObjectMapPo> objectMap = queryObjectMap(tenantId, modelId);
        String path = fileAdapter.saveTempFile(tenantAccount, userId, originalPath);
        DetectRecordPo record = new DetectRecordPo(tenantId, modelId, path, originalPath, path, AutoMapper.toObjectEntityList(appId, boxes, objectMap));
        detectRecordDao.save(record);
        return record;
    }

    private DetectRecordPo insertDetectRecordWithNPath(Integer tenantId, String appId, String path, String modelId, List<BoxDto> boxes) {
        Map<String, ObjectMapPo> objectMap = queryObjectMap(tenantId, modelId);
        DetectRecordPo record = new DetectRecordPo(tenantId, modelId, path, path, path, AutoMapper.toObjectEntityList(appId, boxes, objectMap));
        detectRecordDao.save(record);
        return record;
    }

    private Map<String, ObjectMapPo> queryObjectMap(Integer tenantId, String modelId) {

        List<ObjectMapPo> objectMapList = oldObjectMapDao.query(tenantId, modelId);
        if (CollectionUtils.isEmpty(objectMapList))
            objectMapList = new ArrayList<>();
        objectMapList.addAll(objectMapDao.query(tenantId, modelId));

        Map<String, ObjectMapPo> objectMap = new HashMap<>();
        for (ObjectMapPo objectMapPo : objectMapList) {
            if (!objectMap.containsKey(objectMapPo.getKey())) {
                objectMap.put(objectMapPo.getKey(), objectMapPo);
            }
        }
        return objectMap;
    }
}