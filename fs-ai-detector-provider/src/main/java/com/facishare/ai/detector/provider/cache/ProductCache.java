package com.facishare.ai.detector.provider.cache;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataQuery;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component("productCache")
public class ProductCache implements InitializingBean {

    @Resource
    private PaasDataProxy paasDataProxy;

    private Cache<String, Map<String, Object>> SKU_CACHE;
    private Cache<String, Map<String, Object>> SPU_CACHE;
    private static Cache<String, Map<String, Object>> objectCache;
    private Cache<String, List<String>> SPU_SKU_RELATION_CACHE;

    private static final int SUPER_USER_ID = -10000;
    private static final String SKU_OBJ = "ProductObj";
    private static final String SPU_OBJ = "SPUObj";
    private static final String SPU_SKU_RELATION = "SPU_SKU_RELATION";
    private static final String SPU_ID_FIELD_KEY = "spu_id";
    private static final String ID_FIELD = "_id";
    private static final List<String> KEEP_FILED_NAME_LIST = Lists.newArrayList("_id", "name");

    public List<Map<String, Object>> querySpu(int tenantId, Collection<String> spuIds) throws AiProviderException {
        return queryObject(tenantId, SPU_CACHE, SPU_OBJ, spuIds);
    }

    public List<Map<String, Object>> querySku(int tenantId, Collection<String> skuIds) throws AiProviderException {
        return queryObject(tenantId, SKU_CACHE, SKU_OBJ, skuIds);
    }

    public List<Map<String, Object>> queryRelatedSku(int tenantId, Collection<String> spuIds) throws AiProviderException {
        List<Map<String, Object>> rst = new ArrayList<>();
        Set<String> missCacheIds = new HashSet<>();
        for (String id : spuIds) {
            String key = buildCacheKey(tenantId, SPU_SKU_RELATION, id);
            List<String> skuIds = SPU_SKU_RELATION_CACHE.getIfPresent(key);
            if (skuIds != null) {
                rst.addAll(querySku(tenantId, skuIds));
            } else {
                missCacheIds.add(id);
            }
        }
        if (!missCacheIds.isEmpty()) {
            PaasDataQuery.FilterDTO spuIdFilter = new PaasDataQuery.FilterDTO();
            spuIdFilter.setFieldName(SPU_ID_FIELD_KEY);
            spuIdFilter.setOperator("IN");
            spuIdFilter.setFieldValues(missCacheIds);

            PaasDataQuery.FilterDTO spuIdNotNullFilter = new PaasDataQuery.FilterDTO();
            spuIdNotNullFilter.setFieldName(SPU_ID_FIELD_KEY);
            spuIdNotNullFilter.setOperator("ISN");
            spuIdNotNullFilter.setFieldValues(Lists.newArrayList(""));

            List<JSONObject> skuList = queryData(tenantId, SKU_OBJ, spuIdFilter, spuIdNotNullFilter);
            Map<String, List<String>> relation = new HashMap<>();
            for (JSONObject object : skuList) {
                String spuId = object.getString(SPU_ID_FIELD_KEY);
                String id = object.getString("_id");
                String key = buildCacheKey(tenantId, SPU_SKU_RELATION, spuId);
                if (relation.containsKey(key)) {
                    relation.get(key).add(id);
                } else {
                    relation.put(key, Lists.newArrayList(id));
                }
            }
            SPU_SKU_RELATION_CACHE.putAll(relation);
            rst.addAll(skuList);
        }
        return rst;
    }

    private String buildCacheKey(int tenantId, String prefix, String id) {
        return String.format("FMCG.AI.SDK.%s.%s.%s", tenantId, prefix, id);
    }

    public List<Map<String, Object>> queryObjectData(int tenantId, String apiName, Collection<String> dataIds) throws AiProviderException {
        return queryObject(tenantId, objectCache, apiName, dataIds);
    }

    private List<Map<String, Object>> queryObject(int tenantId, Cache<String, Map<String, Object>> cache, String apiName, Collection<String> ids) throws AiProviderException {
        List<Map<String, Object>> rst = new ArrayList<>();
        Set<String> missCacheIds = new HashSet<>();
        for (String id : ids) {
            String key = buildCacheKey(tenantId, apiName, id);
            Map<String, Object> cacheItem = cache.getIfPresent(key);
            if (cacheItem != null) {
                rst.add(cacheItem);
            } else {
                missCacheIds.add(id);
            }
        }
        if (!missCacheIds.isEmpty()) {
            List<JSONObject> skuList = queryData(tenantId, apiName, missCacheIds);
            for (JSONObject sku : skuList) {
                String key = buildCacheKey(tenantId, apiName, sku.getString("_id"));
                cache.put(key, sku);
                rst.add(sku);
            }
        }
        return rst;
    }

    @Override
    public void afterPropertiesSet() {
        SKU_CACHE = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(20, TimeUnit.MINUTES)
                .build();
        SPU_CACHE = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(20, TimeUnit.MINUTES)
                .build();
        SPU_SKU_RELATION_CACHE = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(20, TimeUnit.MINUTES)
                .build();

        objectCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(20, TimeUnit.MINUTES)
                .build();
    }

    private List<JSONObject> queryData(int tenantId, String apiName, Collection<String> ids) throws AiProviderException {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }
        PaasDataQuery.FilterDTO idFilter = new PaasDataQuery.FilterDTO();
        idFilter.setFieldName(ID_FIELD);
        idFilter.setOperator("IN");
        idFilter.setFieldValues(Lists.newArrayList(ids));
        return queryData(tenantId, apiName, idFilter);
    }

    private List<JSONObject> queryData(int tenantId, String apiName, PaasDataQuery.FilterDTO... filters) throws AiProviderException {
        PaasDataQuery.Arg queryArg = new PaasDataQuery.Arg();
        queryArg.setObjectApiName(apiName);
        PaasDataQuery.QueryDTO query = new PaasDataQuery.QueryDTO();
        query.setLimit(200);
        query.setOffset(0);
        query.setFilters(Lists.newArrayList(filters));
        query.setOrders(Lists.newArrayList());
        queryArg.setQuery(query);
        PaasDataQuery.Result queryResult = paasDataProxy.query(tenantId, SUPER_USER_ID, queryArg);
        if (queryResult.getErrCode() != 0) {
            throw new AiProviderException(String.valueOf(queryResult.getErrCode()), queryResult.getErrMessage());
        }
        return queryResult.getResult().getDataList().stream().map(v -> {
            JSONObject newJ = new JSONObject();
            KEEP_FILED_NAME_LIST.forEach(k -> {
                Object value = v.get(k);
                if (value != null)
                    newJ.put(k, value);
            });
            return newJ;
        }).collect(Collectors.toList());
    }
}
