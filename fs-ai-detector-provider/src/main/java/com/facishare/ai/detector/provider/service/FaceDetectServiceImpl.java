package com.facishare.ai.detector.provider.service;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.ai.detector.api.dto.arg.AddFaceArg;
import com.facishare.ai.detector.api.dto.arg.FaceComparisionArg;
import com.facishare.ai.detector.api.dto.arg.FaceDetectArg;
import com.facishare.ai.detector.api.dto.result.AddFaceResult;
import com.facishare.ai.detector.api.dto.result.FaceComparisionResult;
import com.facishare.ai.detector.api.dto.result.FaceDetectResult;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.ai.detector.api.service.FaceDetectService;
import com.facishare.ai.detector.api.util.ConstantUtil;
import com.facishare.ai.detector.provider.adapter.SpringContextHolder;
import com.facishare.ai.detector.provider.adapter.service.abstraction.FaceDetectDetector;
import com.facishare.ai.detector.provider.adapter.service.face.model.FaceImage;
import com.facishare.ai.detector.provider.adapter.service.file.FileAdapter;
import com.facishare.ai.detector.provider.dao.abstraction.DetectCounterDao;
import com.facishare.ai.detector.provider.dao.abstraction.FaceInfoDao;
import com.facishare.ai.detector.provider.dao.po.FaceInfoPo;
import com.facishare.ai.detector.provider.util.GrayUtil;
import com.fmcg.framework.http.util.TimeLimitedExecutor;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 19-11-12  下午6:47
 * <p>
 * 选择层 当脸部识别的平台大于1时 可以在这个层面进行企业和服务的映射
 */
@Component(value = "faceDetectService")
public class FaceDetectServiceImpl implements FaceDetectService {

    public static String bean = "baiduFaceDetector";

    private static final Logger log = LoggerFactory.getLogger(FaceDetectServiceImpl.class);

    @Resource
    private FileAdapter fileAdapter;

    @Resource
    private FaceInfoDao faceInfoDao;

    @Resource
    private DetectCounterDao detectCounterDao;

    private static final long DOWNLOAD_TIME_OUT_LIMIT = 4000L;

    private static final AiProviderException IMAGE_DOWNLOAD_TIME_OUT_EXCEPTION = new AiProviderException("100500", "图片下载超时，请稍后再试。");



    private static final List<String> ERROR_CODE_BY_USE = Lists.newArrayList("222202", "222203", "222207", "222209", "223113", "223114", "223115", "223116", "223120", "223121"
            , "223122", "223123", "223124", "223125", "223126", "223127");

    @Override
    public AddFaceResult addFace(AddFaceArg arg) throws AiProviderException {
        log.info("add face .arg: {}", arg);
        long start = System.currentTimeMillis();
        String groupId = StringUtils.isNotEmpty(arg.getGroupId()) ? arg.getGroupId() : arg.getTenantAccount();
        String userId = arg.getUserId();
        FaceImage faceImage = new FaceImage();
        if (StringUtils.isNotEmpty(arg.getScene())) {
            faceImage.setFaceType(arg.getScene());
        } else {
            faceImage.setFaceType("LIVE");
            arg.setScene("LIVE");
        }
        faceImage.setLivenessControl("NORMAL");
        if (!Strings.isNullOrEmpty(arg.getFaceToken())) {
            faceImage.setImage(arg.getFaceToken());
            faceImage.setType("FACE_TOKEN");
        } else {
            faceImage.setQualityControl("HIGH");
            faceImage.setImage(Base64.encodeBase64String(TimeLimitedExecutor.create().execute(() -> fileAdapter.downloadWithoutSockets(arg.getTenantId(), arg.getTenantAccount(), arg.getImagePath()) , DOWNLOAD_TIME_OUT_LIMIT, IMAGE_DOWNLOAD_TIME_OUT_EXCEPTION)));
            faceImage.setType("BASE64");
        }
        log.info("add face  get image time:{},arg:{}", System.currentTimeMillis() - start, arg);
        AddFaceResult result = ((FaceDetectDetector) SpringContextHolder.getBean(bean)).addFace(groupId, userId, faceImage);
        log.info("add face time:{},arg:{}", System.currentTimeMillis() - start, arg);
        if (result.getErrorCode().equals("0")) {
            saveFaceInfo(result.getFaceId(), groupId, userId, arg.getImagePath(), arg.getScene());
        }
        log.info("arg:{},face add rst:{}", arg, result);
        return result;
    }

    @Override
    public FaceComparisionResult faceComparision(FaceComparisionArg arg) throws AiProviderException {
        log.info("faceComparison .arg: {}", arg);
        long start = System.currentTimeMillis();
        boolean isAllowFaceToken = GrayRelease.isAllow("fmcg", "allow_face_token_detect", arg.getTenantId());
        String groupId = StringUtils.isNotEmpty(arg.getGroupId()) ? arg.getGroupId() : arg.getTenantAccount();
        String userId = arg.getUserId();
        FaceInfoPo po = faceInfoDao.query(groupId, userId, arg.getBaseImagePath());
        FaceImage image1 = new FaceImage();
        FaceImage image2 = new FaceImage();
        List<FaceImage> faceImages = Lists.newArrayList(image2, image1);
        if (po == null || !isAllowFaceToken) {
            image1.setType("BASE64");
            image1.setImage(Base64.encodeBase64String(
                    TimeLimitedExecutor.create().execute(() -> fileAdapter.downloadWithoutSockets(arg.getTenantId(), arg.getTenantAccount(), arg.getBaseImagePath())
                            , DOWNLOAD_TIME_OUT_LIMIT, IMAGE_DOWNLOAD_TIME_OUT_EXCEPTION)));
            image1.setFaceType(arg.getScene());
        } else {
            log.info("token mode");
            image1.setType("FACE_TOKEN");
            image1.setImage(po.getFaceId());
            image1.setFaceType(po.getScene());
        }
        log.info("face compare get base image time:{},arg:{}", System.currentTimeMillis() - start, arg);
        image2.setType("BASE64");
        image2.setImage(Base64.encodeBase64String(
                TimeLimitedExecutor.create().execute(() -> fileAdapter.downloadWithoutSockets(arg.getTenantId(), arg.getTenantAccount(), arg.getDetectedImagePath())
                        , DOWNLOAD_TIME_OUT_LIMIT, IMAGE_DOWNLOAD_TIME_OUT_EXCEPTION)));
        image2.setFaceType(arg.getScene());
        image2.setLivenessControl("LOW");

        Map<String, Object> recaptureGray = GrayUtil.getMap("AI_FACE_RECAPTURE_MAP");
        if (recaptureGray != null && recaptureGray.containsKey(arg.getTenantId().toString())) {
            Map<String, Object> params = (Map<String, Object>) recaptureGray.get(arg.getTenantId().toString());
            long user = Long.parseLong(userId);
            boolean isPass = false;
            if (params.containsKey("max")) {
                int percent = (Integer) params.get("max");
                long userData = user % 100;

                if (userData <= percent) {
                    isPass = true;
                }
            }
            if (params.containsKey("userIds")) {
                List<Integer> userIds = (List<Integer>) (params.get("userIds"));
                if (userIds.contains(user)) {
                    isPass = true;
                }
            }
            if (isPass) {
                List<Integer> highList = (List<Integer>) recaptureGray.get("high");
                List<Integer> normalList = (List<Integer>) recaptureGray.get("normal");
                if (highList.contains(arg.getTenantId()))
                    image2.setLivenessControl("HIGH");
                else if (normalList.contains(arg.getTenantId()))
                    image2.setLivenessControl("NORMAL");
            }
        }

        log.info("face compare get detected image time:{},arg:{}", System.currentTimeMillis() - start, arg);
        FaceComparisionResult r = ((FaceDetectDetector) SpringContextHolder.getBean(bean)).faceComparison(groupId, userId, faceImages);
        log.info("face compare   time:{},arg:{}", System.currentTimeMillis() - start, arg);
        log.info("arg:{},face compare rst:{}", arg, r);
        if (r.getErrorCode().equals("0") || ERROR_CODE_BY_USE.contains(r.getErrorCode())) {
            detectCounterDao.success(arg.getTenantId(), ConstantUtil.FACE_COMPARISION);
            if (po == null && CollectionUtils.isNotEmpty(r.getFaceIds()) && isAllowFaceToken) {
                AddFaceArg addFaceArg = new AddFaceArg();
                addFaceArg.setFaceToken(r.getFaceIds().get(1));
                addFaceArg.setTenantId(arg.getTenantId());
                addFaceArg.setUserId(arg.getUserId());
                addFaceArg.setTenantAccount(arg.getTenantAccount());
                addFaceArg.setScene("LIVE");
                addFaceArg.setImagePath(arg.getBaseImagePath());
                addFaceArg.setGroupId(arg.getGroupId());
                addFace(addFaceArg);
            }
        } else {
            detectCounterDao.fail(arg.getTenantId(), ConstantUtil.FACE_COMPARISION);
        }

        return r;
    }

    private void saveFaceInfo(String faceId, String groupId, String userId, String path, String scene) {
        FaceInfoPo po = new FaceInfoPo();
        po.setFaceId(faceId);
        po.setGroupId(groupId);
        po.setUserId(userId);
        po.setPath(path);
        po.setScene(scene);
        faceInfoDao.save(po);
    }

    @Override
    public FaceDetectResult faceDetect(FaceDetectArg arg) throws AiProviderException {
        log.info("faceDetect .arg: {}", arg);
        FaceImage faceImage = new FaceImage();
        faceImage.setLivenessControl("LOW");
        faceImage.setImage(Base64.encodeBase64String(fileAdapter.downloadWithoutSockets(arg.getTenantId(), arg.getTenantAccount(), arg.getImagePath())));
        faceImage.setType("BASE64");
        FaceDetectResult result = ((FaceDetectDetector) SpringContextHolder.getBean(bean)).faceDetect(arg.getTenantAccount(), arg.getUserId(), faceImage);
        log.info("faceDetect .rst: {}", result);
        return result;
    }
}
