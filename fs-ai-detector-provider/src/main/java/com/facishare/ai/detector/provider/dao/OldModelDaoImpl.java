package com.facishare.ai.detector.provider.dao;


import com.facishare.ai.detector.provider.dao.po.ModelPo;
import com.fs.fmcg.sdk.ai.plat.AppEnum;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository(value = "oldModelDao")
public class OldModelDaoImpl {

    private static List<ModelPo> mockTable = new ArrayList<>();

    static {
        ModelPo jmlModel = new ModelPo();
        jmlModel.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        jmlModel.setTenantId(608158);
        jmlModel.setKey("jml_v1");
        jmlModel.setPlatform("baidu");
        jmlModel.setConfidence(0.8);
        mockTable.add(jmlModel);

        ModelPo jmlModel2 = new ModelPo();
        jmlModel2.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        jmlModel2.setTenantId(670624);
        jmlModel2.setKey("jml_v1");
        jmlModel2.setPlatform("baidu");
        jmlModel2.setConfidence(0.8);
        mockTable.add(jmlModel2);

        ModelPo jmlModel1 = new ModelPo();
        jmlModel1.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        jmlModel1.setTenantId(590103);
        jmlModel1.setKey("jml");
        jmlModel1.setPlatform("fs");
        jmlModel1.setConfidence(0.8);
        mockTable.add(jmlModel1);

//        ModelPo jmlModel2 = new ModelPo();
//        jmlModel2.setIdentity();
//        jmlModel2.setModelId("14e92e1b-5467-448e-8419-e550769c5b97");
//        jmlModel2.setTenantId(590081);
//        jmlModel2.setKey("jml");
//        jmlModel2.setPlatform("fs");
//        mockTable.add(jmlModel2);

        ModelPo jmlModel3 = new ModelPo();
        jmlModel3.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        jmlModel3.setTenantId(644282);
        jmlModel3.setKey("jml");
        jmlModel3.setPlatform("fs");
        jmlModel3.setConfidence(0.8);
        mockTable.add(jmlModel3);

        ModelPo jml112Model = new ModelPo();
        jml112Model.setIdentity("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        jml112Model.setTenantId(71578);
        jml112Model.setKey("jml_v1");
        jml112Model.setPlatform("baidu");
        jml112Model.setConfidence(0.8);
        mockTable.add(jml112Model);

        /*ModelPo gfModel1 = new ModelPo();
        gfModel1.setIdentity("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        gfModel1.setTenantId(71578);
        gfModel1.setKey("guanfang");
        gfModel1.setPlatform("alibaba");
        gfModel1.setModelCode("FenXiangXiaoKe");
        gfModel1.setConfidence(0.8);
        mockTable.add(gfModel1);*/


        ModelPo baiduRetail1 = new ModelPo();
        baiduRetail1.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        baiduRetail1.setTenantId(71578);
        baiduRetail1.setKey("drink");
        baiduRetail1.setPlatform("baidu_retail");
        baiduRetail1.setConfidence(0.8);
        mockTable.add(baiduRetail1);

        ModelPo pandaModel = new ModelPo();
        pandaModel.setIdentity("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        pandaModel.setTenantId(590103);
        pandaModel.setKey("guanfang");
        pandaModel.setPlatform("alibaba");
        pandaModel.setModelCode("FenXiangXiaoKe");
        pandaModel.setConfidence(0.8);
        mockTable.add(pandaModel);

        ModelPo henganModel = new ModelPo();
        henganModel.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        henganModel.setTenantId(590251);
        henganModel.setKey("hengan_v2");
        henganModel.setPlatform("baidu");
        henganModel.setConfidence(0.8);
        mockTable.add(henganModel);

        ModelPo baiduModel1 = new ModelPo();
        baiduModel1.setIdentity("14e92e1b-5467-448e-8417-e550769c5b91");
        baiduModel1.setTenantId(0);
        baiduModel1.setKey("recapture");
        baiduModel1.setPlatform("baidu_customized");
        baiduModel1.setConfidence(0.8);
        mockTable.add(baiduModel1);

        ModelPo recaptureModel = new ModelPo();
        recaptureModel.setIdentity("14e92e1b-5467-448e-8417-e550769c5b91");
        recaptureModel.setTenantId(99999);
        recaptureModel.setKey("recapture");
        recaptureModel.setPlatform("huawei");
        recaptureModel.setConfidence(0.8);
        mockTable.add(recaptureModel);

        ModelPo combination = new ModelPo();
        combination.setIdentity("14e92e1b-5467-448e-8417-e550769c5b91");
        combination.setTenantId(0);
        combination.setKey("recapture");
        combination.setPlatform("combination");
        combination.setConfidence(0.8);
        mockTable.add(combination);

        ModelPo cloudModel = new ModelPo();
        cloudModel.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        cloudModel.setTenantId(590081);
        cloudModel.setKey("drink");
        cloudModel.setPlatform("baidu_retail");
        cloudModel.setConfidence(0.8);
        mockTable.add(cloudModel);

        ModelPo jmlV1Model = new ModelPo();
        jmlV1Model.setIdentity("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        jmlV1Model.setTenantId(590081);
        jmlV1Model.setKey("jml_v1");
        jmlV1Model.setPlatform("baidu");
        jmlV1Model.setConfidence(0.3);
        mockTable.add(jmlV1Model);

        ModelPo jmlV2Model = new ModelPo();
        jmlV2Model.setIdentity("334d68fc-7ea5-4ee6-a4b0-5f8a6275f9b6");
        jmlV2Model.setTenantId(608158);
        jmlV2Model.setKey("jml_v1");
        jmlV2Model.setPlatform("baidu");
        jmlV2Model.setConfidence(0.8);
        mockTable.add(jmlV2Model);

        ModelPo guanfangModel = new ModelPo();
        guanfangModel.setIdentity("334d68fc-1234-4ee6-a4b0-5f8a6275f9b6");
        guanfangModel.setTenantId(590081);
        guanfangModel.setKey("guanfang");
        guanfangModel.setModelCode("FenXiangXiaoKe");
        guanfangModel.setPlatform("alibaba");
        guanfangModel.setConfidence(0.8);
        mockTable.add(guanfangModel);


        ModelPo langJingModel = new ModelPo();
        langJingModel.setIdentity("14e92e1b-5467-448e-8417-e550769c5b91");
        langJingModel.setTenantId(9999999);
        langJingModel.setKey("recapture");
        langJingModel.setPlatform("langjing");
        langJingModel.setConfidence(0.8);
        mockTable.add(langJingModel);

        ModelPo baiduModel = new ModelPo();
        baiduModel.setIdentity("334d68fc-1234-4567-78910-5f8a6275f9b6");
        baiduModel.setTenantId(0);
        baiduModel.setKey("recaptureV2");
        baiduModel.setPlatform("baidu");
        baiduModel.setConfidence(0.8);
        mockTable.add(baiduModel);

        ModelPo jmlxp = new ModelPo();
        jmlxp.setIdentity("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
        jmlxp.setTenantId(-1);
        jmlxp.setKey("jml_xp");
        jmlxp.setPlatform("baidu");
        jmlxp.setConfidence(0.7);
        mockTable.add(jmlxp);


        ModelPo jiajia = new ModelPo();
        jiajia.setIdentity("14e92e1b-5467-448e-8419-e550769c5b97");
        jiajia.setTenantId(683665);
        jiajia.setKey("drink");
        jiajia.setPlatform("baidu_retail");
        jiajia.setConfidence(0.8);
        mockTable.add(jiajia);


        //
        ModelPo guanfangBox = new ModelPo();
        guanfangBox.setIdentity("18dcb71a-46e6-4fb9-8ec2-f1b4d872d076");
        guanfangBox.setTenantId(472252);
        guanfangBox.setKey("guanfang_drink");
        guanfangBox.setPlatform("baidu");
        guanfangBox.setConfidence(0.8);
        mockTable.add(guanfangBox);


        ModelPo testM = new ModelPo();
        testM.setIdentity("jiegeniube");
        testM.setTenantId(-1);
        testM.setKey("redbull-others");
        testM.setPlatform("baidu");
        testM.setConfidence(0.8);
        testM.setTokenKey(AppEnum.BAIDU_TEMPLATE_ACCOUNT_EASYDL.value());
        mockTable.add(testM);

    }

    public ModelPo get(Integer tenantId, String id) {
        ModelPo model = mockTable
                .stream()
                .filter(f -> f.getTenantId().equals(tenantId) && f.getIdentity().equals(id))
                .findFirst()
                .orElse(null);

        if (model == null) {
            model = mockTable
                    .stream()
                    .filter(f -> f.getTenantId() == (-1) && f.getIdentity().equals(id))
                    .findFirst()
                    .orElse(null);
        }
        return model;
    }
}
