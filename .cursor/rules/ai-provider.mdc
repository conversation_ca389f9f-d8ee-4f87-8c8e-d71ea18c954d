---
description: 针对每个新增类方法的请求请参考下列规则
globs: 
---

针对于接口的文件，每个新的方法需要依据这个类已经实现的接口方法定义作为参考，例如如果这个类里的接口方法大多是arg和result的模式，那么新生成的方法需要按照这个格式进行分装，如果是多参数的格式，那么入参和返回值就根据需求里的进行添加不需要进行封装成arg.

下列提供参考的arg:[AddApplicationArg.java](mdc:fs-ai-detector-api/src/main/java/com/facishare/ai/detector/api/dto/arg/AddApplicationArg.java) ,result: [AddApplicationResult.java](mdc:fs-ai-detector-api/src/main/java/com/facishare/ai/detector/api/dto/result/AddApplicationResult.java) , 所有入参都必须有Integer tenantId .

针对每个dto 我们的id字段都是字符串
